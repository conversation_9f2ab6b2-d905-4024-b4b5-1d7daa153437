<template>
  <div class="index">

    <div class="content-box">
      <div class="send_bgbox">{{scene.sceneName}}</div>
      <div class="send_contentbox">
        <div style="width: 95%;margin:46px auto ;display: flex;" class="send-cont-box">
          <div>
            <div style="margin: auto;">
              <img :src="item1" alt="" style="width: 489px;height: 68px;margin-top: 45px;">
            </div>
            <div class="send-item1-box">
              <img :src="development.imgUrl" style="width: 415px;height: 270px;margin: auto;margin-top: 45px;" alt="">
            </div>
          </div>
          <div>
            <div style="margin: auto;">
              <img :src="item2" alt="" style="width: 489px;height: 68px;margin-top: 45px;">
            </div>
            <div class="send-item2-box">
              <div style="width: 420px;margin: auto;margin-top: 45px;">
                <table border="0" cellpadding="0" cellspacing="0" style=" width: 420px;" class="e_table" id="e_table">
                  <thead style="display:block;margin: auto;">
                    <tr style="background-color: #4b5e7a;color: #0ff;font-weight: 600;">
                      <td style="width: 48px;">类别</td>
                      <td style="width: 100px;">名称</td>
                      <td style="width: 160px;"> 功能介绍</td>
                      <td style="width: 48px;">数量</td>
                      <td style="width: 48px;">单位</td>
                      <td style="width: 48px;"> 单价</td>
                    </tr>
                  </thead>
                  <tbody style="display:block; width: 420px;margin: auto;height: 230px;overflow-y: scroll">
                    <tr v-for="(item) in selectDeviceTableList" :key="item.id" style="color: #0ff;">
                      <td style="width: 48px;">硬件</td>
                      <td style="width: 100px;">{{item.serviceName}}</td>
                      <td style="width: 160px;">{{item.serviceExplain}}</td>
                      <td style="width: 48px;"> {{item.serviceNumber==null ? '1' :
                                                item.serviceNumber}}</td>
                      <td style="width: 48px;"> {{item.serviceUnit}}</td>
                      <td style="width: 48px;"> {{item.servicePrice}}</td>
                    </tr>

                    <tr v-for="(item) in selectSoftwareTableList" :key="item.id" style="color: #0ff;">
                      <td style="width: 48px;">软件</td>
                      <td style="width: 100px;">{{item.serviceName}}</td>
                      <td style="width: 160px;">{{item.serviceExplain}}</td>
                      <td style="width: 48px;"> {{item.serviceNumber==null ? '1' :
                                                item.serviceNumber}}</td>
                      <td style="width: 48px;"> {{item.serviceUnit}}</td>
                      <td style="width: 48px;"> {{item.servicePrice}}</td>
                    </tr>

                    <tr v-for="(item) in selectIntegrateTableList" :key="item.id" style="color: #0ff;">
                      <td style="width: 48px;">集成服务</td>
                      <td style="width: 100px;">{{item.serviceName}}</td>
                      <td style="width: 160px;">{{item.serviceExplain}}</td>
                      <td style="width: 48px;"> {{item.serviceNumber==null ? '1' :
                                                item.serviceNumber}}</td>
                      <td style="width: 48px;"> {{item.serviceUnit}}</td>
                      <td style="width: 48px;"> {{item.servicePrice}}</td>
                    </tr>

                    <tr v-for="(item) in selectOtherTableList" :key="item.id" style="color: #0ff;">
                      <td style="width: 48px;">其它服务</td>
                      <td style="width: 100px;">{{item.serviceName}}</td>
                      <td style="width: 160px;">{{item.serviceExplain}}</td>
                      <td style="width: 48px;"> {{item.serviceNumber==null ? '1' :
                                                item.serviceNumber}}</td>
                      <td style="width: 48px;"> {{item.serviceUnit}}</td>
                      <td style="width: 48px;"> {{item.servicePrice}}</td>
                    </tr>
                  </tbody>

                </table>
              </div>

            </div>

          </div>

          <div style="">
            <div style="margin: auto;">
              <img :src="item3" alt="" style="width: 489px;height: 68px;margin-top: 45px;">
            </div>
            <div style="position: absolute;top: 80px;">
              <div style="display: flex;flex-wrap: wrap;height: 387px;overflow-y: auto;width: 101%;overflow-x: hidden;">
                <div v-for="(item,index) in qxCaseList" :key="index">
                  <div v-if="qxCaseList.length == 1" class="length1">
                    <div class="send-item3-box" style="margin-bottom: 10px;">
                      <div class="anli-bgitem">
                        <img :src="anliBgitem" alt="">
                        <div style="position: absolute;top: -3px;left: 44px;">
                          案例{{item.caseIndex}}</div>

                      </div>
                      <div style="height: 30px;line-height: 18px;display: flex;align-items: center;justify-content: center;">
                        {{item.caseName}}</div>
                    </div>
                    <div class="send-item3-item">
                      <div style="text-align: center;padding-top: 40px;">
                        <img :src="item.img" alt="" style="width: 445px;height: 162px;">
                        <div v-if="item.linkUrl!=null" style="height: 18px;width: 445px;float:left;margin-left:255px;margin-top:10px;margin-bottom:10px;">
                          <img @click="caseDetail(item)" :src="detailimg" style="width:25px; height: 18px;margin-right: 1px;float:left;" alt="">
                          <label @click="caseDetail(item)" style="font-size: 12px;line-height:20px; font-weight: 600;color: #0ff;width: 60px;margin: auto;float:left;cursor: pointer;">查看详情</label>
                        </div>
                      </div>
                      <div style="font-size: 12px;font-weight: 600;color: #0ff;width: 445px;margin: auto;text-align: left;padding: 10px 0;">
                        案例描述</div>
                      <div style="font-size: 12px;color: #fff;width: 445px;margin: auto;text-align: left;margin: auto;text-align: left;height: 40px; overflow-y: auto;">
                        {{item.caseExplain}}</div>
                      <div @click="download(item)" v-if="item.pdtArr!=null&&item.pdtArr.length>0" style="font-size: 12px;color: #fff;width: 445px;margin: auto;text-align: center;display: flex;justify-content: center;margin-top: 20px;">
                        <img :src="ppt" style="width:18px; height: 18px;margin-right: 20px;" alt="">
                        <img :src="undow" style="width:118px; height: 24px;" alt="">
                      </div>
                    </div>

                  </div>
                  <div v-if="qxCaseList.length >= 2" class="length2">

                    <div class="send-item3-box" style="margin-bottom: 10px;">
                      <div class="anli-bgitem">
                        <img :src="anliBgitem" alt="">
                        <div style="position: absolute;top: -1.8px;left: 46px;">
                          案例{{item.caseIndex}}</div>

                      </div>
                      <div style="height: 30px;line-height: 18px;display: flex;align-items: center;justify-content: center;">
                        {{item.caseName}}</div>
                    </div>
                    <div class="send-item3-item1" style="">
                      <div style="text-align: center;padding-top: 30px;">
                        <img :src="item.img" alt="" style="width: 221px;height: 98px;">
                        <div v-if="item.linkUrl!=null" style="height: 18px;width: 221px;float:left;margin-left:100px;margin-top:10px;margin-bottom:10px;">
                          <img @click="caseDetail(item)" :src="detailimg" style="width:25px; height: 18px;margin-right: 1px;float:left;" alt="">
                          <label @click="caseDetail(item)" style="font-size: 12px;line-height:20px; font-weight: 600;color: #0ff;width: 60px;margin: auto;float:left;cursor: pointer;">查看详情</label>
                        </div>
                      </div>
                      <div style="font-size: 12px;font-weight: 600;color: #0ff;width: 221px;margin: auto;text-align: left;padding: 10px 0;">
                        案例描述</div>
                      <div style="font-size: 12px;color: #fff;width: 221px;margin: auto;text-align: left;height: 81px; overflow-y: auto;">
                        {{item.caseExplain}}</div>
                      <div @click="download(item)" v-if="item.pdtArr!=null&&item.pdtArr.length>0" style="font-size: 12px;color: #fff;width: 221px;margin: 20px auto;text-align: center;display: flex;justify-content: center;">
                        <img :src="ppt" style="width:18px; height: 18px;margin-right: 20px;" alt="">
                        <img :src="undow" style="width:118px; height: 24px;" alt="">
                      </div>
                    </div>

                  </div>

                </div>
              </div>

            </div>
          </div>
        </div>
        <div class="send_content-t">
          <div style="color:#0ff">支撑能力</div>
          <div>
            <div style="display: flex;width: 240px;margin: 0 93px 0 69px;overflow-x: auto;overflow-y: hidden;">
              <div style="position: relative;width: 25%;margin-right: 40px;" v-for="(item,index) in developmentCityList" :key="index">
                <img :src="map" alt="" style="width: 33px;height: 27px;">
                <div style="font-size: 12px;position: absolute;color: #0ff;width: 89px;text-align: center;left: -20px;">
                  {{item.label}}</div>
                <img :src="yuan" alt="" style="width:49px;height: 15px;">
              </div>

            </div>
            <div style="display: flex;font-size: 12px;width: 100px;color: #ABC2FF;margin: 0px 0px  0px 5px;text-align: center;margin: 0 auto;align-items: center;">
              <div class="dian-box"></div>
              <div>可支撑地市</div>
            </div>
          </div>
          <div class="kezhichi-box"> {{development.achieveNumber}} </div>
          <div class="dian-box"></div>
          <div style="font-size: 12px;width:55px;color: #ABC2FF;margin: 0px 0px  0px 5px;text-align: center;">
            可支撑人数
          </div>
          <div class="fengexian"></div>
          <div class="kezhichi-box anli-box"> {{development.number}} </div>
          <div class="dian-box"></div>
          <div style="font-size: 12px;width:55px;color: #ABC2FF;margin: 0px 0px  0px 5px;text-align: center;">
            全国案例数
          </div>
          <div class="fengexian"></div>
          <div style="font-size: 12px;">
            <div style="display: flex;align-items: center;">
              <img style="width: 30px;height: 30px;" :src="marshalling" alt="">
              <div style="color: #0ff;margin: 0 15px;">友情链接</div>
              <div class="link-box">
                <div v-for="(item,index)  in qxLinkList" :key="index">
                  <div style="padding-right:20px;color: #fff;">{{item.linkName}}</div>
                  <div @click=" linkUrl(item.linkUrl) " style="color: #fff;">{{item.linkUrl}}</div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import item1 from "../../../assets/images/partners/item-1.png";
import item2 from "../../../assets/images/partners/item-2.png";
import item3 from "../../../assets/images/partners/item-3.png";
import ppt from "../../../assets/images/partners/ppt.png";
import undow from "../../../assets/images/partners/undow.png";
import map from "../../../assets/images/partners/map.png";
import yuan from "../../../assets/images/partners/yuan.png";
import marshalling from "../../../assets/images/partners/marshalling.png";
import comecart from "../../../assets/images/partners/comecart.png";
import anliBgitem from "../../../assets/images/partners/anli-bgitem.png";
import detailimg from "../../../assets/images/partners/detail.png";
import colseImg from "../../../assets/images/close.png";


import { getProvince } from "../../../utils/province";
import { getDevelopmentDetail } from "@/api/system/development";
export default {
  components: {
  },
  data() {
    return {
      item1, item2, item3, ppt, undow, map, yuan, marshalling, comecart, anliBgitem, detailimg, colseImg,
      name: null,
      optionsProvince: [],
      selectDeviceTableList: [],//表格数据
      selectSoftwareTableList: [],//软件数据
      selectIntegrateTableList: [],//集成服务
      selectOtherTableList: [],//集成服务
      qxCaseList: [],//案例
      qxLinkList: [],//链接
      developmentCityList: [],//城市的列表
      cityName: "",
      scene: {},
      development: {},
      industryCode: "",
      developmentId: null,
    };
  },
  created() {
    this.developmentId = this.$route.params && this.$route.params.developmentId;
    this.setProvince();
    this.Move();
    if (this.developmentId != null)
      this.getDetailList(this.developmentId);
  },
  mounted() {

  },
  methods: {
    //关闭全景
    panoramaClose() {
      this.iframeUr = "";
      document.body.removeChild(this.divSFYX)
      this.Move();
      this.getPanorama();
    },
    //停止页面滚动
    stopMove() {
      let m = function (e) { e.preventDefault(); };
      document.documentElement.style.overflow = 'hidden';
      document.documentElement.scrollTop = 0;
      document.addEventListener("touchmove", m, { passive: false });//禁止页面滑动
    },
    //开启页面滚动
    Move() {
      let m = function (e) { e.preventDefault(); };
      document.documentElement.style.overflow = '';//出现滚动条
      document.removeEventListener("touchmove", m, { passive: true });
    },
    //item.linkUrl
    //type=0显示审核的数据type=1显示不需要审核的数据这样
    caseDetail(item) {
      let that = this;
      if (item == null || item.linkUrl == null || item.linkUrl == "") return;

      window.panoramaClose = (data) => {
        // 使用_this可以继续调用vue中的属性和方法，也可以完成绑定事件
        that.Move();
        document.body.removeChild(this.divSFYX)
      };
      let state = this.development.state == 0 ? 0 : 1;
      this.iframeUrl = item.linkUrl + '?type=' + state;
      that.divSFYX = window.document.createElement("div");
      let html = " <div style='width: 100vw;overflow: hidden;height: 100vh;position: absolute;top: 0;left: 0;z-index: 1001;'>" +
        "<img src='" + colseImg + "' onclick='panoramaClose()'  style='  right: 30px;top: 15px;z-index: 2002; position: absolute;width: 73px;height: 24px;cursor: pointer;' />" +
        "<iframe style='width: 100%;padding: 0;border: 0;margin: 0;height: 100%;overflow: hidden;' scrolling='no' src='" + this.iframeUrl + "' class='panorama-ifram-item'></iframe></div>"
      that.divSFYX.innerHTML = html;
      document.body.appendChild(that.divSFYX)
      this.stopMove();
    },


    download(item) {
      item.pdtArr.forEach(i => {
        window.open(i, '_blank');
      })
    },
    linkUrl(url) {
      window.open(url);
    },
    getDetailList(id) {
      let that = this;
      this.clearList();
      getDevelopmentDetail(id).then(res => {
        if (res.code === 200) {
          this.scene = res.data.scene;
          this.serviceList = res.data.serviceList;
          this.serviceList.forEach(row => {
            if (row.serviceType === 0) {
              this.selectDeviceTableList.push(row);
            } else if (row.serviceType === 1) {
              this.selectSoftwareTableList.push(row);
            } else if (row.serviceType === 2) {
              this.selectIntegrateTableList.push(row);
            } else if (row.serviceType === 3) {
              this.selectOtherTableList.push(row);
            } else if (row.serviceType === 4) {
              this.qxCaseList.push(row);
            }
          });
          this.qxLinkList = res.data.linkList;
          this.qxCaseList = res.data.caseList;
          this.developmentCityList = res.data.developmentCityList;
          this.development = res.data.development;

          let pdtArr = []
          this.qxCaseList.forEach(item => {
            this.$set(item, 'caseUrl', JSON.parse(item.caseUrl));
            item.caseUrl.forEach(i => {
              i.type = i.url.substring(i.url.length - 3)
              if (i.type == 'png' || i.type == 'jpg') item.img = i.url
              if (i.type == 'mp4') item.mp4 = i.url
              if (i.type == 'pdf' || i.type == 'txt' || i.type == 'ppt') {
                item.pdtname = i.name
                pdtArr.push(i.url)
              }
              item.pdtArr = pdtArr
            })
          });

          this.industryCode = res.data.qxIndustry.industryCode;

          //显示市级城市
          if (this.developmentCityList != null && this.developmentCityList.length === 340) {
            this.developmentCityList = [{ label: '全国' }]
          } else {
            this.developmentCityList.forEach(row => {
              this.optionsProvince.forEach(item => {
                if (row.provinceId == item.value) {
                  item.children.forEach(child => {
                    if (row.cityId == child.value) {
                      row.label = child.label;
                    }
                  });
                }
              });
            });
          }
          //显示查询详情图片
          this.previewPage = true;
        }
      });
    },
    clearList() {
      this.serviceList = [];
      this.selectDeviceTableList = [];//表格数据
      this.selectSoftwareTableList = [];//软件数据
      this.selectIntegrateTableList = [];//集成服务
      this.selectOtherTableList = [];//集成服务
      this.qxCaseList = [];
      this.qxLinkList = [];
    },

    setProvince() {
      this.optionsProvince = getProvince();
    }
  }
};
</script>

<style lang="scss" scoped>
.index {
  width: 800px; /* 视口宽度 */
  height: 100vh; /* 视口高度 */
  position: relative; /* 隐藏自身滚动条 */
  // position:fixed;
  // top: 84px;
  // left: 211px;
  font-family: FZLTXHK--GBK1-0, FZLTXHK--GBK1;
}

.content-box {
  position: relative;
  background-color: rgba(0, 0, 0);
  width: 1920px;
  height: 100%;
  overflow-x: scroll;
  
  /* 定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 5px !important;
    height: 5px;
    background-color: rgba(0, 0, 0, 0);
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
    border-radius: 5px;
    background-color: rgba(240, 240, 240, 0.5);
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
    width: 5px !important;
    height: 5px !important;
    background-color: #fff;
  }
}

.content-box > div {
  cursor: pointer;
}

.cart-modoul {
  position: relative;;
  top: 36%;
  left: 36%;
  width: 600px;
  height: 280px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 5px solid #5a96ff;

  text-align: center;
}

.cart-modoul img {
  width: 72px;
  height: 72px;

  padding-top: 76px;
  padding-bottom: 24px;
  text-align: center;
}

.e_table,
.e_table tr,
.e_table td,
.e_table tr td:last-child {
  font-size: 12px;
  line-height: 22px;
  padding: 5px 0;
}

.e_table {
  width: 100%;
  /* min-height: 100%; */
}

.e_table tr,
.e_table td,
.e_table tr td:last-child {
  /* padding-left: 20px;] */
  text-align: center;
  min-width: 60px;
}

.e_table tr:nth-child(odd) {
  background: #162332;
}

.e_table tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.2);
}

.e_table tbody {
  display: block;
}

[v-cloak] {
  display: none !important;
}

.send_bgbox {
  color: #fff;
  position: absolute;
  top: 38px;
  left: 20vw;
  background: url(../../../assets/images/partners/send_bg.png) no-repeat
    center center;
  width: 926px;
  height: 131px;
  background-size: 100%;
  text-align: center;
  line-height: 131px;
  font-size: 40px;
  font-weight: 600;
}

.send_contentbox {
  position: absolute;
  top: 220px;
  left: 3vw;
  width: 1590px;
  height: 751px;
  background: url(../../../assets/images/partners/send_content_bg.png)
    no-repeat center center;
  background-size: 100%;
}

.send_content-t {
  /* margin: 41rem auto 46rem auto; */
  position: absolute;
  top: 500px;
  left: 50px;
  width: 1481px;
  height: 105px;

  background: url(../../../assets/images/partners/send_title.png) no-repeat
    center center;
  background-size: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px 0 27px;
  box-sizing: border-box;
}

.kezhichi-box {
  width: 76px;
  height: 76px;
  background: url(../../../assets/images/partners/kechizhi.png) no-repeat
    center center;
  background-size: 100%;
  margin-right: 17px;
  text-align: center;
  line-height: 76px;
  font-size: 14px;
  color: #fff;
}

.dian-box {
  width: 7px;
  height: 7px;
  background: url(../../../assets/images/partners/dian.png) no-repeat center
    center;
  background-size: 100%;
}

.fengexian {
  width: 1px;
  height: 79px;
  margin: 0 58px;
  background: url(../../../assets/images/partners/fengexian.png) no-repeat
    center center;
  background-size: 100%;
}

.anli-box {
  background: url(../../../assets/images/partners/anlishu.png) no-repeat
    center center;
  background-size: 100%;
}

.send-cont-box > div {
  display: flex;
  position: relative;
  width: 576px;

  justify-content: space-between;
  text-align: center;
  margin: auto;
}

.link-box {
  width: 428px;
  height: 59px;
  background: linear-gradient(
    360deg,
    rgba(0, 140, 205, 0.2) 0%,
    rgba(0, 128, 196, 0.16) 20%,
    rgba(0, 116, 186, 0.11) 56%,
    rgba(0, 113, 184, 0.1) 100%
  );
  opacity: 0.6;
  border: 1px solid #0ff;
  overflow-y: auto;
  text-align: center;
  line-height: 28px;
}

.send-item1-box {
  position: absolute;
  top: 78px;
  background: url(../../../assets/images/partners/item-1-bg.png) no-repeat
    center center;
  background-size: 100%;
  width: 476px;
  height: 363px;
}

.send-item2-box {
  position: absolute;
  top: 78px;
  background: url(../../../assets/images/partners/item-2-bg.png) no-repeat
    center center;
  background-size: 100%;
  width: 476px;
  height: 363px;
}

.send-item3-box {
  /*
        background: url(../imgs/partners/item3-bg.png) no-repeat center center;
        background-size: 100%; */
  width: 219px;
  font-size: 12px;
  /* background: #435573; */
  /* height: 24px;] */
  /* line-height: 24px;] */
  margin: auto;
  color: #fff;
}

.send-item3-item {
  background: url(../../../assets/images/partners/item-3-bg.png) no-repeat
    center center;
  background-size: 100%;
  width: 485px;
  height: 327px;
  margin-top: -30px;
  /* overflow-y: auto; */
}

.send-item3-item1 {
  background: url(../../../assets/images/partners/lenth2.png) no-repeat
    center center;
  background-size: 100%;
  width: 278px;
  height: 327px;
  width: 268px;
  height: 327px;
  margin-right: 20px;
  margin-top: 10px;
  /* overflow-y: auto; */
}

.send-item3-item2 {
  background: url(../../../assets/images/partners/lenth3.png) no-repeat
    center center;
  background-size: 100%;
  width: 175px;
  height: 327px;
  margin: auto;
  /* overflow-y: auto; */
}

.length2 {
  margin-top: 20px;
}

.length1 {
  margin-top: 5px;
}

.length2 .send-item3-item1:nth-child(odd) {
  margin-right: 0;
}

.anli-bgitem {
  /* background: url(../imgs/partners/anli-bgitem.png) no-repeat center center;
        background-size: 100%; */
  width: 116px;
  height: 29px;
  text-align: right;
  padding-right: 20px;
  margin: auto;
  position: relative;
}

.avatar-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409eff;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>