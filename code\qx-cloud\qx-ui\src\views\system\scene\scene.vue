<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业" prop="industryId">
        <el-select v-model="queryParams.industryId" placeholder="请选择行业" size="small" filterable clearable @clear="handleIndustryClear">
          <el-option v-for="item in industryList" :key="item.id" :label="item.industryName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="场景名称" prop="sceneName">
        <el-input v-model="queryParams.sceneName" placeholder="请输入场景名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="场景编码" prop="sceneCode">
        <el-input v-model="queryParams.sceneCode" placeholder="请输入场景编号" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:scene:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:scene:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:scene:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:scene:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sceneList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!--       <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="行业场景名称" align="center" prop="industryName" >
         <template #default="scope">
            {{ scope.row.industryName +'/'+ scope.row.paramName}}
         </template>
        </el-table-column>
      <el-table-column label="场景名称" align="center" prop="sceneName" />
      <el-table-column label="场景编码" align="center" prop="sceneCode" />
      <el-table-column label="场景说明" align="center" prop="sceneExplain">
        <template #default="scope">
          <el-tooltip :content="scope.row.sceneExplain" raw-content placement="top-start" v-if="scope.row.sceneExplain">
            <span v-if="scope.row.sceneExplain && scope.row.sceneExplain.length <= 30">
              {{ scope.row.sceneExplain }}
            </span>
            <span v-if="scope.row.sceneExplain && scope.row.sceneExplain.length > 30">
              {{ scope.row.sceneExplain.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.sceneExplain== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="排序号" align="center" prop="sceneOrder" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:scene:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:scene:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    <!-- 添加或修改场景对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="场景名称" prop="sceneName">
              <el-input v-model="form.sceneName" maxlength="50" show-word-limit placeholder="请输入场景名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="场景编码" prop="sceneCode">
              <el-input v-model="form.sceneCode" maxlength="50" show-word-limit placeholder="请输入场景编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="行业场景" prop="industryId">
              <el-cascader v-model="form.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" filterable clearable @change="queryParamsHandleChange" @clear="handleClear">
              </el-cascader>
              <!-- <el-select v-model="form.industryId" placeholder="请选择行业" onchange="setIndustrycode()">
                <el-option v-for="item in industryList" :key="item.id" :label="item.industryName" :value="item.id"></el-option>
              </el-select> -->
              <br />
              (一级场景选择行业,二级场景选择父场景)
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序号" prop="sceneOrder">
              <el-input-number v-model="form.sceneOrder" :min="0" :max="999999999" label="请输入排序号"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
<!--          <el-col :span="8">-->
<!--            <el-form-item label="场景背景图" prop="sceneUrl">-->
<!--              <ImageUpload @input="setInput" :value="form.sceneUrl" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="8">
            <el-form-item label="场景全景图" prop="panoramaUrl">
              <PanoramaUpload @input="setPanoramaUrlInput" :value="form.panoramaUrl" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="场景封面图" prop="coverUrl">
              <ImageUpload @input="setCover" :value="form.coverUrl" />
            </el-form-item>
          </el-col>
        </el-row>

<!--        <el-row>-->
<!--          <el-col :span="16">-->
<!--            <el-form-item label="分享视频" prop="shareVedio">-->
<!--              <VideoUpload @input="setShareVedio" tipContent="(备注:上传mp4文件,文件最大500M)" accept=".mp4" :fileSize="500" :value="form.shareVedio" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->

        <el-row>
          <el-col :span="18">
            <el-form-item label="场景说明" prop="sceneExplain">
              <el-input type="textarea" :rows="3" v-model="form.sceneExplain" maxlength="200" show-word-limit placeholder="请输入场景说明" />
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listScene,
  listSceneV2,
  getScene,
  delScene,
  addScene,
  updateScene,
  exportScene,
  checkScene,
} from "@/api/system/scene";

import PanoramaUpload from "../../../components/PanoramaUpload";
import ImageUpload from "../../../components/ImageUpload";
import VideoUpload from "../../../components/ImageUpload/ImageUpload.vue";
import { listIndustry } from "@/api/system/industry";

export default {
  name: "Scene",
  components: {
    ImageUpload,
    VideoUpload,
    PanoramaUpload,
  },
  data() {
    return {
      options: [],
      // 遮罩层
      loadingPanorama: null,
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 场景表格数据
      sceneList: [],
      tagTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sceneName: null,
        industryId: null,
        sceneExplain: null,
        sceneCode: null,
        trgCode: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sceneName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        sceneCode: [
          { required: true, message: "编码不能为空", trigger: "blur" },
        ],
        industryId: [
          { required: true, message: "所属行业不能为空", trigger: "blur" },
        ],
      },
      //行业列表
      industryList: [],
      sceneListVo: [],
      options:[]
    };
  },
  created() {
    this.getList();
    listIndustry().then((response) => {
      this.industryList = response.rows;
    });

    this.getIndustryScene();

    this.getDicts("tag_type").then((response) => {
      this.tagTypeList = response.data;
    });
  },
  methods: {
    getIndustryScene() {
      listIndustry().then((response) => {
        this.industryList = response.rows;
        listScene().then((response) => {
          this.sceneListVo = response.rows;
          this.setOption();
        });
      });
    },
    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.form.industryId = value[0];
      }

      if (value != null || value.length > 1) {
        this.form.paramId = value[1];
      }
    },
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach((element) => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: [],
        };
        this.sceneListVo.forEach((row) => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName,
            });
          }
        });
        this.options.push(item);
      });
    },

    setIndustrycode() {
      this.industryList.forEach((row) => {
        if (this.form.industryId == row.id) {
          this.form.industryCode = row.industryCode;
        }
      });
    },
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    setPanoramaUrlInput(value) {
      this.$set(this.form, "panoramaUrl", value);
      this.form.panoramaTime = new Date().getTime();
    },
    setInput(value) {
      this.$set(this.form, "sceneUrl", value);
    },
    setCover(value) {
      this.$set(this.form, "coverUrl", value);
    },
    setShareImg(value) {
      this.$set(this.form, "shareImg", value);
    },
    setShareVedio(value) {
      console.log(value);
      this.$set(this.form, "shareVedio", value);
    },

    /** 查询场景列表 */
    getList() {
      this.loading = true;
      listSceneV2(this.queryParams).then((response) => {
        this.sceneList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sceneName: null,
        industryId: null,
        sceneExplain: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        sceneUrl: null,
        sceneCode: null,
        sceneOrder: null,
        shareVedio: null,
        shareImg: null,
        industryCode: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加场景";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getScene(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改场景";

        if (this.form.industryId && this.form.paramId) {
          this.form.sceneIds = [this.form.industryId, this.form.paramId];
        } else if (this.form.industryId) {
          this.form.sceneIds = [this.form.industryId];
        } else {
          this.form.sceneIds = []; // 确保始终是数组
        }

        console.log("sdfsdf", this.form.sceneIds);
        this.industryList.forEach((row) => {
          if (this.form.industryId == row.id) {
            this.form.industryCode = row.industryCode;
          }
        });

        this.options = this.options.map((option) => {
          return {
            ...option,
            children: option.children.map((child) => {
              return {
                ...child,
                disabled: child.value === this.form.id,
              };
            }),
          };
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.loadingPanorama = this.$loading({
            lock: true,
            text: "数据上传并解析图片中...",
            background: "rgba(0, 0, 0, 0.7)",
          });
          if (this.form.id != null) {
            updateScene(this.form)
              .then((response) => {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
                this.getIndustryScene();
                this.loadingPanorama.close();
              })
              .catch((r) => {
                if (this.loadingPanorama != null) this.loadingPanorama.close();
              });
          } else {
            addScene(this.form)
              .then((response) => {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
                this.getIndustryScene();
                this.loadingPanorama.close();
              })
              .catch((r) => {
                if (this.loadingPanorama != null) this.loadingPanorama.close();
              });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.sceneList
        .filter((item) => ids.includes(item.id))
        .map((item) => item.sceneCode)
        .join(", ");

      this.$confirm('是否确认删除场景编号为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return checkScene(ids);
        })
        .then((response) => {
          return delScene(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null, // Add ids if any are selected
      };
      let msg = "";
      if (that.ids.length > 0) {
        msg = "是否确认导出所筛选或选中的场景数据";
      } else {
        msg = "是否确认导出所有的场景数据";
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          that.download(
            "system/scene/export",
            exportParams,
            `scene_${new Date().getTime()}.xlsx`
          );
        })
        .catch({});
    },
  },
};
</script>
