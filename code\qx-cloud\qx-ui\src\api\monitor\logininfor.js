import request from '@/utils/request'

// 查询登录日志列表
export function list(query) {
  return request({
    url: '/system/monitor/logininfor/list',
    method: 'get',
    params: query
  })
}

// 删除登录日志
export function delLogininfor(infoId) {
  return request({
    url: '/system/monitor/logininfor/' + infoId,
    method: 'delete'
  })
}

// 清空登录日志
export function cleanLogininfor() {
  return request({
    url: '/system/monitor/logininfor/clean',
    method: 'delete'
  })
}

// 导出登录日志
export function exportLogininfor(query) {
  return request({
    url: '/system/monitor/logininfor/export',
    method: 'get',
    params: query
  })
}



// 查询登录日志列表
export function countList(query) {
  return request({
    url: '/system/monitor/logininfor/countlist',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 15
  })
}

// 导出登录日志
export function exportLogininforCount(query) {
  return request({
    url: '/system/monitor/logininfor/countexport',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 15
  })
}




// 查询登录日志时间列表
export function timeList(query) {
  return request({
    url: '/system/monitor/logininfor/timelist',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 15
  })
}

// 导出登录日志时间
export function exportLogininforTime(query) {
  return request({
    url: '/system/monitor/logininfor/timeexport',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 15
  })
}