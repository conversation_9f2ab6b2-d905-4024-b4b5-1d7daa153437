<template>
  <div>
    <!-- 添加或修改用户对话框 -->
    <el-dialog title="导出数据-4A管控验证码" :visible.sync="open" @close="cancelCode()" width="550px" :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="选择审批人" prop="approver">
          <el-select v-model="form.approver" style="width:100%" placeholder="请选择审批人">
            <el-option key="2323" label="(王柳,13810417302)" value="2323"></el-option>
            <el-option v-for="item in approverList" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="申请原因" prop="reason">
          <el-input v-model="form.reason" placeholder="请输入申请原因" maxlength="100"   show-word-limit/>
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input v-model="form.code" style="width:218px" placeholder="请输入验证码" maxlength="10" />
          <el-button style="margin-left:20px;width:140px" type="primary" :disabled="disabled" @click="getCode">{{btnStr}}</el-button>
        </el-form-item>
        <el-form-item label="申请批号" v-show="disabled">
          <label>{{form.requestID}}</label>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancelCode">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getApprover, getTreasuryCode, sendTreasuryCode, cancelSmsCode } from "@/api/treasury";
import { Loading } from "element-ui";
export default {
  name: "MySelect",
  props: {
    functionCode: {
      type: String,
      default: "600001"
    },
    operationCode: {
      type: String,
      default: "1001"
    }
  },
  data() {
    var checkCode = (rule, value, callback) => {
      if (this.isGetCode) {
        if (this.form.code == null || this.form.code == "") return callback(new Error("验证码不能为空"));
      } else {
        return callback(new Error("未获取验证码"));
      }
      return callback();
    };
    var checkAccountState = (rule, value, callback) => {
      if (this.accountState === 1) {
        return callback(new Error(this.accountMsg));
      }
      return callback();
    };
    return {
      open: false,
      form: { code: "" },
      disabled: false,
      isGetCode: false,
      btnStr: "获取验证码",
      timer: null,
      timerNum: 120,
      approverList: [],
      form: {
        approver: null,
        code: null,
        uuid: null,
        reason: null
      },
      rules: {
        approver: [{ required: true, message: "审批人不能为空", trigger: "blur" }],
        reason: [
          { required: true, message: "申请原因不能为空", trigger: "blur" },
          {
            pattern: /^[\u4e00-\u9fff0\w,。.]+$/,
            message: "申请原因格式不正确,只能是汉字、大小写、数字、特殊字符_,，。.",
            trigger: "blur"
          }
        ],
        code: [{ required: true, validator: checkCode, trigger: "blur" }]
      }
    };
  },
  created() {},
  methods: {
    getTreasuryApprover() {
      this.open = true;
      this.disabled = false;
      this.isGetCode = false;
      this.btnStr = "获取验证码";
      clearInterval(this.timer);
      let that = this;
      getApprover({ functionCode: that.functionCode, operationCode: that.operationCode })
        .then(response => {
          if (response.msg !== "-1") {
            that.approverList = [];
            if (response.data != null && response.data.length > 0) {
              response.data.forEach((element, index) => {
                if (element == null || element == "") {
                  that.$message.error("获取审批人列表失败");
                } else {
                  let items = element.split("G|T");
                  that.approverList.push({ value: items[0], name: "(" + items[1] + "," + items[2] + ")" });
                  if (index == 0) that.form.approver = items[0];
                }
              });
            } else {
              that.$message.error("获取审批人列表失败");
            }
            console.log(response.msg);
          }
        })
        .catch(r => {
          that.$message.error("获取审批人列表失败");
        });
    },
    getCode() {
      let that = this;
      this.$refs["form"].validateField("approver", Error => {
        if (!Error) {
          that.disabled = true;
          getTreasuryCode({
            approver: that.form.approver,
            functionCode: that.functionCode,
            operationCode: that.operationCode,
            reason: that.form.reason
          }).then(res => {
            if (res.code === 200) {
              that.$message.success("获取验证码成功");
              that.isGetCode = true;
              that.form.requestID = res.data.requestID;
              that.timerNum = 120;
              this.timer = setInterval(() => {
                if (that.timerNum <= 0) {
                  that.btnStr = "获取验证码";
                  that.disabled = false;
                  clearInterval(this.timer);
                } else {
                  that.btnStr = "重新获取(" + that.timerNum + ")";
                  that.timerNum--;
                }
              }, 1000);
            } else {
              this.$refs.form.validateField("username");
              that.disabled = false;
            }
          });
        }
      });
    },
    submitForm() {
      let that = this;
      this.$refs["form"].validate(valid => {
        if (valid) {
          sendTreasuryCode(this.form).then(res => {
            if (res.code === 200) {
              if (res.msg == 0) {
                that.$message.success("金库验证成功！");
                that.reset();
                that.$parent.handleExport();
              } else if (res.msg == -1) {
                that.$message.error("金库验证错误三次！请重新申请");
                that.reset();
              }
            }
          });
        }
      });
    },
    reset() {
      let that = this;
      that.$refs.form.resetFields();
      that.open = false;
      that.disabled = false;
      that.isGetCode = false;
      that.btnStr = "获取验证码";
    },
    cancelCode() {
      this.reset();
      this.open = false;
      cancelSmsCode().then(res => {});
    }
  }
};
</script>
