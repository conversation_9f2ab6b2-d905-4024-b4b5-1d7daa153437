<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业" prop="industryId">
        <el-select
          v-model="queryParams.industryId"
          placeholder="请选择行业"
          clearable
          @clear="handleIndustryClear">
          <el-option
            v-for="item in industryList"
            :key="item.id"
            :label="item.industryName"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户名称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:proposal:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:proposal:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="proposalList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="id" width="220" align="center" prop="proposalId" />-->
      <el-table-column label="行业名称" width="120" align="center" prop="industryName" />
      <el-table-column label="用户名称" width="120" align="center" prop="nickname" />
      <el-table-column label="建议内容" align="center" prop="content" />
      <el-table-column label="创建时间" width="180" align="center" prop="createTime" />
      <!-- <el-table-column label="建议内容" align="center" prop="content">
        <template slot-scope="scope">
          <a :title="scope.row.answer" href="#">{{scope.row.answer.length>50?scope.row.answer.substr(0,50)+'...':scope.row.answer}}</a>
        </template>
      </el-table-column> -->
       <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-del ete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:proposal:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { listProposal, getProposal, exportProposal,delProposal } from "@/api/system/proposal";
import { listIndustry } from "@/api/system/industry";

export default {
  name: "Proposal",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 优化建议表格数据
      proposalList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industryCode: null,
        nickname:'',
        industryId: null,
        customerId: null,
        content: null,
        exportIdList: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      industryList:[],
    };
  },
  created() {
    this.getList();
    listIndustry().then(response => {
      this.industryList = response.rows;
    });
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    /** 查询优化建议列表 */
    getList() {
      this.loading = true;
      listProposal(this.queryParams).then(response => {
        this.proposalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        proposalId: null,
        industryCode: null,
        customerId: null,
        content: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.proposalId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加优化建议";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const proposalId = row.proposalId || this.ids
      getProposal(proposalId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改优化建议";
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
     // const proposalIds = row.proposalId || this.ids;
     const proposalIds = row.proposalId == null ? this.ids : [row.proposalId];
     let msg = ' ';
     if ( this.ids.length > 0 ){
       msg = "是否确认删除选择的" + this.ids.length + "条数据?";
     }else {
       msg = '是否确认删除选择的数据项';
     }
      this.$confirm(msg, "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delProposal(proposalIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      // Combine queryParams with selected ids
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的优化建议数据';
      }else {
        msg = '是否确认导出所有的优化建议数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/proposal/export', exportParams, `proposal_${new Date().getTime()}.xlsx`);
      }).catch(() => {});
    }
  }
};
</script>
