import request from '@/utils/request'

// 查询E编排模板列表
export function listTemplate(query) {
  return request({
    url: '/system/template/list',
    method: 'get',
    params: query
  })
}

// 查询E编排模板详细
export function getTemplate(templateKey) {
  return request({
    url: '/system/template/' + templateKey,
    method: 'get'
  })
}

// 新增E编排模板
export function addTemplate(data) {
  return request({
    url: '/system/template',
    method: 'post',
    data: data
  })
}

// 修改E编排模板
export function updateTemplate(data) {
  return request({
    url: '/system/template',
    method: 'put',
    data: data
  })
}

// 删除E编排模板
export function delTemplate(templateKey) {
  return request({
    url: '/system/template/' + templateKey,
    method: 'delete'
  })
}

// 导出E编排模板
export function exportTemplate(query) {
  return request({
    url: '/system/template/export',
    method: 'get',
    params: query
  })
}