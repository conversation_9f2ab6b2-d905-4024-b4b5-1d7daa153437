import request from '@/utils/request'



export function examinelist(query) {
  return request({
      url: '/system/hzhb/case/main/examinelist',
      method: 'get',
      params: query
  })
}
export function agree(data) {
  return request({
      url: '/system/hzhb/case/main/agree',
      method: 'post',
      data: data
  })
}

export function reject(data) {
  return request({
      url: '/system/hzhb/case/main/reject',
      method: 'post',
      data: data
  })
}

export function examine(data) {
  return request({
      url: '/system/hzhb/case/main/examine',
      method: 'post',
      data: data
  })
}


export function add(data) {
    return request({
      url: '/system/hzhb/case/main',
      method: 'post',
      data: data
    })
  }
  
  export function update(data) {
    return request({
      url: '/system/hzhb/case/main',
      method: 'put',
      data: data
    })
  }


  export function deleteTo(id) {
    return request({
      url: '/system/hzhb/case/main/' + id,
      method: 'delete'
    })
  }


export function detail(id) {
    return request({
        url: '/system/hzhb/case/main/' + id,
        method: 'get'
    })
}

export function list(query) {
    return request({
        url: '/system/hzhb/case/main/list',
        method: 'get',
        params: query
    })
}
export function industrylist(query) {
  return request({
      url: '/system/hzhb/industry/list',
      method: 'get',
      params: query
  })
}