import request from '@/utils/request'




// 新增案例
export function add(data) {
    return request({
      url: '/system/hzhb/case/qxuser',
      method: 'post',
      data: data
    })
  }
  
  // 修改案例
  export function update(data) {
    return request({
      url: '/system/hzhb/case/qxuser',
      method: 'put',
      data: data
    })
  }
  
  // 删除案例
  export function deleteTo(id) {
    return request({
      url: '/system/hzhb/case/qxuser/' + id,
      method: 'delete'
    })
  }


export function detail(id) {
    return request({
        url: '/system/hzhb/case/qxuser/' + id,
        method: 'get'
    })
}

export function list(query) {
    return request({
        url: '/system/hzhb/case/qxuser/list',
        method: 'get',
        params: query
    })
}
