<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "1688px",
    },
    height: {
      type: String,
      default: "300px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {},
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(title, name, value, boo) {
      let that = this;
      this.chart = echarts.init(this.$el, "macarons");
      this.chart.setOption({
        title: {
          left: "left",
          text: title,
        },
        grid: {
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          appendToBody: true,
          axisPointer: {
            type: "shadow",
          },
          formatter: function (datas) {
            if (
              datas[0].data &&
              datas[0].data.nameOptions &&
              datas[0].data.valueOptions
            ) {
              var res = "";
              datas[0].data.nameOptions.forEach((item, index) => {
                res += item + ":" + datas[0].data.valueOptions[index] + "<br/>";
              });
              return res;
            }
            return "";
          },
        },
        xAxis: {
          triggerEvent: true,
          type: "category",
          data: name,
          axisLabel: {
            interval: 0,
            rotate: 0,
            color: "#666666", // 设置字体颜色
            formatter: (value) => {
              if (name.length < 25) {
                if (value.length <= 5) {
                  return value; // 如果长度小于或等于7，不进行换行
                }

                // 对超过7个字符的部分进行换行
                let formattedValue = "";
                for (let i = 0; i < value.length; i += 5) {
                  formattedValue += value.slice(i, i + 5) + "\n"; // 每7个字符换行
                }

                return formattedValue.trim(); // 去除最后的换行符
              }
              return value
                .split("")
                .map((char, index) => {
                  if (char === "(" || char === ")") {
                    return char;
                  }
                  return char + "\n";
                })
                .join("")
                .replace(/\(\n/g, "(")
                .replace(/\n\)/g, ")");
            },
          },
        },
        yAxis: {
          minInterval: 1, //不允许出现小数位
          type: "value",
        },
        series: [
          {
            barCategoryGap: "50%", // 设置柱子之间的间距，从而影响柱子的宽度
            data: value,
            barMaxWidth: "50px", // 最大宽度的百分比
            type: "bar",
            itemStyle: {
              normal: {
                barBorderRadius: [3, 3, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#96d2fb" },
                  { offset: 1, color: "#004fff" },
                ]),
                label: {
                  show: true, //开启显示
                  position: "top", //在上方显示
                  textStyle: {
                    //数值样式
                    color: "#3485C7",
                    fontSize: 16,
                  },
                },
              },
            },
          },
        ],
      });
      if (!boo) {
        this.$nextTick(() => {
          if (this.chart != null) {
            this.chart.off("click");
            this.chart.on("click", function (params) {
              if (params.componentType == "yAxis") {
              } else {
                that.$parent.$parent.$parent.$parent.bigDepatClick(
                  params.value
                );
                //that.$parent.$parent.bigDepatClick(params.value);
              }
            });
          }
        });
      }
    },
  },
};
</script>
<style scoped>
.echarts-tooltip {
  position: absolute !important;
  z-index: 10000 !important;
}
</style>