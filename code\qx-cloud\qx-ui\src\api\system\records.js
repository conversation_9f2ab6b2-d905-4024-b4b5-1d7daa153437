import request from '@/utils/request'

// 查询点击记录列表
export function listRecords(query) {
  return request({
    url: '/system/records/list',
    method: 'get',
    params: query,
    timeout: 1000*60*60
  })
}

// 查询点击记录详细
export function getRecords(id) {
  return request({
    url: '/system/records/' + id,
    method: 'get'
  })
}

// 新增点击记录
export function addRecords(data) {
  return request({
    url: '/system/records',
    method: 'post',
    data: data
  })
}

// 修改点击记录
export function updateRecords(data) {
  return request({
    url: '/system/records',
    method: 'put',
    data: data
  })
}

// 删除点击记录
export function delRecords(id) {
  return request({
    url: '/system/records/' + id,
    method: 'delete'
  })
}

// 导出点击记录
export function exportRecords(query) {
  return request({
    url: '/system/records/export',
    method: 'get',
    params: query,
    timeout: 1000*60*60
  })
}