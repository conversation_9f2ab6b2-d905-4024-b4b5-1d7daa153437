<template>
    <div class="app-container">
        <div style="width: 1px;height: 1px;background: #000;"></div>
        <div class="my_flex">
            <div class="left-flex">
                <div class="Overall_bg">
                    <div>一、平台总体数据</div>
                </div>
                <div>
                    <div class="visits-num">
                        <div class="num-text">{{ sumCount }}</div>
                    </div>
                    <div class="users-num">
                        <div class="num-text">{{ userSumNum }}</div>
                    </div>
                </div>
            </div>
            <div class="right-flex">
                <div class="">
                    <div class="utotal">
                        <PieChart v-if="isshow" class="Piechart" id="myChart" :height="'350px'" :width="'400px'"
                            :echartData="echartData" :color="color"></PieChart>
                    </div>
                    <div class="utime">
                        <PieChart v-if="isshow" class="Piechart" :height="'350px'" :width="'400px'"
                            :echartData="echartData1" :color="ecolor"></PieChart>
                    </div>
                </div>
            </div>
        </div>
        <div class="buttom-box">
            <div class="industry">
                <lineclickchart v-if="isshow" :linxAxisData="linxAxisData1" :linchartData="linchartData1"
                    :height="'400px'" :width="'900px'" class="lineclickchart"></lineclickchart>
            </div>
            <div class="modular">
                <lineclickchart v-if="isshow" :linxAxisData="linxAxisData" :linchartData="linchartData"
                    :height="'400px'" :width="'900px'" class="lineclickchart"></lineclickchart>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from 'echarts'
import { setScale } from '@/utils/setScale'
import PieChart from '../../dashboard/PieChart'
import lineclickchart from '../../dashboard/lineclickchart'
import { clicksumpopulation } from '@/api/system/sum.js'
export default {
    components: {
        PieChart,
        lineclickchart
    },
    name: 'Config',
    data() {
        return {
            titleOptions: [
                { id: 1, name: '行业访客' },
                { id: 2, name: '场景访客' },
                { id: 4, name: '网络方案访客' },
                { id: 5, name: '商业价值访客' },
                { id: 6, name: '落地案例访客' },
                { id: 7, name: 'VR看现场访客' },
                { id: 8, name: '集成报价访客' },
                { id: 9, name: '行业生态链访客' }
            ],
            color: ['#93BEFF', '#4F7AFC', '#FFAA57'],
            isfirst: false,
            echartData: [],
            echartDataJson: {
                towNum: '0-2次',
                fiveNum: '3-5次',
                sixNum: '6次以上'
            },
            ecolor: ['#FFAA57', '#4F7AFC', '#93BEFF', '#283E81', '#4ECC93'],
            echartData1: [],
            echartData1Json: {
                halfMinuteNum: '10-30s',
                maxMinuteNum: '181s以上',
                oneMinuteNum: '31-60s',
                tenSecondNum: '10s以内',
                threeMinuteNum: '61-180s'
            },
            linxAxisData: [],
            linxAxisData1: [],
            linchartData: [],
            linchartData1: [],
            sumCount: '', //访问总数
            userSumNum: '', // 用户数
            isshow: false
        }
    },
    mounted() {
        this.getList()
        console.log(document.getElementById('#hamburger-container'))
        setScale()
        window.onresize = () => {
            setScale()
        }
    },
    methods: {
        getList() {
            clicksumpopulation().then(res => {
                this.userSumNum = res.data.userSumNum
                res.data.visitFrequency.towNum = this.userSumNum - (res.data.visitFrequency.fiveNum + res.data.visitFrequency.sixNum)
                //this.sumCount = res.data.visitFrequency.sumCount
                let qxClickRecordsItemCount = res.data.qxClickRecordsItemCount
                let visitFrequency = res.data.visitFrequency
                let industryModuleClick = res.data.industryModuleClick
                // industryModuleClick.sort((a, b) => a.clickValue - b.clickValue)
                // industryModuleClick.forEach(item => {
                //     if ()
                // });

                //点击类型
                this.titleOptions.forEach(item => {
                    item.sum = 0
                    industryModuleClick.forEach(row => {
                        if (item.id == row.clickType) {
                            item.sum += parseInt(row.clickNumber)
                        }
                    })
                })
                this.titleOptions.sort((a, b) => b.sum - a.sum)
                this.titleOptions.forEach(item => {
                    this.linxAxisData1.push(item.name)
                    this.linchartData1.push(item.sum)
                })
                // console.log(this.titleOptions)

                //行业分类
                res.data.qxIndustryList.forEach(item => {
                    item.sum = 0
                    industryModuleClick.forEach(row => {
                        if (item.industryCode == row.clickValue && parseInt(row.clickType) < 10) {
                            item.sum += parseInt(row.clickNumber)
                        }
                    })
                })
                res.data.qxIndustryList.sort((a, b) => b.sum - a.sum)
                res.data.qxIndustryList.forEach(item => {
                    this.linxAxisData.push(item.industryName)
                    this.linchartData.push(item.sum)
                })

                console.log(res.data.qxIndustryList)

                delete visitFrequency.sumCount

                this.sumCount = 0
                // this.echartData1[1].value = qxClickRecordsItemCount.halfMinuteNum
                for (var key in qxClickRecordsItemCount) {
                    this.echartData1.push({ name: this.echartData1Json[key], value: qxClickRecordsItemCount[key] })
                    this.sumCount += qxClickRecordsItemCount[key]
                    console.log(this.sumCount)
                }
                //  =
                for (var key in visitFrequency) {
                    this.echartData.push({ name: this.echartDataJson[key], value: visitFrequency[key] })
                }
                console.log(this.echartData)
                this.isshow = true
                //console.log(industryModuleClick)
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.app-container {
    padding: 40px;

    .buttom-box {
        display: flex;
        justify-content: space-between;
    }
}

.my_flex {
    width: 100%;
    display: flex;

    .left-flex {
        /* flex: 1; */
        width: 49%;

        div {
            display: flex;
            justify-content: space-between;
        }
    }

    .right-flex {
        width: 52%;
        /* flex: 1.1; */
        padding-left: 40px;

        div {
            display: flex;
            justify-content: space-between;
        }
    }
}

.visits-num {
    width: 433px;
    height: 310px;

    // background: url(../../assets/images/click/visits-num.png  ) no-repeat center center;
    background: url(../../../assets/images/click/visits-num.png) no-repeat center center;
    background-size: 100%;
    text-align: center;
    padding-right: 38px;
}

// .users-num {
//     width: 433px;
//     height: 310px;

//     background: url(../../../assets/images/click/user-num.png) no-repeat center center;
//     background-size: 100%;
//     text-align: center;
// }

// .Overall_bg {
//     width: 896px;
//     height: 91px;
//     background: url(../../../assets/images/click/Overall_bg.png) no-repeat center center;
//     background-size: 100%;
//     text-align: center;
//     margin-bottom: 40px;
//     div {
//         font-weight: 800;
//         font-size: 45px;
//         padding-left: 21px;
//         display: flex;
//         align-items: center;
//         color: #fff;
//     }
// }

// .utotal {
//     width: 433px;
//     height: 446px;
//     background: url(../../../assets/images/click/usertotal.png) no-repeat center center;
//     background-size: 100%;
//     text-align: center;
//     margin-bottom: 40px;
// }

// .utime {
//     width: 433px;
//     height: 446px;
//     background: url(../../../assets/images/click/usertime.png) no-repeat center center;
//     background-size: 100%;
//     text-align: center;
//     margin-bottom: 40px;
// }

// .num-text {
//     width: 270px;
//     height: 96px;
//     font-size: 70px;
//     font-family: Futura-粗体, Futura;
//     font-weight: 800;
//     line-height: 96px;
//     text-shadow: 0px 24px 35px rgba(47, 143, 200, 0.1);
//     background: linear-gradient(270deg, #30d5a9 0%, #1078c2 100%);
//     -webkit-background-clip: text;
//     -webkit-text-fill-color: transparent;
//     text-align: center;
//     margin: 127px 0 0 130px;
// }

// .Piechart {
//     width: 400px;
//     height: 300px;
//     margin: auto;
//     margin-top: 70px;
// }

// .lineclickchart {
//     width: 900px;
//     height: 400px;
//     margin: auto;
//     margin-top: 70px;

// }

// .industry {
//     width: 900px;
//     height: 480px;
//     background: url(../../../assets/images/click/industry.png) no-repeat center center;
//     background-size: 100%;
//     text-align: center;

// }

// .modular {
//     width: 900px;
//     height: 480px;
//     background: url(../../../assets/images/click/modular.png) no-repeat center center;
//     background-size: 100%;
//     text-align: center;

// }</style>
