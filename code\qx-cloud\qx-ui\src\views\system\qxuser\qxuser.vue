<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号码" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="账号" prop="account">
        <el-input v-model="queryParams.account" placeholder="请输入账号" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="状态" prop="accountState">
        <el-select v-model="queryParams.accountState" placeholder="用户状态" clearable size="small" style="width: 240px">
          <el-option v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="角色" prop="roleIds">

        <el-select v-model="queryParams.roleIds" style="width: 100%;" multiple size="small" placeholder="请选择">
          <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId" :disabled="item.status == 1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:qxuser:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:qxuser:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:qxuser:remove']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExportVo" v-hasPermi="['system:qxuser:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账号" align="center" prop="account">
        <template #default="scope">
          <el-tooltip :content="scope.row.account" raw-content placement="top-start" v-if="scope.row.account">
            <span v-if="scope.row.account && scope.row.account.length <= 30">
              {{ scope.row.account }}
            </span>
            <span v-if="scope.row.account && scope.row.account.length > 30">
              {{ scope.row.account.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.account== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="昵称" align="center" prop="nickname">
        <template #default="scope">
          <el-tooltip :content="scope.row.nickname" raw-content placement="top-start" v-if="scope.row.nickname">
            <span v-if="scope.row.nickname && scope.row.nickname.length <= 30">
              {{ scope.row.nickname }}
            </span>
            <span v-if="scope.row.nickname && scope.row.nickname.length > 30">
              {{ scope.row.nickname.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.nickname== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="电话号码" align="center" prop="phone" />
      <!-- <el-table-column label="电子邮箱" align="center" prop="email" /> -->
      <el-table-column label="用户角色" align="center" prop="phone">
        <template slot-scope="scope">
          <span>{{ getRoleName(scope.row) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column v-if="activeName==2" label="区域" align="center" prop="region" />
      <el-table-column v-if="activeName==2" label="部门名称" align="center" prop="departmentName">
        <template #default="scope">
          <el-tooltip :content="scope.row.departmentName" raw-content placement="top-start" v-if="scope.row.departmentName">
            <span v-if="scope.row.departmentName && scope.row.departmentName.length <= 30">
              {{ scope.row.departmentName }}
            </span>
            <span v-if="scope.row.departmentName && scope.row.departmentName.length > 30">
              {{ scope.row.departmentName.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.departmentName== null"> </span>
        </template>
      </el-table-column>
      <el-table-column v-if="activeName==2" label="用户职务" align="center" prop="userJob">
        <template #default="scope">
          <el-tooltip :content="scope.row.userJob" raw-content placement="top-start" v-if="scope.row.userJob">
            <span v-if="scope.row.userJob && scope.row.userJob.length <= 30">
              {{ scope.row.userJob }}
            </span>
            <span v-if="scope.row.userJob && scope.row.userJob.length > 30">
              {{ scope.row.userJob.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.userJob== null"> </span>
        </template>
      </el-table-column>

      <el-table-column label="企业图片" v-if="activeName==0" align="center" prop="icon">
        <template slot-scope="scope">
          <img v-if="scope.row.icon" style="width:50px;height:50px;border-radius: 50%;" :src="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column label="供应商类型" v-if="activeName==0" align="center" prop="supplierGrade">
        <template slot-scope="scope">
          <span>{{ scope.row.supplierGrade == 0 ? '普通供应商' : '一级供应商' }}</span>
        </template>
      </el-table-column> -->

      <el-table-column label="状态" align="center" key="accountState">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.accountState" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:qxuser:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleUpdatePwd(scope.row)" v-hasPermi="['system:qxuser:edit']">重置密码</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:qxuser:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body @close="cancel">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账号" prop="account">
              <el-input v-model="form.account" v-bind:disabled="form.id!=null" placeholder="请输入账号" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password" v-show="form.id==null">
              <el-input v-model="form.password" type="password" maxlength="32" placeholder="请输入密码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname" style="margin-top:10px;">
              <el-input v-model="form.nickname" placeholder="请输入昵称" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone" style="margin-top:10px;">
              <el-input v-model="form.phone" placeholder="请输入手机号码" maxlength="11" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" maxlength="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" style="width: 100%;" multiple placeholder="请选择">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId" :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <!-- <el-form-item label="类型">
          <el-select v-model="activeName" multiple placeholder="请选择">
            <el-option key="0" label="合作伙伴用户" value="0"></el-option>
            <el-option key="1" label="审核用户" value="1"></el-option>
            <el-option key="2" label="省管理员用户" value="2"></el-option>
          </el-select>
        </el-form-item> -->

        <el-row>
          <el-col :span="12">
            <el-form-item label="企业性质" prop="unitType">
              <el-select v-model="form.unitType" style="width: 100%;" filterable multiple placeholder="请选择企业性质">
                <el-option v-for="dict in unitTypeList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商类型" prop="supplierGrade">
              <el-select v-model="form.supplierGrade" placeholder="请选择">
                <el-option key="0" label="普通供应商" value="0"></el-option>
                <el-option key="1" label="一级供应商" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="企业图片" prop="icon">
              <ImageUpload @input="setInput" :value="form.icon" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="人员规模" prop="scales">
              <el-select v-model="form.scales" style="width: 100%;" filterable multiple placeholder="请选择人员规模">
                <el-option v-for="dict in scalesList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="departmentName">
              <el-input v-model="form.departmentName" placeholder="请输入部门名称" maxlength="100" show-word-limit />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户职务" prop="userJob">
              <el-input v-model="form.userJob" placeholder="请输入用户职务" maxlength="50" show-word-limit />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合作伙伴标签" prop="tagCode">
              <el-select v-model="form.tagCode" style="width: 100%;" filterable multiple placeholder="请选择标签">
                <el-option v-for="dict in tagTypeList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="账号状态">
              <el-radio-group v-model="form.accountState">
                <el-radio v-for="dict in statusOptions" :key="dict.dictValue" :label="dict.dictValue">{{ dict.dictLabel }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="合作伙伴简介" v-if="activeName==0">
              <el-input v-model="form.introduce" type="textarea" placeholder="请输入简介" show-word-limit maxlength="2000"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" show-word-limit maxlength="200"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="openSetPwd" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="pwdform" :model="pwdform" :rules="pwdrules" label-width="80px">
        <el-form-item label="密码" prop="password">
          <el-input v-model="pwdform.password" placeholder="请输入密码" maxlength="32" type="password" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPwdForm">确 定</el-button>
        <el-button @click="cancelPwd">取 消</el-button>
      </div>
    </el-dialog>

    <!-- <el-dialog title="导出数据-4A管控验证码" :visible.sync="openCode" @close="cancelCode()" width="450px" append-to-body :close-on-click-modal="false">
          <el-form ref="codeform" :model="codeform" :rules="coderules" label-width="80px">
            <el-form-item label="验证码" prop="loginCode">
              <el-input v-model="codeform.loginCode" maxlength="10" show-word-limit placeholder="请输入验证码" />
            </el-form-item>

          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitCode">确 定</el-button>
            <el-button @click="cancelCode">取 消</el-button>
          </div>
        </el-dialog> -->
    <TreasuryVerify ref="treasuryVerifyRef" functionCode="600002" operationCode="1002" />
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  exportUser,
  editpwd
} from "@/api/system/qxuser";
import ImageUpload from "../../../components/ImageUpload";
import TreasuryVerify from "../../../components/TreasuryVerify/TreasuryVerify.vue";
import { Loading } from "element-ui";
import store from "@/store";
import md5 from "md5";

export default {
  name: "User",
  components: {
    ImageUpload,
    TreasuryVerify,
  },

  data() {
    var checkAccount = (rule, value, callback) => {
      if (this.form.account === null || this.form.account === "")
        return callback(new Error("用户账号不能为空"));
      var reg = /^[a-zA-Z0-9.@_-]{8,50}$/;
      var msg =
        "账号格式不正确，只能包含字母、数字、下划线和短横线，或账号邮箱，长度在8到50个字符之间";
      // if (
      //   this.activeName == 0 ||
      //   this.activeName == 1 ||
      //   this.activeName == 2
      // ) {
      //   reg = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      //   msg = "账号邮箱格式不正确";
      // }
      if (!reg.test(this.form.account)) return callback(new Error(msg));
      return callback();
    };
    var checkEmail = (rule, value, callback) => {
      if (this.activeName == 4) {
        if (this.form.email === null || this.form.email === "")
          return callback(new Error("邮箱不能为空"));
        // var reg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        var reg = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
        var msg = "邮箱格式不正确";
        if (!reg.test(this.form.email)) return callback(new Error(msg));
      }
      return callback();
    };

    var checkPassword = (rule, value, callback) => {
      if (this.form.id == null) {
        if (this.form.password === null || this.form.password === "")
          return callback(new Error("密码不能为空"));
        else {
          var reg =
            /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,32}$/;
          var msg =
            "密码不符合复杂度要求。应至少包含大写字母、小写字母、数字或特殊字符的其中三种，密码长度8-32位";
          if (!reg.test(this.form.password)) return callback(new Error(msg));
        }
      }
      return callback();
    };

    return {
      statusOptions: [],
      activeName: 0,
      accountType: store.getters.accountType,
      openSetPwd: false,
      pwdform: {},
      // 表单校验
      pwdrules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            pattern:
              /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,32}$/,
            message:
              "密码不符合复杂度要求。应至少包含大写字母、小写字母、数字或特殊字符的其中三种，密码长度8-32位",
            trigger: "blur",
          },
        ],
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 角色选项
      roleOptions: [],
      tagTypeList: [],
      unitTypeList: [],
      scalesList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        phone: null,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        email: null,
        icon: null,
        isExamine: null,
        trgCode: null,
        roleIds: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        account: [
          {
            required: true,
            validator: checkAccount,
            trigger: "blur",
          },
        ],
        email: [
          {
            required: true,
            validator: checkEmail,
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            validator: checkPassword,
            trigger: "blur",
          },
        ],
        nickname: [
          { required: true, message: "用户名称不能为空", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^(13|14|15|16|17|18|19)\d{9}$/,
            message: "手机号码格式不正确",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("tag_type").then((response) => {
      this.tagTypeList = response.data;
    });
    this.getDicts("unit_type").then((response) => {
      this.unitTypeList = response.data;
    });
    this.getDicts("personnel_size").then((response) => {
      this.scalesList = response.data;
    });
    getUser(-1).then((response) => {
      this.roleOptions = response.roles;
    });
  },
  methods: {
    getRoleName(row) {
      let name = "";
      for (let i = 0; i < row.qxUserRoleToList.length; i++) {
        name =
          name == ""
            ? row.qxUserRoleToList[i].roleName
            : name + "," + row.qxUserRoleToList[i].roleName;
      }
      return name;
    },
    /** 修改按钮操作 */
    handleUpdatePwd(row) {
      const id = row.id || this.ids;
      this.openSetPwd = true;
      this.pwdform = {
        id: id,
        password: null,
        resetPwd: 0,
      };
      this.resetForm("pwdform");
    },
    cancelPwd() {
      this.openSetPwd = false;
      this.pwdform = {
        id: null,
        password: null,
        resetPwd: 0,
      };
      this.resetForm("pwdform");
    },
    /** 提交按钮 */
    submitPwdForm() {
      this.$refs["pwdform"].validate((valid) => {
        if (valid) {
          if (this.pwdform.id != null) {
            this.pwdform.password = md5(this.pwdform.password);
            this.pwdform.resetPwd = 1;
            editpwd(this.pwdform)
              .then((response) => {
                this.msgSuccess("修改密码成功");
                this.openSetPwd = false;
              })
              .catch((r) => {
                this.pwdform.password = "";
              });
          }
        }
      });
    },
    setInput(value) {
      this.$set(this.form, "icon", value);
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      //this.queryParams.accountType = this.activeName;
      listUser(this.queryParams).then((response) => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        phone: null,
        account: null,
        password: null,
        nickname: null,
        accountState: "0",
        accountType: 0,
        email: null,
        icon: null,
        createTime: null,
        roleIds: null,
      };
      this.resetForm("form");
    }, // 用户状态修改
    handleStatusChange(row) {
      let that = this;
      let text = row.accountState === "0" ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.account + '"用户吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          updateUser({
            id: row.id,
            accountState: row.accountState,
            accountType: row.accountType,
          })
            .then((response) => {
              that.msgSuccess(text + "成功");
            })
            .catch(function () {
              row.accountState = row.accountState === "0" ? "1" : "0";
            });
        })
        .catch(function () {
          row.accountState = row.accountState === "0" ? "1" : "0";
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      //this.reset();
      const id = row.id || this.ids;
      getUser(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
      // getUser(-1).then(response => {
      //   this.open = true;
      //   this.title = "系统用户添加用户";
      //   this.roleOptions = response.roles;
      // });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.accountType = this.activeName;
          if (this.form.id != null) {
            this.form.password = null;
            updateUser(this.form).then((response) => {
              this.msgSuccess(response.msg);
              this.open = false;
              this.getList();
            });
          } else {
            this.form.password = md5(this.form.password);
            if (this.form.password === "" || this.form.password == null) {
              that.$message.error("密码不能为空");
              return false;
            }
            addUser(this.form)
              .then((response) => {
                this.msgSuccess(response.msg);
                this.open = false;
                this.getList();
              })
              .then(() => {
                this.form.password = "";
              });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      console.log(row);
      console.log(row.id);
      this.$confirm("是否确认删除?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delUser(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(response.msg);
        });
    },
    // handleExport() {
    //   let downloadLoadingInstance = Loading.service({ text: "正在申请4A金库操作，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", });
    //   exportUserVo().then(response => {
    //     let data = response.msg;
    //     if (data == 0) {
    //       this.openCode = true;
    //       this.loading = false;
    //     } else {
    //       this.msgError("获取导出4A金库验证码失败，请联系管理员");
    //     }
    //     if (downloadLoadingInstance != null)
    //       downloadLoadingInstance.close();
    //   }).catch((r) => {
    //     if (downloadLoadingInstance != null)
    //       downloadLoadingInstance.close();
    //   });
    // },
    // cancelCode() {
    //   this.openCode = false;
    //   this.codeform.loginCode = "";
    //   cencelexport().then(response=>{

    //   })
    // },
    /** 导出按钮操作 */
    handleExportVo() {
      const queryParams = this.queryParams;
      this.queryParams.accountType = this.activeName;
      exportUser(queryParams).then((response) => {
        if (response.msg != "-1") {
          this.handleExport();
        } else {
          this.$refs.treasuryVerifyRef.getTreasuryApprover();
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null, // Add ids if any are selected
      };
      that.download(
        "system/qxuser/exportvo",
        exportParams,
        `user_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
