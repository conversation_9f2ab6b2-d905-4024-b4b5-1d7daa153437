{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\scene\\scene.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\scene\\scene.vue", "mtime": 1755684666418}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\babel.config.js", "mtime": 1744356874228}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747036191864}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747036191888}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_scene", "require", "_PanoramaUpload", "_interopRequireDefault", "_ImageUpload", "_ImageUpload2", "_industry", "name", "components", "ImageUpload", "VideoUpload", "PanoramaUpload", "data", "_defineProperty2", "default", "options", "loadingPanorama", "loading", "ids", "single", "multiple", "showSearch", "total", "sceneList", "tagTypeList", "title", "open", "queryParams", "pageNum", "pageSize", "scene<PERSON><PERSON>", "industryId", "sceneExplain", "sceneCode", "trgCode", "form", "rules", "required", "message", "trigger", "industryList", "sceneListVo", "created", "_this", "getList", "listIndustry", "then", "response", "rows", "getIndustryScene", "getDicts", "methods", "_this2", "listScene", "setOption", "queryParamsHandleChange", "value", "length", "paramId", "handleClear", "sceneIds", "_this3", "for<PERSON>ach", "element", "item", "id", "label", "industryName", "children", "row", "push", "setIndustrycode", "_this4", "industryCode", "handleIndustryClear", "setPanoramaUrlInput", "$set", "panoramaTime", "Date", "getTime", "setInput", "setCover", "setShareImg", "setShareVedio", "console", "log", "_this5", "listSceneV2", "cancel", "reset", "createBy", "createTime", "updateBy", "updateTime", "sceneUrl", "sceneOrder", "shareVedio", "shareImg", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "handleAdd", "handleUpdate", "_this6", "getScene", "option", "_objectSpread2", "child", "disabled", "submitForm", "_this7", "$refs", "validate", "valid", "$loading", "lock", "text", "background", "updateScene", "msgSuccess", "close", "catch", "r", "addScene", "handleDelete", "_this8", "names", "filter", "includes", "join", "$confirm", "confirmButtonText", "cancelButtonText", "type", "checkScene", "delScene", "handleExport", "that", "exportParams", "exportIdList", "msg", "download", "concat"], "sources": ["src/views/system/scene/scene.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"行业\" prop=\"industryId\">\n        <el-select v-model=\"queryParams.industryId\" placeholder=\"请选择行业\" size=\"small\" filterable clearable @clear=\"handleIndustryClear\">\n          <el-option v-for=\"item in industryList\" :key=\"item.id\" :label=\"item.industryName\" :value=\"item.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"场景名称\" prop=\"sceneName\">\n        <el-input v-model=\"queryParams.sceneName\" placeholder=\"请输入场景名称\" clearable size=\"small\" @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n      <el-form-item label=\"场景编码\" prop=\"sceneCode\">\n        <el-input v-model=\"queryParams.sceneCode\" placeholder=\"请输入场景编号\" clearable size=\"small\" @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['system:scene:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\" v-hasPermi=\"['system:scene:edit']\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['system:scene:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" v-hasPermi=\"['system:scene:export']\">导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"sceneList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <!--       <el-table-column label=\"id\" align=\"center\" prop=\"id\" />-->\n      <el-table-column label=\"行业场景名称\" align=\"center\" prop=\"industryName\" >\n         <template #default=\"scope\">\n            {{ scope.row.industryName +'/'+ scope.row.paramName}}\n         </template>\n        </el-table-column>\n      <el-table-column label=\"场景名称\" align=\"center\" prop=\"sceneName\" />\n      <el-table-column label=\"场景编码\" align=\"center\" prop=\"sceneCode\" />\n      <el-table-column label=\"场景说明\" align=\"center\" prop=\"sceneExplain\">\n        <template #default=\"scope\">\n          <el-tooltip :content=\"scope.row.sceneExplain\" raw-content placement=\"top-start\" v-if=\"scope.row.sceneExplain\">\n            <span v-if=\"scope.row.sceneExplain && scope.row.sceneExplain.length <= 30\">\n              {{ scope.row.sceneExplain }}\n            </span>\n            <span v-if=\"scope.row.sceneExplain && scope.row.sceneExplain.length > 30\">\n              {{ scope.row.sceneExplain.substr(0, 30) + \"...\" }}\n            </span>\n          </el-tooltip>\n          <span v-else-if=\"scope.row.sceneExplain== null\"> </span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序号\" align=\"center\" prop=\"sceneOrder\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['system:scene:edit']\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['system:scene:remove']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n    <!-- 添加或修改场景对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1000px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景名称\" prop=\"sceneName\">\n              <el-input v-model=\"form.sceneName\" maxlength=\"50\" show-word-limit placeholder=\"请输入场景名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景编码\" prop=\"sceneCode\">\n              <el-input v-model=\"form.sceneCode\" maxlength=\"50\" show-word-limit placeholder=\"请输入场景编码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"行业场景\" prop=\"industryId\">\n              <el-cascader v-model=\"form.sceneIds\" size=\"small\" placeholder=\"请选择行业场景\" :props=\"{ checkStrictly: true }\" :options=\"options\" filterable clearable @change=\"queryParamsHandleChange\" @clear=\"handleClear\">\n              </el-cascader>\n              <!-- <el-select v-model=\"form.industryId\" placeholder=\"请选择行业\" onchange=\"setIndustrycode()\">\n                <el-option v-for=\"item in industryList\" :key=\"item.id\" :label=\"item.industryName\" :value=\"item.id\"></el-option>\n              </el-select> -->\n              <br />\n              (一级场景选择行业,二级场景选择父场景)\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"排序号\" prop=\"sceneOrder\">\n              <el-input-number v-model=\"form.sceneOrder\" :min=\"0\" :max=\"999999999\" label=\"请输入排序号\"></el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"场景背景图\" prop=\"sceneUrl\">-->\n<!--              <ImageUpload @input=\"setInput\" :value=\"form.sceneUrl\" />-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景全景图\" prop=\"panoramaUrl\">\n              <PanoramaUpload @input=\"setPanoramaUrlInput\" :value=\"form.panoramaUrl\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景封面图\" prop=\"coverUrl\">\n              <ImageUpload @input=\"setCover\" :value=\"form.coverUrl\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n<!--        <el-row>-->\n<!--          <el-col :span=\"16\">-->\n<!--            <el-form-item label=\"分享视频\" prop=\"shareVedio\">-->\n<!--              <VideoUpload @input=\"setShareVedio\" tipContent=\"(备注:上传mp4文件,文件最大500M)\" accept=\".mp4\" :fileSize=\"500\" :value=\"form.shareVedio\" />-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--        </el-row>-->\n\n        <el-row>\n          <el-col :span=\"18\">\n            <el-form-item label=\"场景说明\" prop=\"sceneExplain\">\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.sceneExplain\" maxlength=\"200\" show-word-limit placeholder=\"请输入场景说明\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listScene,\n  listSceneV2,\n  getScene,\n  delScene,\n  addScene,\n  updateScene,\n  exportScene,\n  checkScene,\n} from \"@/api/system/scene\";\n\nimport PanoramaUpload from \"../../../components/PanoramaUpload\";\nimport ImageUpload from \"../../../components/ImageUpload\";\nimport VideoUpload from \"../../../components/ImageUpload/ImageUpload.vue\";\nimport { listIndustry } from \"@/api/system/industry\";\n\nexport default {\n  name: \"Scene\",\n  components: {\n    ImageUpload,\n    VideoUpload,\n    PanoramaUpload,\n  },\n  data() {\n    return {\n      options: [],\n      // 遮罩层\n      loadingPanorama: null,\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 场景表格数据\n      sceneList: [],\n      tagTypeList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        sceneName: null,\n        industryId: null,\n        sceneExplain: null,\n        sceneCode: null,\n        trgCode: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        sceneName: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n        ],\n        sceneCode: [\n          { required: true, message: \"编码不能为空\", trigger: \"blur\" },\n        ],\n        industryId: [\n          { required: true, message: \"所属行业不能为空\", trigger: \"blur\" },\n        ],\n      },\n      //行业列表\n      industryList: [],\n      sceneListVo: [],\n      options:[]\n    };\n  },\n  created() {\n    this.getList();\n    listIndustry().then((response) => {\n      this.industryList = response.rows;\n    });\n\n    this.getIndustryScene();\n\n    this.getDicts(\"tag_type\").then((response) => {\n      this.tagTypeList = response.data;\n    });\n  },\n  methods: {\n    getIndustryScene() {\n      listIndustry().then((response) => {\n        this.industryList = response.rows;\n        listScene().then((response) => {\n          this.sceneListVo = response.rows;\n          this.setOption();\n        });\n      });\n    },\n    //选中\n    queryParamsHandleChange(value) {\n      if (value != null || value.length > 0) {\n        this.form.industryId = value[0];\n      }\n\n      if (value != null || value.length > 1) {\n        this.form.paramId = value[1];\n      }\n    },\n    handleClear() {\n      // 清除场景时执行的操作\n      this.queryParams.sceneIds = []; // 清空选中的场景\n    },\n    //设置行业场景下拉\n    setOption() {\n      this.industryList.forEach((element) => {\n        let item = {\n          value: element.id,\n          label: element.industryName,\n          children: [],\n        };\n        this.sceneListVo.forEach((row) => {\n          if (row.industryId === element.id) {\n            item.children.push({\n              value: row.id,\n              label: row.sceneName,\n            });\n          }\n        });\n        this.options.push(item);\n      });\n    },\n\n    setIndustrycode() {\n      this.industryList.forEach((row) => {\n        if (this.form.industryId == row.id) {\n          this.form.industryCode = row.industryCode;\n        }\n      });\n    },\n    handleIndustryClear() {\n      // 清除行业选择时执行的操作\n      this.queryParams.industryId = null; // 重置行业ID\n    },\n    setPanoramaUrlInput(value) {\n      this.$set(this.form, \"panoramaUrl\", value);\n      this.form.panoramaTime = new Date().getTime();\n    },\n    setInput(value) {\n      this.$set(this.form, \"sceneUrl\", value);\n    },\n    setCover(value) {\n      this.$set(this.form, \"coverUrl\", value);\n    },\n    setShareImg(value) {\n      this.$set(this.form, \"shareImg\", value);\n    },\n    setShareVedio(value) {\n      console.log(value);\n      this.$set(this.form, \"shareVedio\", value);\n    },\n\n    /** 查询场景列表 */\n    getList() {\n      this.loading = true;\n      listSceneV2(this.queryParams).then((response) => {\n        this.sceneList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        sceneName: null,\n        industryId: null,\n        sceneExplain: null,\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        sceneUrl: null,\n        sceneCode: null,\n        sceneOrder: null,\n        shareVedio: null,\n        shareImg: null,\n        industryCode: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加场景\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getScene(id).then((response) => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改场景\";\n\n        if (this.form.industryId && this.form.paramId) {\n          this.form.sceneIds = [this.form.industryId, this.form.paramId];\n        } else if (this.form.industryId) {\n          this.form.sceneIds = [this.form.industryId];\n        } else {\n          this.form.sceneIds = []; // 确保始终是数组\n        }\n\n        console.log(\"sdfsdf\", this.form.sceneIds);\n        this.industryList.forEach((row) => {\n          if (this.form.industryId == row.id) {\n            this.form.industryCode = row.industryCode;\n          }\n        });\n\n        this.options = this.options.map((option) => {\n          return {\n            ...option,\n            children: option.children.map((child) => {\n              return {\n                ...child,\n                disabled: child.value === this.form.id,\n              };\n            }),\n          };\n        });\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          this.loadingPanorama = this.$loading({\n            lock: true,\n            text: \"数据上传并解析图片中...\",\n            background: \"rgba(0, 0, 0, 0.7)\",\n          });\n          if (this.form.id != null) {\n            updateScene(this.form)\n              .then((response) => {\n                this.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n                this.getIndustryScene();\n                this.loadingPanorama.close();\n              })\n              .catch((r) => {\n                if (this.loadingPanorama != null) this.loadingPanorama.close();\n              });\n          } else {\n            addScene(this.form)\n              .then((response) => {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n                this.getIndustryScene();\n                this.loadingPanorama.close();\n              })\n              .catch((r) => {\n                if (this.loadingPanorama != null) this.loadingPanorama.close();\n              });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id == null ? this.ids : [row.id];\n      const names = this.sceneList\n        .filter((item) => ids.includes(item.id))\n        .map((item) => item.sceneCode)\n        .join(\", \");\n\n      this.$confirm('是否确认删除场景编号为\"' + names + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(function () {\n          return checkScene(ids);\n        })\n        .then((response) => {\n          return delScene(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      let that = this;\n      const exportParams = {\n        ...that.queryParams,\n        exportIdList: that.ids.length ? that.ids : null, // Add ids if any are selected\n      };\n      let msg = \"\";\n      if (that.ids.length > 0) {\n        msg = \"是否确认导出所筛选或选中的场景数据\";\n      } else {\n        msg = \"是否确认导出所有的场景数据\";\n      }\n      this.$confirm(msg, \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(function () {\n          that.download(\n            \"system/scene/export\",\n            exportParams,\n            `scene_${new Date().getTime()}.xlsx`\n          );\n        })\n        .catch({});\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAsJA,IAAAA,MAAA,GAAAC,OAAA;AAWA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,aAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,SAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,UAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,WAAA,EAAAA,qBAAA;IACAC,cAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA;MACAC,OAAA;MACA;MACAC,eAAA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA;QACAC,YAAA;QACAC,SAAA;QACAC,OAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAN,SAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,SAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,UAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAC,YAAA;MACAC,WAAA;IAAA,cACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,IAAAC,sBAAA,IAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAH,YAAA,GAAAO,QAAA,CAAAC,IAAA;IACA;IAEA,KAAAC,gBAAA;IAEA,KAAAC,QAAA,aAAAJ,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAnB,WAAA,GAAAuB,QAAA,CAAAnC,IAAA;IACA;EACA;EACAuC,OAAA;IACAF,gBAAA,WAAAA,iBAAA;MAAA,IAAAG,MAAA;MACA,IAAAP,sBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAAZ,YAAA,GAAAO,QAAA,CAAAC,IAAA;QACA,IAAAK,gBAAA,IAAAP,IAAA,WAAAC,QAAA;UACAK,MAAA,CAAAX,WAAA,GAAAM,QAAA,CAAAC,IAAA;UACAI,MAAA,CAAAE,SAAA;QACA;MACA;IACA;IACA;IACAC,uBAAA,WAAAA,wBAAAC,KAAA;MACA,IAAAA,KAAA,YAAAA,KAAA,CAAAC,MAAA;QACA,KAAAtB,IAAA,CAAAJ,UAAA,GAAAyB,KAAA;MACA;MAEA,IAAAA,KAAA,YAAAA,KAAA,CAAAC,MAAA;QACA,KAAAtB,IAAA,CAAAuB,OAAA,GAAAF,KAAA;MACA;IACA;IACAG,WAAA,WAAAA,YAAA;MACA;MACA,KAAAhC,WAAA,CAAAiC,QAAA;IACA;IACA;IACAN,SAAA,WAAAA,UAAA;MAAA,IAAAO,MAAA;MACA,KAAArB,YAAA,CAAAsB,OAAA,WAAAC,OAAA;QACA,IAAAC,IAAA;UACAR,KAAA,EAAAO,OAAA,CAAAE,EAAA;UACAC,KAAA,EAAAH,OAAA,CAAAI,YAAA;UACAC,QAAA;QACA;QACAP,MAAA,CAAApB,WAAA,CAAAqB,OAAA,WAAAO,GAAA;UACA,IAAAA,GAAA,CAAAtC,UAAA,KAAAgC,OAAA,CAAAE,EAAA;YACAD,IAAA,CAAAI,QAAA,CAAAE,IAAA;cACAd,KAAA,EAAAa,GAAA,CAAAJ,EAAA;cACAC,KAAA,EAAAG,GAAA,CAAAvC;YACA;UACA;QACA;QACA+B,MAAA,CAAA9C,OAAA,CAAAuD,IAAA,CAAAN,IAAA;MACA;IACA;IAEAO,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAhC,YAAA,CAAAsB,OAAA,WAAAO,GAAA;QACA,IAAAG,MAAA,CAAArC,IAAA,CAAAJ,UAAA,IAAAsC,GAAA,CAAAJ,EAAA;UACAO,MAAA,CAAArC,IAAA,CAAAsC,YAAA,GAAAJ,GAAA,CAAAI,YAAA;QACA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA;MACA,KAAA/C,WAAA,CAAAI,UAAA;IACA;IACA4C,mBAAA,WAAAA,oBAAAnB,KAAA;MACA,KAAAoB,IAAA,MAAAzC,IAAA,iBAAAqB,KAAA;MACA,KAAArB,IAAA,CAAA0C,YAAA,OAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAAxB,KAAA;MACA,KAAAoB,IAAA,MAAAzC,IAAA,cAAAqB,KAAA;IACA;IACAyB,QAAA,WAAAA,SAAAzB,KAAA;MACA,KAAAoB,IAAA,MAAAzC,IAAA,cAAAqB,KAAA;IACA;IACA0B,WAAA,WAAAA,YAAA1B,KAAA;MACA,KAAAoB,IAAA,MAAAzC,IAAA,cAAAqB,KAAA;IACA;IACA2B,aAAA,WAAAA,cAAA3B,KAAA;MACA4B,OAAA,CAAAC,GAAA,CAAA7B,KAAA;MACA,KAAAoB,IAAA,MAAAzC,IAAA,gBAAAqB,KAAA;IACA;IAEA,aACAZ,OAAA,WAAAA,QAAA;MAAA,IAAA0C,MAAA;MACA,KAAArE,OAAA;MACA,IAAAsE,kBAAA,OAAA5D,WAAA,EAAAmB,IAAA,WAAAC,QAAA;QACAuC,MAAA,CAAA/D,SAAA,GAAAwB,QAAA,CAAAC,IAAA;QACAsC,MAAA,CAAAhE,KAAA,GAAAyB,QAAA,CAAAzB,KAAA;QACAgE,MAAA,CAAArE,OAAA;MACA;IACA;IACA;IACAuE,MAAA,WAAAA,OAAA;MACA,KAAA9D,IAAA;MACA,KAAA+D,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtD,IAAA;QACA8B,EAAA;QACAnC,SAAA;QACAC,UAAA;QACAC,YAAA;QACA0D,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACA7D,SAAA;QACA8D,UAAA;QACAC,UAAA;QACAC,QAAA;QACAxB,YAAA;MACA;MACA,KAAAyB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAxE,WAAA,CAAAC,OAAA;MACA,KAAAgB,OAAA;IACA;IACA,aACAwD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApF,GAAA,GAAAoF,SAAA,CAAAC,GAAA,WAAAvC,IAAA;QAAA,OAAAA,IAAA,CAAAC,EAAA;MAAA;MACA,KAAA9C,MAAA,GAAAmF,SAAA,CAAA7C,MAAA;MACA,KAAArC,QAAA,IAAAkF,SAAA,CAAA7C,MAAA;IACA;IACA,aACA+C,SAAA,WAAAA,UAAA;MACA,KAAAf,KAAA;MACA,KAAA/D,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAgF,YAAA,WAAAA,aAAApC,GAAA;MAAA,IAAAqC,MAAA;MACA,KAAAjB,KAAA;MACA,IAAAxB,EAAA,GAAAI,GAAA,CAAAJ,EAAA,SAAA/C,GAAA;MACA,IAAAyF,eAAA,EAAA1C,EAAA,EAAAnB,IAAA,WAAAC,QAAA;QACA2D,MAAA,CAAAvE,IAAA,GAAAY,QAAA,CAAAnC,IAAA;QACA8F,MAAA,CAAAhF,IAAA;QACAgF,MAAA,CAAAjF,KAAA;QAEA,IAAAiF,MAAA,CAAAvE,IAAA,CAAAJ,UAAA,IAAA2E,MAAA,CAAAvE,IAAA,CAAAuB,OAAA;UACAgD,MAAA,CAAAvE,IAAA,CAAAyB,QAAA,IAAA8C,MAAA,CAAAvE,IAAA,CAAAJ,UAAA,EAAA2E,MAAA,CAAAvE,IAAA,CAAAuB,OAAA;QACA,WAAAgD,MAAA,CAAAvE,IAAA,CAAAJ,UAAA;UACA2E,MAAA,CAAAvE,IAAA,CAAAyB,QAAA,IAAA8C,MAAA,CAAAvE,IAAA,CAAAJ,UAAA;QACA;UACA2E,MAAA,CAAAvE,IAAA,CAAAyB,QAAA;QACA;QAEAwB,OAAA,CAAAC,GAAA,WAAAqB,MAAA,CAAAvE,IAAA,CAAAyB,QAAA;QACA8C,MAAA,CAAAlE,YAAA,CAAAsB,OAAA,WAAAO,GAAA;UACA,IAAAqC,MAAA,CAAAvE,IAAA,CAAAJ,UAAA,IAAAsC,GAAA,CAAAJ,EAAA;YACAyC,MAAA,CAAAvE,IAAA,CAAAsC,YAAA,GAAAJ,GAAA,CAAAI,YAAA;UACA;QACA;QAEAiC,MAAA,CAAA3F,OAAA,GAAA2F,MAAA,CAAA3F,OAAA,CAAAwF,GAAA,WAAAK,MAAA;UACA,WAAAC,cAAA,CAAA/F,OAAA,MAAA+F,cAAA,CAAA/F,OAAA,MACA8F,MAAA;YACAxC,QAAA,EAAAwC,MAAA,CAAAxC,QAAA,CAAAmC,GAAA,WAAAO,KAAA;cACA,WAAAD,cAAA,CAAA/F,OAAA,MAAA+F,cAAA,CAAA/F,OAAA,MACAgG,KAAA;gBACAC,QAAA,EAAAD,KAAA,CAAAtD,KAAA,KAAAkD,MAAA,CAAAvE,IAAA,CAAA8B;cAAA;YAEA;UAAA;QAEA;MACA;IACA;IACA,WACA+C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAjG,eAAA,GAAAiG,MAAA,CAAAI,QAAA;YACAC,IAAA;YACAC,IAAA;YACAC,UAAA;UACA;UACA,IAAAP,MAAA,CAAA9E,IAAA,CAAA8B,EAAA;YACA,IAAAwD,kBAAA,EAAAR,MAAA,CAAA9E,IAAA,EACAW,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAAS,UAAA;cACAT,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAArE,OAAA;cACAqE,MAAA,CAAAhE,gBAAA;cACAgE,MAAA,CAAAjG,eAAA,CAAA2G,KAAA;YACA,GACAC,KAAA,WAAAC,CAAA;cACA,IAAAZ,MAAA,CAAAjG,eAAA,UAAAiG,MAAA,CAAAjG,eAAA,CAAA2G,KAAA;YACA;UACA;YACA,IAAAG,eAAA,EAAAb,MAAA,CAAA9E,IAAA,EACAW,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAAS,UAAA;cACAT,MAAA,CAAAvF,IAAA;cACAuF,MAAA,CAAArE,OAAA;cACAqE,MAAA,CAAAhE,gBAAA;cACAgE,MAAA,CAAAjG,eAAA,CAAA2G,KAAA;YACA,GACAC,KAAA,WAAAC,CAAA;cACA,IAAAZ,MAAA,CAAAjG,eAAA,UAAAiG,MAAA,CAAAjG,eAAA,CAAA2G,KAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAI,YAAA,WAAAA,aAAA1D,GAAA;MAAA,IAAA2D,MAAA;MACA,IAAA9G,GAAA,GAAAmD,GAAA,CAAAJ,EAAA,gBAAA/C,GAAA,IAAAmD,GAAA,CAAAJ,EAAA;MACA,IAAAgE,KAAA,QAAA1G,SAAA,CACA2G,MAAA,WAAAlE,IAAA;QAAA,OAAA9C,GAAA,CAAAiH,QAAA,CAAAnE,IAAA,CAAAC,EAAA;MAAA,GACAsC,GAAA,WAAAvC,IAAA;QAAA,OAAAA,IAAA,CAAA/B,SAAA;MAAA,GACAmG,IAAA;MAEA,KAAAC,QAAA,kBAAAJ,KAAA;QACAK,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACA1F,IAAA;QACA,WAAA2F,iBAAA,EAAAvH,GAAA;MACA,GACA4B,IAAA,WAAAC,QAAA;QACA,WAAA2F,eAAA,EAAAxH,GAAA;MACA,GACA4B,IAAA;QACAkF,MAAA,CAAApF,OAAA;QACAoF,MAAA,CAAAN,UAAA;MACA;IACA;IACA,aACAiB,YAAA,WAAAA,aAAA;MACA,IAAAC,IAAA;MACA,IAAAC,YAAA,OAAAhC,cAAA,CAAA/F,OAAA,MAAA+F,cAAA,CAAA/F,OAAA,MACA8H,IAAA,CAAAjH,WAAA;QACAmH,YAAA,EAAAF,IAAA,CAAA1H,GAAA,CAAAuC,MAAA,GAAAmF,IAAA,CAAA1H,GAAA;MAAA,EACA;MACA,IAAA6H,GAAA;MACA,IAAAH,IAAA,CAAA1H,GAAA,CAAAuC,MAAA;QACAsF,GAAA;MACA;QACAA,GAAA;MACA;MACA,KAAAV,QAAA,CAAAU,GAAA;QACAT,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACA1F,IAAA;QACA8F,IAAA,CAAAI,QAAA,CACA,uBACAH,YAAA,WAAAI,MAAA,CACA,IAAAnE,IAAA,GAAAC,OAAA,YACA;MACA,GACA6C,KAAA;IACA;EACA;AACA", "ignoreList": []}]}