<template>
    <div class="app-container">
        <!-- <div>
            <div class="classftion-box">
                <div class="classftion-right-box">
                    <div class="classftion-list">
                        <div class="classftion-list-sum">
                            <div class="classftion-list-sum-title">总部数据</div>
                            <div style="float:left;width:336px;height:100%;">
                                <img src="@/assets/images/index/headquarters.png" alt="" />
                                <div class="classftion-list-item">
                                    <div style="font-size: 24px;font-weight: 800;color: #1366BF;">总部</div>
                                    <div style="font-size:28px;color:#30D4A9;font-weight: 800">{{ headquarters }}</div>
                                </div>
                            </div>

                            <div class="classftion-list-zhengqi">
                                <div class="classftion-list-tltle" style="line-height:70px">
                                    政企专业公司
                                </div>
                                <div class="classftion-list-item" v-for="(i, num) in list2" :key="num">
                                    <div style="font-size: 24px;font-weight: 800;color: #1366BF;">{{ i.provinceName }}</div>
                                    <div style="font-size:28px;color:#30D4A9;font-weight: 800">{{ i.peopleNumber }}</div>
                                </div>
                            </div>
                        </div>
                        <div v-for="(item, index) in list1" class="classftion-list2" :key="index">
                            <div class="classftion-list-tltle">
                                省份列表
                            </div>
                            <div class="classftion-list-item" v-for="(i, num) in item" :key="num">
                                <div style="font-size: 24px;font-weight: 800;color: #1366BF;">{{ i.provinceName }}</div>
                                <div style="font-size:28px;color:#30D4A9;font-weight: 800">{{ i.peopleNumber }}</div>
                            </div>
                        </div>

                        <div class="classftion-list2">
                            <div class="classftion-list-tltle">
                                其它专业公司
                            </div>
                            <div class="classftion-list-item" v-for="(i, num) in list3" :key="num">
                                <div style="font-size: 24px;font-weight: 800;color: #1366BF;">{{ i.provinceName }}</div>
                                <div style="font-size:28px;color:#30D4A9;font-weight: 800">{{ i.peopleNumber }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> -->
        <div class="classftion-box">
            <div class="headquarters">
                <div class="headquarters-tltie ">{{ isSecondaryAdministrator? this.region:'总部' }}数据</div>
                <div class="headquarters-box">
                    <img style="width: 74px;" src="@/assets/images/index/headquarters.png" alt="" />
                    <div>
                        <div class="headquarters-tltie" style="margin-top: 0;text-align: center;">{{ isSecondaryAdministrator? this.region:'总部' }}</div>
                        <div class="headquarters-num">{{ headquarters }}</div>
                    </div>
                </div>
            </div>
            <div style="margin: 0 56px 0 22px;">
                <!-- <div class="headquarters-tltie ">政企专业公司</div> -->
                <myLine v-if="list2.length" :width="'488px'" :height="'257px'" :title="'政企专业公司'" :List="list2"></myLine>
            </div>
            <div>
                <!-- <div class="headquarters-tltie ">其他专业公司</div> -->
                <myLine v-if="list3.length" :width="'800px'" :height="'257px'" :title="'其他专业公司'" :List="list3"></myLine>
            </div>
        </div>
        <div>
            <myLine v-if="list1.length" :width="'1580px'" :height="'257px'" :title="'省份列表'" :List="list1"></myLine>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
import { clicksumusercount, clickSumusercountExport,provincialSecondaryAdministrator } from '@/api/system/sum.js'
import myLine from './myLine.vue'
import { Loading } from 'element-ui'
// clicksumusercount
export default {
    name: 'Config',
    components: {
        myLine
    },
    data() {
        return {
            // dayDate: [],
            list1: [],
            list2: [],
            list3: [],
            headquarters: 0,
            queryParams: {},
            isSecondaryAdministrator: false,
            region: '',
        }
    },
    props: {
        dayDate: {
            type: Array,
            default: []
        }
    },
    watch: {
        // 监听 Date 数组的变化
        dayDate: {
            handler() {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
                this.getclicksumusercount()
            },
            immediate: true, // 初始化时立即执行一次
            deep: true // 深度监听
        }
    },
    created() {
        setScale()
        window.onresize = () => {
            setScale()
        }
        // this.getclicksumusercount()
        this.myProvincialSecondaryAdministrator();
    },
    methods: {
        myProvincialSecondaryAdministrator(){
            provincialSecondaryAdministrator().then(res=>{
            this.isSecondaryAdministrator = res.data.isSecondaryAdministrator;
            this.region = res.data.region;
            if(this.isSecondaryAdministrator && res.data.region){
                this.queryParams.region = res.data.region;
            }
        });
        },
        handleExport() {
            let that = this
            this.queryParams.params = {}
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            this.$confirm('是否确认导出各省用户数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    that.download(
                        'system/clicksum/usercountexport',
                        {
                            ...that.queryParams
                        },
                        `usercount_${new Date().getTime()}.xlsx`
                    )
                })
                .catch({})
        },

        getclicksumusercount() {
            let that = this
            that.list1 = []
            that.list2 = []
            that.list3 = []
            that.headquarters = 0
            this.queryParams.params = {}
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }

            if(this.isSecondaryAdministrator && this.region){
                this.queryParams.region = this.region;
            }
            clicksumusercount(this.queryParams).then(res => {
                res.province.forEach(element => {
                    element.peopleNumber = 0
                    res.datalist.forEach(item => {
                        if (element.provinceName === item.provinceName) {
                            element.peopleNumber = item.peopleNumber
                        }
                    })
                })

                //数据为0的省份
                // if (isaddList.length > 0) {
                //   isaddList.forEach(element => {
                //     res.datalist.push({ peopleNumber: 0, provinceName: element.provinceName, provinceType: element.provinceType })
                //   });
                // }
                res.datalist.forEach(item => {
                    if (item.provinceName === '总部') {
                        that.headquarters = item.peopleNumber
                    }

                    if(this.isSecondaryAdministrator && this.region === item.provinceName){
                        that.headquarters = item.peopleNumber
                    }

                })

                let list1 = []
                res.province.forEach(element => {
                    if (element.provinceType === 0) list1.push(element)
                    if (element.provinceType === 1) that.list2.push(element)
                    if (element.provinceType === 2) that.list3.push(element)
                })

                // const len = list1.length //len为数组的长度
                // const n = 12 // 假设每行显示12个
                // const Num = len % 12 === 0 ? len / 12 : Math.floor(len / 12 + 1) //得出多少份
                // const res1 = [] //定义数组接受最终的数据
                // for (let i = 0; i < Num; i++) {
                //     // slice() 方法返回一个从开始到结束（不包括结束）的数组。且原始数组不会被修改。
                //     const newArr = list1.slice(i * n, i * n + n) //得到每份的数据
                //     res1.push(newArr) //往res数组里加数据
                // }
                this.list1 = list1
                // console.log(this.list2, res1)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;

    height: 560px;
    border-radius: 10px;
    padding: 25px 20px;
    box-sizing: border-box;
    background: #ffffff;

    .classftion-box {
        display: flex;

        .headquarters-tltie {
            font-size: 18px;
            margin-bottom: 18px;
        }

        .headquarters-box {
            display: flex;
            align-items: center;
            justify-content: space-around;

            width: 211px;
            height: 132px;
            border: 1px solid transparent;
            /* 设置边框宽度 */
            position: relative;
            border-image: linear-gradient(to right, #68e5fa, #013fff) 1;
            /* 添加渐变边框 */
            border-radius: 10px;

            .headquarters-num {
                font-size: 36px;
                color: #2269de;
                font-weight: 900;
            }
        }
    }
}

::deep .el-button--warning .is-plain {
    height: 40px;
}

::v-deep .el-input__inner {
    border: 0;
    height: 40px;
}

::v-deep .el-range-editor--medium .el-range__icon,
.el-range-editor--medium .el-range__close-icon {
    display: none;
}

::v-deep .el-date-editor .el-range__icon {
    line-height: 30px;
}

::v-deep .el-date-editor .el-range-separator {
    padding: 0;
}

::v-deep .el-date-editor .el-range__close-icon {
    display: none;
}

::v-deep .el-date-editor .el-range-input {
    width: 48%;
    font-size: 20px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-range-separator {
    font-size: 20px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-date-editor>input:-moz-placeholder {
    color: #1366bf;
}

::v-deep .el-date-editor>input:-ms-input-placeholder {
    color: #1366bf;
}

::v-deep .el-date-editor>input::-webkit-input-placeholder {
    color: #1366bf;
}
</style>
