<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";

require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

const animationDuration = 6000;

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "1120px",
    },
    height: {
      type: String,
      default: "280px",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {},
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart(echartXData, echartData, echartNumData) {
      let that = this;
      this.chart = echarts.init(this.$el);

      this.$nextTick(() => {
        this.chart.setOption({
          title: { show: false },
          backgroundColor: "rgba(255, 255, 255, 1)",
          tooltip: {
            trigger: "axis",
            backgroundColor: "#FFFFFF", // 修改背景颜色
            borderColor: "#000000", // 修改字体颜色
            formatter: function (params) {
              //使用formatter函数修改需要的样式
              let res =
                "<div style='color: #1366bf;font-size: 18px;font-weight: 800;'>" +
                params[0].axisValue +
                "<br/>人数：" +
                params[1].value +
                "<br/>人次：" +
                params[0].value +
                "</div>";
              console.log(params);
              return res;
            },
          },
          xAxis: {
            boundaryGap: false, // 显示间距设置为false
            type: "category",
            data: echartXData,
            axisLabel: {
              //x轴文字的配置
              show: true,
              fontWeight: 800,
              margin: 10, //刻度标签与轴线之间的距离
              textStyle: {
                color: "#1366BF", // 修改 x 轴文字颜色
              },
            },
            axisTick: {
              show: false, //不显示坐标轴刻度线
            },
            axisLine: {
              lineStyle: {
                color: "#fff", //x轴轴线颜色
              },
            },
            splitLine: {
              //网格线
              show: false,
            },
          },
          grid: {
            top: 40,
            bottom: 40,
            left: "5%", // 左边距
            right: "5%", // 右边距
          },
          yAxis: {
            type: "value",
            axisTick: {
              show: false, //不显示坐标轴刻度线
            },
            axisLine: {
              lineStyle: {
                color: "#e8e8e8", //x轴轴线颜色
              },
            },
            splitLine: {
              //网格线
              show: false,
            },
            axisLabel: {
              //x轴文字的配置
              show: true,
              fontWeight: 800,
              textStyle: {
                color: "#1366BF",
              },
            },
          },
          series: [
            {
              data: echartData,
              type: "line",
              symbol: "circle", // 使用空心的圆点
              symbolSize: 8, // 圆点的大小
              itemStyle: {
                normal: {
                  lineStyle: {
                    color: "#bedbff", //改变折线颜色
                  },
                  color: "#bedbff", // 圆点的颜色
                },
              },
              label: {
                show: true,
                position: "top", // 文字显示在节点的上方
                color: "#333333", // 文字颜色
                fontWeight: 800,
                fontSize: 14, // 文字大小
              },
            },
            {
              data: echartNumData,
              type: "line",
              symbol: "circle", // 使用空心的圆点
              symbolSize: 8, // 圆点的大小
              itemStyle: {
                normal: {
                  lineStyle: {
                    color: "#bedbff", //改变折线颜色
                  },
                  color: "#bedbff", // 圆点的颜色
                },
              },
              label: {
                show: true,
                position: "top", // 文字显示在节点的上方
                color: "#333333", // 文字颜色
                fontWeight: 800,
                fontSize: 14, // 文字大小
              },
            },
          ],
        });
      });
    },
  },
};
</script>
