<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业场景" prop="sceneId">
        <el-cascader
          v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear"> <!-- 监听清除事件 -->
        </el-cascader>
      </el-form-item>
      <el-form-item label="问题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入问题" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="关键字" prop="type">
        <el-input maxlength="150" v-model="queryParams.type" size="small" placeholder="请输入关键字" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:question:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:question:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:question:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:question:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionList" @selection-change="handleSelectionChange" :show-overflow-tooltip='true'>
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="行业名称" align="center" prop="industryName" />
      <el-table-column label="场景名称" align="center" prop="sceneName" />
      <el-table-column label="关键字" align="center" prop="type" />
      <el-table-column label="问题" align="center" prop="title">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.title"
            raw-content
            placement="top-start"
            v-if="scope.row.title"
          >
            <span v-if="scope.row.title && scope.row.title.length <= 30">
               {{ scope.row.title }}
          </span>
            <span v-if="scope.row.title && scope.row.title.length > 30">
               {{ scope.row.title.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.title== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="答案" align="center" prop="answer">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.answer"
            raw-content
            placement="top-start"
            v-if="scope.row.answer"
          >
            <span v-if="scope.row.answer && scope.row.answer.length <= 30">
               {{ scope.row.answer }}
          </span>
            <span v-if="scope.row.answer && scope.row.answer.length > 30">
               {{ scope.row.answer.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.configValue== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="点击次数" align="center" prop="clickCount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:question:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:question:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改问题对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="场景" prop="sceneId">
          <el-cascader v-model="form.sceneIds" :options="options" @change="handleChange"></el-cascader>
        </el-form-item>
        <el-form-item label="关键字" prop="type">
          <el-input maxlength="100" v-model="form.type" placeholder="请输入关键字" show-word-limit/>
        </el-form-item>
        <el-form-item label="问题" prop="title">
          <el-input maxlength="200" v-model="form.title" placeholder="请输入问题" show-word-limit/>
        </el-form-item>
        <el-form-item label="答案" prop="answer">
          <el-input v-model="form.answer" maxlength="2000" show-word-limit type="textarea" rows="15" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion, exportQuestion } from "@/api/system/question";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";
export default {
  name: "Question",
  components: {
  },
  data() {
    return {
      // 类型字典
      typeOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 问题表格数据
      questionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        answer: null,
        type: null,
        clickCount: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sceneId: [
          { required: true, message: "场景不能为空", trigger: "change" }
        ],
        type: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
        title: [
          { required: true, message: "问题不能为空", trigger: "blur" }
        ],
        answer: [
          { required: true, message: "答案不能为空", trigger: "blur" }
        ]
      },
      //行业列表
      industryList: [],
      //场景列表
      sceneList: [],
      options: [],
      selectItem: {},//选中的item
    };
  },
  created() {
    this.getList();
    this.getDicts("qx_question_type").then(response => {
      this.typeOptions = response.data;

    });
    listIndustry().then(response => {
      this.industryList = response.rows;
      listScene().then(response => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });
  },
  methods: {
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.queryParams.industryId = value[0];
      };

      if (value != null || value.length > 1) {
        this.queryParams.sceneId = value[1];
      };

    },
    //选中
    handleChange(value) {
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach(row => {
        if (row.id === value[1]) {
          this.selectItem = row;
        }
      });
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach(element => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: []
        }
        this.sceneList.forEach(row => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName
            });
          }
        });
        this.options.push(item);
      });
    },
    /** 查询问题列表 */
    getList() {
      this.loading = true;
      listQuestion(this.queryParams).then(response => {
        this.questionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        answer: null,
        type: null,
        clickCount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        industryId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.sceneIds = [];
      this.queryParams.industryId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加问题";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let that = this;
      this.reset();
      const id = row.id || this.ids
      getQuestion(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改问题";
        that.sceneList.forEach(row => {
          if (row.id === that.form.sceneId) {
            that.selectItem = row;
            that.form.sceneIds = [row.industryId, row.id];
            console.log(that.form.sceneIds);
          }
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateQuestion(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQuestion(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.questionList
        .filter(item => ids.includes(item.id))
        .map(item => item.title)
        .join(', ');

      this.$confirm('是否确认删除问题为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delQuestion(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的问题数据';
      }else {
        msg = '是否确认导出所有的问题数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/question/export', exportParams, `question_${new Date().getTime()}.xlsx`)
      }).catch({});
    }
  }
};
</script>
