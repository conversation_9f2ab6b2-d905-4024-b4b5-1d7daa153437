<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="省份" prop="provinceName">
        <el-input v-model="queryParams.provinceName" placeholder="请输入省份" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="类别" prop="clickType">
        <el-select v-model="queryParams.clickTypeList" multiple collapse-tags placeholder="请选择点击类别" clearable size="small">
          <el-option v-for="option in titleOptions" :key="option.id" :value="option.id" :label="option.name" />
        </el-select>
      </el-form-item>

      <el-form-item label="行业" prop="clickValue">
        <el-select
         size="small"
          v-model="queryParams.industryCodeList"
          placeholder="请选择行业"
          clearable
          @clear="handleIndustryClear">
          <el-option
            v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
          </el-option>
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item> -->

      <el-form-item label="终端" prop="isMobile">
        <el-select v-model="queryParams.isMobile" collapse-tags placeholder="请选择注册终端" clearable size="small">
          <el-option :key="-1" label="全部" :value="-1"></el-option>
          <el-option v-for="dict in formOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>

        <!-- <el-select v-model="queryParams.isMobile" size="small" placeholder="请选择终端">
          <el-option :key="2" label="全部" :value="2"></el-option>
          <el-option :key="0" label="PC端" :value="0"></el-option>
          <el-option :key="1" label="移动端" :value="1"></el-option>
        </el-select> -->
      </el-form-item>

      <el-form-item label="开始时间" prop="createTime">
        <el-date-picker v-model="createTime" type="datetimerange" size="small" :default-time="['00:00:00', '23:59:59']"
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:records:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="省份" align="center" prop="provinceName" width="140"></el-table-column>
      <el-table-column label="点击类别" align="center" prop="clickType">
        <template slot-scope="scope">
          <span>{{setClickType(scope.row.clickType)}}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="行业" align="center" prop="industryName" width="140"></el-table-column>
      <el-table-column label="场景" align="center" prop="sceneName" width="140"></el-table-column>
      <el-table-column label="合作伙伴" align="center" prop="companyName" width="180"></el-table-column>
      <el-table-column label="姓名" align="center" prop="nickname" width="140"></el-table-column>
      <el-table-column label="开始时间" align="center" prop="createTime" width="180"></el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180"></el-table-column>
      <el-table-column label="停留时长(秒)" align="center" prop="timeLength" />
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

  </div>
</template>

<script>
import { listRecords, delRecords, exportRecords } from "@/api/system/records";
import { listIndustry } from "@/api/system/industry";
import { Loading } from 'element-ui'

export default {
  name: "Records",
  components: {
  },
  data() {
    return {
      formOptions:[],
      industryList: [],
      createTime: [],
      titleOptions: [{ id: 0, name: 'ishow首页' }, { id: 1, name: '行业访客' }, { id: 2, name: '场景访客' }, { id: 4, name: '网络方案访客' }, { id: 5, name: '商业价值访客' }, { id: 6, name: '落地案例访客' },
      { id: 7, name: 'VR看现场访客' }, { id: 8, name: '集成报价访客' }, { id: 9, name: '行业生态链访客' }, { id: 10, name: '视频讲解' }, { id: 11, name: '合作伙伴' }, { id: 12, name: '产品介绍' }, { id: 13, name: '基础能力' }],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 点击记录表格数据
      recordsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ip: null,
        clickType: null,
        childType: null,
        clickValue: null,
        provinceCode: null,
        cityCode: null,
        customerId: null,
        endTime: null,
        timeLength: null,
        nickname: null,
        isMobile: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("channel_type").then(response => {
      this.formOptions = response.data;
    });
    
    listIndustry().then(response => {
      this.industryList = response.rows;
    });
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    setClickType(type) {
      let name = "";
      for (let i = 0; i < this.titleOptions.length; i++) {
        if (this.titleOptions[i].id == type)
          name = this.titleOptions[i].name;
      }
      return name;
    },
    /** 查询点击记录列表 */
    getList() {
      this.loading = true;
      listRecords(this.queryParams).then(response => {
        this.recordsList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.createTime.length > 0) {
        this.queryParams.params = {};
        this.queryParams.params.createTime = this.parseTime(this.createTime[0], '{y}-{m}-{d} {h}:{i}:{s}');
        this.queryParams.params.endTime = this.parseTime(this.createTime[1], '{y}-{m}-{d} {h}:{i}:{s}');
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        ip: null,
        clickType: null,
        childType: null,
        clickValue: null,
        provinceCode: null,
        cityCode: null,
        customerId: null,
        endTime: null,
        timeLength: null,
        nickname: null,
        isMobile: null,
        clickTypeList: [],
        industryCodeList: [],
      };
      this.createTime = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除点击记录编号为"' + ids + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delRecords(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的点击记录数据项';
      }else {
        msg = '是否确认导出所有的点击记录数据项';
      }
      this.$confirm('是否确认导出所筛选或选中的点击记录数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/records/export', exportParams, `records_${new Date().getTime()}.xlsx`)
      }).catch({});
    }
  }
};
</script>
