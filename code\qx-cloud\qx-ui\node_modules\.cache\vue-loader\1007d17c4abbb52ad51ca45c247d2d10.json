{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\scene\\scene.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\scene\\scene.vue", "mtime": 1755684666418}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747036191864}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747036191888}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgbGlzdFNjZW5lLAogIGxpc3RTY2VuZVYyLAogIGdldFNjZW5lLAogIGRlbFNjZW5lLAogIGFkZFNjZW5lLAogIHVwZGF0ZVNjZW5lLAogIGV4cG9ydFNjZW5lLAogIGNoZWNrU2NlbmUsCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3NjZW5lIjsKCmltcG9ydCBQYW5vcmFtYVVwbG9hZCBmcm9tICIuLi8uLi8uLi9jb21wb25lbnRzL1Bhbm9yYW1hVXBsb2FkIjsKaW1wb3J0IEltYWdlVXBsb2FkIGZyb20gIi4uLy4uLy4uL2NvbXBvbmVudHMvSW1hZ2VVcGxvYWQiOwppbXBvcnQgVmlkZW9VcGxvYWQgZnJvbSAiLi4vLi4vLi4vY29tcG9uZW50cy9JbWFnZVVwbG9hZC9JbWFnZVVwbG9hZC52dWUiOwppbXBvcnQgeyBsaXN0SW5kdXN0cnkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vaW5kdXN0cnkiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJTY2VuZSIsCiAgY29tcG9uZW50czogewogICAgSW1hZ2VVcGxvYWQsCiAgICBWaWRlb1VwbG9hZCwKICAgIFBhbm9yYW1hVXBsb2FkLAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG9wdGlvbnM6IFtdLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZ1Bhbm9yYW1hOiBudWxsLAogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgLy8g5oC75p2h5pWwCiAgICAgIHRvdGFsOiAwLAogICAgICAvLyDlnLrmma/ooajmoLzmlbDmja4KICAgICAgc2NlbmVMaXN0OiBbXSwKICAgICAgdGFnVHlwZUxpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHNjZW5lTmFtZTogbnVsbCwKICAgICAgICBpbmR1c3RyeUlkOiBudWxsLAogICAgICAgIHNjZW5lRXhwbGFpbjogbnVsbCwKICAgICAgICBzY2VuZUNvZGU6IG51bGwsCiAgICAgICAgdHJnQ29kZTogbnVsbCwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBzY2VuZU5hbWU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwKICAgICAgICBdLAogICAgICAgIHNjZW5lQ29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue8lueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgICAgaW5kdXN0cnlJZDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnuihjOS4muS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIC8v6KGM5Lia5YiX6KGoCiAgICAgIGluZHVzdHJ5TGlzdDogW10sCiAgICAgIHNjZW5lTGlzdFZvOiBbXSwKICAgICAgb3B0aW9uczpbXQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICAgIGxpc3RJbmR1c3RyeSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgIHRoaXMuaW5kdXN0cnlMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgIH0pOwoKICAgIHRoaXMuZ2V0SW5kdXN0cnlTY2VuZSgpOwoKICAgIHRoaXMuZ2V0RGljdHMoInRhZ190eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgdGhpcy50YWdUeXBlTGlzdCA9IHJlc3BvbnNlLmRhdGE7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldEluZHVzdHJ5U2NlbmUoKSB7CiAgICAgIGxpc3RJbmR1c3RyeSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5pbmR1c3RyeUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIGxpc3RTY2VuZSgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICB0aGlzLnNjZW5lTGlzdFZvID0gcmVzcG9uc2Uucm93czsKICAgICAgICAgIHRoaXMuc2V0T3B0aW9uKCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6YCJ5LitCiAgICBxdWVyeVBhcmFtc0hhbmRsZUNoYW5nZSh2YWx1ZSkgewogICAgICBpZiAodmFsdWUgIT0gbnVsbCB8fCB2YWx1ZS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5mb3JtLmluZHVzdHJ5SWQgPSB2YWx1ZVswXTsKICAgICAgfQoKICAgICAgaWYgKHZhbHVlICE9IG51bGwgfHwgdmFsdWUubGVuZ3RoID4gMSkgewogICAgICAgIHRoaXMuZm9ybS5wYXJhbUlkID0gdmFsdWVbMV07CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVDbGVhcigpIHsKICAgICAgLy8g5riF6Zmk5Zy65pmv5pe25omn6KGM55qE5pON5L2cCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2NlbmVJZHMgPSBbXTsgLy8g5riF56m66YCJ5Lit55qE5Zy65pmvCiAgICB9LAogICAgLy/orr7nva7ooYzkuJrlnLrmma/kuIvmi4kKICAgIHNldE9wdGlvbigpIHsKICAgICAgdGhpcy5pbmR1c3RyeUxpc3QuZm9yRWFjaCgoZWxlbWVudCkgPT4gewogICAgICAgIGxldCBpdGVtID0gewogICAgICAgICAgdmFsdWU6IGVsZW1lbnQuaWQsCiAgICAgICAgICBsYWJlbDogZWxlbWVudC5pbmR1c3RyeU5hbWUsCiAgICAgICAgICBjaGlsZHJlbjogW10sCiAgICAgICAgfTsKICAgICAgICB0aGlzLnNjZW5lTGlzdFZvLmZvckVhY2goKHJvdykgPT4gewogICAgICAgICAgaWYgKHJvdy5pbmR1c3RyeUlkID09PSBlbGVtZW50LmlkKSB7CiAgICAgICAgICAgIGl0ZW0uY2hpbGRyZW4ucHVzaCh7CiAgICAgICAgICAgICAgdmFsdWU6IHJvdy5pZCwKICAgICAgICAgICAgICBsYWJlbDogcm93LnNjZW5lTmFtZSwKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5vcHRpb25zLnB1c2goaXRlbSk7CiAgICAgIH0pOwogICAgfSwKCiAgICBzZXRJbmR1c3RyeWNvZGUoKSB7CiAgICAgIHRoaXMuaW5kdXN0cnlMaXN0LmZvckVhY2goKHJvdykgPT4gewogICAgICAgIGlmICh0aGlzLmZvcm0uaW5kdXN0cnlJZCA9PSByb3cuaWQpIHsKICAgICAgICAgIHRoaXMuZm9ybS5pbmR1c3RyeUNvZGUgPSByb3cuaW5kdXN0cnlDb2RlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlSW5kdXN0cnlDbGVhcigpIHsKICAgICAgLy8g5riF6Zmk6KGM5Lia6YCJ5oup5pe25omn6KGM55qE5pON5L2cCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuaW5kdXN0cnlJZCA9IG51bGw7IC8vIOmHjee9ruihjOS4mklECiAgICB9LAogICAgc2V0UGFub3JhbWFVcmxJbnB1dCh2YWx1ZSkgewogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAicGFub3JhbWFVcmwiLCB2YWx1ZSk7CiAgICAgIHRoaXMuZm9ybS5wYW5vcmFtYVRpbWUgPSBuZXcgRGF0ZSgpLmdldFRpbWUoKTsKICAgIH0sCiAgICBzZXRJbnB1dCh2YWx1ZSkgewogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAic2NlbmVVcmwiLCB2YWx1ZSk7CiAgICB9LAogICAgc2V0Q292ZXIodmFsdWUpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImNvdmVyVXJsIiwgdmFsdWUpOwogICAgfSwKICAgIHNldFNoYXJlSW1nKHZhbHVlKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzaGFyZUltZyIsIHZhbHVlKTsKICAgIH0sCiAgICBzZXRTaGFyZVZlZGlvKHZhbHVlKSB7CiAgICAgIGNvbnNvbGUubG9nKHZhbHVlKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInNoYXJlVmVkaW8iLCB2YWx1ZSk7CiAgICB9LAoKICAgIC8qKiDmn6Xor6LlnLrmma/liJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxpc3RTY2VuZVYyKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5zY2VuZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWwoKSB7CiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICB9LAogICAgLy8g6KGo5Y2V6YeN572uCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiBudWxsLAogICAgICAgIHNjZW5lTmFtZTogbnVsbCwKICAgICAgICBpbmR1c3RyeUlkOiBudWxsLAogICAgICAgIHNjZW5lRXhwbGFpbjogbnVsbCwKICAgICAgICBjcmVhdGVCeTogbnVsbCwKICAgICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICAgIHVwZGF0ZUJ5OiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsCiAgICAgICAgc2NlbmVVcmw6IG51bGwsCiAgICAgICAgc2NlbmVDb2RlOiBudWxsLAogICAgICAgIHNjZW5lT3JkZXI6IG51bGwsCiAgICAgICAgc2hhcmVWZWRpbzogbnVsbCwKICAgICAgICBzaGFyZUltZzogbnVsbCwKICAgICAgICBpbmR1c3RyeUNvZGU6IG51bGwsCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlnLrmma8iOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICBjb25zdCBpZCA9IHJvdy5pZCB8fCB0aGlzLmlkczsKICAgICAgZ2V0U2NlbmUoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsKICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Zy65pmvIjsKCiAgICAgICAgaWYgKHRoaXMuZm9ybS5pbmR1c3RyeUlkICYmIHRoaXMuZm9ybS5wYXJhbUlkKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVJZHMgPSBbdGhpcy5mb3JtLmluZHVzdHJ5SWQsIHRoaXMuZm9ybS5wYXJhbUlkXTsKICAgICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5pbmR1c3RyeUlkKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVJZHMgPSBbdGhpcy5mb3JtLmluZHVzdHJ5SWRdOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVJZHMgPSBbXTsgLy8g56Gu5L+d5aeL57uI5piv5pWw57uECiAgICAgICAgfQoKICAgICAgICBjb25zb2xlLmxvZygic2Rmc2RmIiwgdGhpcy5mb3JtLnNjZW5lSWRzKTsKICAgICAgICB0aGlzLmluZHVzdHJ5TGlzdC5mb3JFYWNoKChyb3cpID0+IHsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW5kdXN0cnlJZCA9PSByb3cuaWQpIHsKICAgICAgICAgICAgdGhpcy5mb3JtLmluZHVzdHJ5Q29kZSA9IHJvdy5pbmR1c3RyeUNvZGU7CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIHRoaXMub3B0aW9ucyA9IHRoaXMub3B0aW9ucy5tYXAoKG9wdGlvbikgPT4gewogICAgICAgICAgcmV0dXJuIHsKICAgICAgICAgICAgLi4ub3B0aW9uLAogICAgICAgICAgICBjaGlsZHJlbjogb3B0aW9uLmNoaWxkcmVuLm1hcCgoY2hpbGQpID0+IHsKICAgICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgICAgLi4uY2hpbGQsCiAgICAgICAgICAgICAgICBkaXNhYmxlZDogY2hpbGQudmFsdWUgPT09IHRoaXMuZm9ybS5pZCwKICAgICAgICAgICAgICB9OwogICAgICAgICAgICB9KSwKICAgICAgICAgIH07CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm0oKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMubG9hZGluZ1Bhbm9yYW1hID0gdGhpcy4kbG9hZGluZyh7CiAgICAgICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgICAgIHRleHQ6ICLmlbDmja7kuIrkvKDlubbop6PmnpDlm77niYfkuK0uLi4iLAogICAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwKICAgICAgICAgIH0pOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7CiAgICAgICAgICAgIHVwZGF0ZVNjZW5lKHRoaXMuZm9ybSkKICAgICAgICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICAgICAgdGhpcy5nZXRJbmR1c3RyeVNjZW5lKCk7CiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmdQYW5vcmFtYS5jbG9zZSgpOwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgLmNhdGNoKChyKSA9PiB7CiAgICAgICAgICAgICAgICBpZiAodGhpcy5sb2FkaW5nUGFub3JhbWEgIT0gbnVsbCkgdGhpcy5sb2FkaW5nUGFub3JhbWEuY2xvc2UoKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFkZFNjZW5lKHRoaXMuZm9ybSkKICAgICAgICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICAgICAgdGhpcy5nZXRJbmR1c3RyeVNjZW5lKCk7CiAgICAgICAgICAgICAgICB0aGlzLmxvYWRpbmdQYW5vcmFtYS5jbG9zZSgpOwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgLmNhdGNoKChyKSA9PiB7CiAgICAgICAgICAgICAgICBpZiAodGhpcy5sb2FkaW5nUGFub3JhbWEgIT0gbnVsbCkgdGhpcy5sb2FkaW5nUGFub3JhbWEuY2xvc2UoKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsKICAgICAgY29uc3QgaWRzID0gcm93LmlkID09IG51bGwgPyB0aGlzLmlkcyA6IFtyb3cuaWRdOwogICAgICBjb25zdCBuYW1lcyA9IHRoaXMuc2NlbmVMaXN0CiAgICAgICAgLmZpbHRlcigoaXRlbSkgPT4gaWRzLmluY2x1ZGVzKGl0ZW0uaWQpKQogICAgICAgIC5tYXAoKGl0ZW0pID0+IGl0ZW0uc2NlbmVDb2RlKQogICAgICAgIC5qb2luKCIsICIpOwoKICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Zy65pmv57yW5Y+35Li6IicgKyBuYW1lcyArICci55qE5pWw5o2u6aG5PycsICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgfSkKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICByZXR1cm4gY2hlY2tTY2VuZShpZHMpOwogICAgICAgIH0pCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICByZXR1cm4gZGVsU2NlbmUoaWRzKTsKICAgICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgICB9KTsKICAgIH0sCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVFeHBvcnQoKSB7CiAgICAgIGxldCB0aGF0ID0gdGhpczsKICAgICAgY29uc3QgZXhwb3J0UGFyYW1zID0gewogICAgICAgIC4uLnRoYXQucXVlcnlQYXJhbXMsCiAgICAgICAgZXhwb3J0SWRMaXN0OiB0aGF0Lmlkcy5sZW5ndGggPyB0aGF0LmlkcyA6IG51bGwsIC8vIEFkZCBpZHMgaWYgYW55IGFyZSBzZWxlY3RlZAogICAgICB9OwogICAgICBsZXQgbXNnID0gIiI7CiAgICAgIGlmICh0aGF0Lmlkcy5sZW5ndGggPiAwKSB7CiAgICAgICAgbXNnID0gIuaYr+WQpuehruiupOWvvOWHuuaJgOetm+mAieaIlumAieS4reeahOWcuuaZr+aVsOaNriI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgbXNnID0gIuaYr+WQpuehruiupOWvvOWHuuaJgOacieeahOWcuuaZr+aVsOaNriI7CiAgICAgIH0KICAgICAgdGhpcy4kY29uZmlybShtc2csICLorablkYoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgfSkKICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICB0aGF0LmRvd25sb2FkKAogICAgICAgICAgICAic3lzdGVtL3NjZW5lL2V4cG9ydCIsCiAgICAgICAgICAgIGV4cG9ydFBhcmFtcywKICAgICAgICAgICAgYHNjZW5lXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgCiAgICAgICAgICApOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKHt9KTsKICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["scene.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "scene.vue", "sourceRoot": "src/views/system/scene", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"行业\" prop=\"industryId\">\n        <el-select v-model=\"queryParams.industryId\" placeholder=\"请选择行业\" size=\"small\" filterable clearable @clear=\"handleIndustryClear\">\n          <el-option v-for=\"item in industryList\" :key=\"item.id\" :label=\"item.industryName\" :value=\"item.id\">\n          </el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"场景名称\" prop=\"sceneName\">\n        <el-input v-model=\"queryParams.sceneName\" placeholder=\"请输入场景名称\" clearable size=\"small\" @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n      <el-form-item label=\"场景编码\" prop=\"sceneCode\">\n        <el-input v-model=\"queryParams.sceneCode\" placeholder=\"请输入场景编号\" clearable size=\"small\" @keyup.enter.native=\"handleQuery\" />\n      </el-form-item>\n\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['system:scene:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"success\" plain icon=\"el-icon-edit\" size=\"mini\" :disabled=\"single\" @click=\"handleUpdate\" v-hasPermi=\"['system:scene:edit']\">修改</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['system:scene:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\" v-hasPermi=\"['system:scene:export']\">导出</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"sceneList\" @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <!--       <el-table-column label=\"id\" align=\"center\" prop=\"id\" />-->\n      <el-table-column label=\"行业场景名称\" align=\"center\" prop=\"industryName\" >\n         <template #default=\"scope\">\n            {{ scope.row.industryName +'/'+ scope.row.paramName}}\n         </template>\n        </el-table-column>\n      <el-table-column label=\"场景名称\" align=\"center\" prop=\"sceneName\" />\n      <el-table-column label=\"场景编码\" align=\"center\" prop=\"sceneCode\" />\n      <el-table-column label=\"场景说明\" align=\"center\" prop=\"sceneExplain\">\n        <template #default=\"scope\">\n          <el-tooltip :content=\"scope.row.sceneExplain\" raw-content placement=\"top-start\" v-if=\"scope.row.sceneExplain\">\n            <span v-if=\"scope.row.sceneExplain && scope.row.sceneExplain.length <= 30\">\n              {{ scope.row.sceneExplain }}\n            </span>\n            <span v-if=\"scope.row.sceneExplain && scope.row.sceneExplain.length > 30\">\n              {{ scope.row.sceneExplain.substr(0, 30) + \"...\" }}\n            </span>\n          </el-tooltip>\n          <span v-else-if=\"scope.row.sceneExplain== null\"> </span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"排序号\" align=\"center\" prop=\"sceneOrder\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" v-hasPermi=\"['system:scene:edit']\">修改</el-button>\n          <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\" v-hasPermi=\"['system:scene:remove']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"queryParams.pageNum\" :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\n    <!-- 添加或修改场景对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"1000px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景名称\" prop=\"sceneName\">\n              <el-input v-model=\"form.sceneName\" maxlength=\"50\" show-word-limit placeholder=\"请输入场景名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景编码\" prop=\"sceneCode\">\n              <el-input v-model=\"form.sceneCode\" maxlength=\"50\" show-word-limit placeholder=\"请输入场景编码\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"行业场景\" prop=\"industryId\">\n              <el-cascader v-model=\"form.sceneIds\" size=\"small\" placeholder=\"请选择行业场景\" :props=\"{ checkStrictly: true }\" :options=\"options\" filterable clearable @change=\"queryParamsHandleChange\" @clear=\"handleClear\">\n              </el-cascader>\n              <!-- <el-select v-model=\"form.industryId\" placeholder=\"请选择行业\" onchange=\"setIndustrycode()\">\n                <el-option v-for=\"item in industryList\" :key=\"item.id\" :label=\"item.industryName\" :value=\"item.id\"></el-option>\n              </el-select> -->\n              <br />\n              (一级场景选择行业,二级场景选择父场景)\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"排序号\" prop=\"sceneOrder\">\n              <el-input-number v-model=\"form.sceneOrder\" :min=\"0\" :max=\"999999999\" label=\"请输入排序号\"></el-input-number>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"场景背景图\" prop=\"sceneUrl\">-->\n<!--              <ImageUpload @input=\"setInput\" :value=\"form.sceneUrl\" />-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景全景图\" prop=\"panoramaUrl\">\n              <PanoramaUpload @input=\"setPanoramaUrlInput\" :value=\"form.panoramaUrl\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"场景封面图\" prop=\"coverUrl\">\n              <ImageUpload @input=\"setCover\" :value=\"form.coverUrl\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n<!--        <el-row>-->\n<!--          <el-col :span=\"16\">-->\n<!--            <el-form-item label=\"分享视频\" prop=\"shareVedio\">-->\n<!--              <VideoUpload @input=\"setShareVedio\" tipContent=\"(备注:上传mp4文件,文件最大500M)\" accept=\".mp4\" :fileSize=\"500\" :value=\"form.shareVedio\" />-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--        </el-row>-->\n\n        <el-row>\n          <el-col :span=\"18\">\n            <el-form-item label=\"场景说明\" prop=\"sceneExplain\">\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"form.sceneExplain\" maxlength=\"200\" show-word-limit placeholder=\"请输入场景说明\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listScene,\n  listSceneV2,\n  getScene,\n  delScene,\n  addScene,\n  updateScene,\n  exportScene,\n  checkScene,\n} from \"@/api/system/scene\";\n\nimport PanoramaUpload from \"../../../components/PanoramaUpload\";\nimport ImageUpload from \"../../../components/ImageUpload\";\nimport VideoUpload from \"../../../components/ImageUpload/ImageUpload.vue\";\nimport { listIndustry } from \"@/api/system/industry\";\n\nexport default {\n  name: \"Scene\",\n  components: {\n    ImageUpload,\n    VideoUpload,\n    PanoramaUpload,\n  },\n  data() {\n    return {\n      options: [],\n      // 遮罩层\n      loadingPanorama: null,\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 场景表格数据\n      sceneList: [],\n      tagTypeList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        sceneName: null,\n        industryId: null,\n        sceneExplain: null,\n        sceneCode: null,\n        trgCode: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        sceneName: [\n          { required: true, message: \"名称不能为空\", trigger: \"blur\" },\n        ],\n        sceneCode: [\n          { required: true, message: \"编码不能为空\", trigger: \"blur\" },\n        ],\n        industryId: [\n          { required: true, message: \"所属行业不能为空\", trigger: \"blur\" },\n        ],\n      },\n      //行业列表\n      industryList: [],\n      sceneListVo: [],\n      options:[]\n    };\n  },\n  created() {\n    this.getList();\n    listIndustry().then((response) => {\n      this.industryList = response.rows;\n    });\n\n    this.getIndustryScene();\n\n    this.getDicts(\"tag_type\").then((response) => {\n      this.tagTypeList = response.data;\n    });\n  },\n  methods: {\n    getIndustryScene() {\n      listIndustry().then((response) => {\n        this.industryList = response.rows;\n        listScene().then((response) => {\n          this.sceneListVo = response.rows;\n          this.setOption();\n        });\n      });\n    },\n    //选中\n    queryParamsHandleChange(value) {\n      if (value != null || value.length > 0) {\n        this.form.industryId = value[0];\n      }\n\n      if (value != null || value.length > 1) {\n        this.form.paramId = value[1];\n      }\n    },\n    handleClear() {\n      // 清除场景时执行的操作\n      this.queryParams.sceneIds = []; // 清空选中的场景\n    },\n    //设置行业场景下拉\n    setOption() {\n      this.industryList.forEach((element) => {\n        let item = {\n          value: element.id,\n          label: element.industryName,\n          children: [],\n        };\n        this.sceneListVo.forEach((row) => {\n          if (row.industryId === element.id) {\n            item.children.push({\n              value: row.id,\n              label: row.sceneName,\n            });\n          }\n        });\n        this.options.push(item);\n      });\n    },\n\n    setIndustrycode() {\n      this.industryList.forEach((row) => {\n        if (this.form.industryId == row.id) {\n          this.form.industryCode = row.industryCode;\n        }\n      });\n    },\n    handleIndustryClear() {\n      // 清除行业选择时执行的操作\n      this.queryParams.industryId = null; // 重置行业ID\n    },\n    setPanoramaUrlInput(value) {\n      this.$set(this.form, \"panoramaUrl\", value);\n      this.form.panoramaTime = new Date().getTime();\n    },\n    setInput(value) {\n      this.$set(this.form, \"sceneUrl\", value);\n    },\n    setCover(value) {\n      this.$set(this.form, \"coverUrl\", value);\n    },\n    setShareImg(value) {\n      this.$set(this.form, \"shareImg\", value);\n    },\n    setShareVedio(value) {\n      console.log(value);\n      this.$set(this.form, \"shareVedio\", value);\n    },\n\n    /** 查询场景列表 */\n    getList() {\n      this.loading = true;\n      listSceneV2(this.queryParams).then((response) => {\n        this.sceneList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        sceneName: null,\n        industryId: null,\n        sceneExplain: null,\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        sceneUrl: null,\n        sceneCode: null,\n        sceneOrder: null,\n        shareVedio: null,\n        shareImg: null,\n        industryCode: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加场景\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getScene(id).then((response) => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改场景\";\n\n        if (this.form.industryId && this.form.paramId) {\n          this.form.sceneIds = [this.form.industryId, this.form.paramId];\n        } else if (this.form.industryId) {\n          this.form.sceneIds = [this.form.industryId];\n        } else {\n          this.form.sceneIds = []; // 确保始终是数组\n        }\n\n        console.log(\"sdfsdf\", this.form.sceneIds);\n        this.industryList.forEach((row) => {\n          if (this.form.industryId == row.id) {\n            this.form.industryCode = row.industryCode;\n          }\n        });\n\n        this.options = this.options.map((option) => {\n          return {\n            ...option,\n            children: option.children.map((child) => {\n              return {\n                ...child,\n                disabled: child.value === this.form.id,\n              };\n            }),\n          };\n        });\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          this.loadingPanorama = this.$loading({\n            lock: true,\n            text: \"数据上传并解析图片中...\",\n            background: \"rgba(0, 0, 0, 0.7)\",\n          });\n          if (this.form.id != null) {\n            updateScene(this.form)\n              .then((response) => {\n                this.msgSuccess(\"修改成功\");\n                this.open = false;\n                this.getList();\n                this.getIndustryScene();\n                this.loadingPanorama.close();\n              })\n              .catch((r) => {\n                if (this.loadingPanorama != null) this.loadingPanorama.close();\n              });\n          } else {\n            addScene(this.form)\n              .then((response) => {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n                this.getIndustryScene();\n                this.loadingPanorama.close();\n              })\n              .catch((r) => {\n                if (this.loadingPanorama != null) this.loadingPanorama.close();\n              });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id == null ? this.ids : [row.id];\n      const names = this.sceneList\n        .filter((item) => ids.includes(item.id))\n        .map((item) => item.sceneCode)\n        .join(\", \");\n\n      this.$confirm('是否确认删除场景编号为\"' + names + '\"的数据项?', \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(function () {\n          return checkScene(ids);\n        })\n        .then((response) => {\n          return delScene(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        });\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      let that = this;\n      const exportParams = {\n        ...that.queryParams,\n        exportIdList: that.ids.length ? that.ids : null, // Add ids if any are selected\n      };\n      let msg = \"\";\n      if (that.ids.length > 0) {\n        msg = \"是否确认导出所筛选或选中的场景数据\";\n      } else {\n        msg = \"是否确认导出所有的场景数据\";\n      }\n      this.$confirm(msg, \"警告\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(function () {\n          that.download(\n            \"system/scene/export\",\n            exportParams,\n            `scene_${new Date().getTime()}.xlsx`\n          );\n        })\n        .catch({});\n    },\n  },\n};\n</script>\n"]}]}