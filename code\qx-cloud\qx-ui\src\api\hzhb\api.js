import request from '@/utils/request'
const baseURL = process.env.VUE_APP_BASE_API

export function list(query) {
    return request({
        url: '/system/hzhb/development/list',
        method: 'get',
        params: query,
        timeout: 1000 * 60 * 3
    })
}

export function add(data) {
    return request({
        url: '/system/hzhb/development',
        method: 'post',
        data: data
    })
}

export function update(data) {
    return request({
        url: '/system/hzhb/development',
        method: 'put',
        data: data
    })
}

export function edit(data) {
    return request({
        url: '/system/hzhb/development/edit',
        method: 'put',
        data: data
    })
}

export function examine(data) {
    return request({
        url: '/system/hzhb/development/examine',
        method: 'put',
        data: data
    })
}

export function deleteall(id) {
    return request({
        url: '/system/hzhb/development/' + id,
        method: 'delete'
    })
}

export function getpanorama(params) {
    return request({
        url: '/system/hzhb/development/get/' + params,
        method: 'get'
    })
}

export function devicelist(params) {
    return request({
        url: '/system/panorama/devicelist',
        method: 'get',
        params: params
    })
}


export function industrylist(params) {
    return request({
        url: '/system/hzhb/industry/list',
        method: 'get',
        params: params
    })
}

export function servicelist(params) {
    return request({
        url: '/system/hzhb/service/list',
        method: 'get',
        params: params
    })
}

export function serviceexport(params) {
    return request({
        url: '/system/hzhb/service/export',
        method: 'get',
        params: params
    })
}

export function servicedefault(params) {
    return request({
        url: '/system/hzhb/servicedefault/list',
        method: 'get',
        params: params
    })
}


// 通用下载方法
export function download(fileName) {
    window.location.href = baseURL + "/common/download?fileName=" + encodeURI(fileName) + "&delete=" + true;
}


export function uploadMapShoot(file) {
    let formData = new FormData();
    formData.append('file', file)
    return request({
        url: '/system/common/upload',
        method: 'post',
        data: formData,
        processData: false,// 告诉axios不要去处理发送的数据(重要参数)
    })
}