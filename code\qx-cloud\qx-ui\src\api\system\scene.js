import request from '@/utils/request'

// 查询场景列表
export function listScene(query) {
  return request({
    url: '/system/scene/list',
    method: 'get',
    params: query
  })
}

export function listSceneV2(query) {
  return request({
    url: '/system/scene/list/v2',
    method: 'get',
    params: query
  })
}

// 查询场景列表
export function tolistScene(query) {
  return request({
    url: '/system/scene/tolist',
    method: 'get',
    params: query
  })
}


// 查询场景详细
export function getScene(id) {
  return request({
    url: '/system/scene/' + id,
    method: 'get'
  })
}

// 新增场景
export function addScene(data) {
  return request({
    url: '/system/scene',
    method: 'post',
    data: data, timeout: 120000
  })
}

// 修改场景
export function updateScene(data) {
  return request({
    url: '/system/scene',
    method: 'put',
    data: data, timeout: 120000
  })
}

// 删除场景
export function delScene(id) {
  return request({
    url: '/system/scene/' + id,
    method: 'delete'
  })
}

// 导出场景
export function exportScene(query) {
  return request({
    url: '/system/scene/export',
    method: 'get',
    params: query
  })
}
// 查询场景详细
export function checkScene(id) {
  return request({
    url: '/system/scene/check/' + id,
    method: 'get'
  })
}
