<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="行业" prop="industryCode">
        <el-select
          v-model="queryParams.industryCode"
          placeholder="请选择行业"
          clearable
          @clear="handleIndustryClear">
          <el-option
            v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="startDate">
        <el-date-picker
          v-model="startDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:support:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="supportList">
      <el-table-column label="行业编码" align="center" prop="industryCode" />
      <el-table-column label="行业名称" align="center" prop="industryName" />
      <el-table-column label="点赞数量" align="center" prop="supportCount" />
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { listSupport, exportSupport } from "@/api/system/support";
import { listIndustry } from "@/api/system/industry";
export default {
  name: "Support",
  components: {
  },
  data() {
    return {
      startDate: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 前端用户点赞表格数据
      supportList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerId: null,
        industryCode: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      //行业列表
      industryList: []
    };
  },
  created() {
    this.getList();
    listIndustry().then(response => {
      this.industryList = response.rows;
    });
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    /** 查询前端用户点赞列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.startDate.length > 0) {
        this.queryParams.startDate = this.parseTime(this.startDate[0], '{y}-{m}-{d}');
        this.queryParams.endDate = this.parseTime(this.startDate[1], '{y}-{m}-{d}');
      }
      listSupport(this.queryParams).then(response => {
        this.supportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.startDate = []
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      this.$confirm('是否确认导出所有前端用户点赞数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/support/export', {
          ...that.queryParams
        }, `support_${new Date().getTime()}.xlsx`)
      }).catch({});
    }
  }
};
</script>
