<template>
    <div class="app-container">
        <div class="classftion-right-big-tltle">
            <img src="@/assets/images/index/biaoti.png" alt="" />
            <div>二级店落地案例点击次数和时长</div>
            <div class="more" @click="handleMoreClick('twocasemain')">查看更多</div>
            <!-- <div class="title-change" @click="titleChange()"></div> -->
        </div>
        <div class="partner">
            <div class="partner-left">
                <div class="partner-left-title">行业列表</div>
                <div class="partner-left-list">
                    <div class="partner-left-item" v-for="(item, index) in industryList" :key="index"
                        @click="selectItemClick(item.industryCode)"
                        :class="queryParams.industryCode == item.industryCode ? 'acitve' : ''">{{ item.industryName }}
                    </div>
                </div>
            </div>
            <div class="partner-right">
                <div class="title">
                    <div :class="!titleActive ? 'btn' : 'btn active'" @click="titleActiveClick()">{{ isSecondaryAdministrator? this.region:'总部' }}</div>
                    <div :class="titleActive ? 'btn' : 'btn active'" @click="titleActiveClick()" v-if="!isSecondaryAdministrator">省公司</div>
                    <div class="btn"></div>
                    <!-- <div class="date">
                        <el-date-picker
                            v-model="dayDate"
                            @change="getCaseMainList()"
                            style="width:280px"
                            format="yyyy/MM/dd"
                            prefix-icon=""
                            :clearable="false"
                            ref="elDatePickControl2"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :picker-options="{
                                disabledDate: time => {
                                    return time.getTime() > Date.now()
                                }
                            }"
                        ></el-date-picker>
                    </div> -->
                </div>
                <!-- <div class="top"></div> -->
                <div class="list-title">
                    <div style="flex: 2;">{{ titleActive ? '案例名称' : '省份案例' }}</div>
                    <div>平均值（秒）</div>
                    <div>点击（次）</div>
                </div>
                <div class="list-line">
                    <div class="img"></div>
                </div>
                <div class="partner-right-list">
                    <div v-for="(item, index) in caseMainList" :key="index">
                        <div
                            :style="item.parent != null ? 'font-size:16px;font-weight:bold;flex:2;display:flex;align-items:center;justify-content:center;' : 'flex:2;display:flex;align-items:center;justify-content:center;'">
                            <div v-if="!titleActive && item.caseCount > 0 && item.parent == null"
                                :class="item.mainType == 1 ? 'item-img-close' : 'item-img-open'"
                                @click="caseMainOpen(index, item)"></div>
                            <div v-if="!titleActive && (item.caseCount == 0 || item.parent != null)"
                                class="item-img-div"></div>
                            {{ item.mainCaseTitle }}
                        </div>
                        <div :style="item.parent != null ? 'font-weight:bold;' : ''">{{ item.timeLength.toFixed(2) }}
                        </div>
                        <div :style="item.parent != null ? 'font-weight:bold;' : ''">{{ item.clickCount }}</div>
                    </div>
                    <div v-if="caseMainList.length == 0">
                        <div style="text-align: left;padding-left: 10px;"></div>
                        <div style="padding-right: 80px;">没有数据</div>
                        <div style="padding-left: 20px"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getindustrylist, casemainsum, casemainregion,provincialSecondaryAdministrator } from '@/api/system/sum.js'
import { setScale } from '@/utils/setScale'
export default {
    name: 'Config',
    data() {
        return {
            titleActive: true,
            // dayDate: [],
            list: [{ name: '冶金' }, { name: '电力' }, { name: '教育' }, { name: '工厂' }, { name: '矿山(井工矿)' }, { name: '矿山(露天矿矿)' }],
            selectItem: '',
            industryList: [],
            caseMainList: [],
            queryParams: {
                region: '总部'
            },
            isSecondaryAdministrator: false,
            region: '',
        }
    },
    props: {
        dayDate: {
            type: Array,
            default: []
        }
    },
    watch: {
        // 监听 Date 数组的变化
        dayDate: {
            handler() {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
                this.getIndustryData()
            },
            immediate: true, // 初始化时立即执行一次
            deep: true // 深度监听
        }
    },
    created() {
        // const end = new Date()
        // const start = new Date()
        // start.setTime(start.getTime() - 3600 * 1000 * 24 * 10)
        // this.dayDate = [start, end]
        setScale()
        window.onresize = () => {
            setScale()
        }
        this.getIndustryData()
        this.myProvincialSecondaryAdministrator();
    },
    methods: {
        myProvincialSecondaryAdministrator(){
            provincialSecondaryAdministrator().then(res=>{
            console.log("======res: ",res.data)
            this.isSecondaryAdministrator = res.data.isSecondaryAdministrator;
            this.region = res.data.region;
            if(this.isSecondaryAdministrator && res.data.region){
                this.queryParams.region = res.data.region;
            }
        });
        },
        // 跳转传参数 开始 结束时间
        handleMoreClick(url) {
            this.$router.push("indexDetail/" + url + "?dayDate=" + this.dayDate)

            console.log(url)
        },
        //点击展开
        caseMainOpen(index, item) {
            let that = this
            if (item.mainType == null || item.mainType == 0) {
                item.mainType = 1
                if (that.dayDate.length > 0) {
                    that.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                    that.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
                }
                that.queryParams.region = item.mainCaseTitle
                if(this.isSecondaryAdministrator && this.region){
                    that.queryParams.region = this.region;
                }
                casemainsum(that.queryParams).then(res => {
                    for (let i = 0; i < res.data.length; i++) {
                        res.data[i].parent = item.mainCaseTitle
                        that.caseMainList.splice(index + i + 1, 0, res.data[i])
                    }
                })
            } else {
                item.mainType = 0
                for (let i = index + 1; i < that.caseMainList.length; i++) {
                    let row = that.caseMainList[i]
                    if (row.parent === item.mainCaseTitle) {
                        that.caseMainList.splice(i, 1)
                        i--
                    } else break
                }
            }
        },

        titleActiveClick() {
            this.caseMainList = []
            this.titleActive = !this.titleActive
            if (this.titleActive) {
                this.queryParams.region = '总部'
            }
            this.getCaseMainList()
        },
        selectItemClick(code) {
            this.queryParams.industryCode = code
            this.getCaseMainList()
        },

        getCaseMainList() {
            let that = this
            if (that.dayDate.length > 0) {
                that.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                that.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            if(this.isSecondaryAdministrator && this.region){
                this.queryParams.region = this.region;
            }
            if (this.titleActive) {
                casemainsum(that.queryParams).then(res => {
                    this.caseMainList = res.data
                })
            } else {
                casemainregion(that.queryParams).then(res => {
                    this.caseMainList = res.data
                })
            }
        },

        getIndustryData() {
            getindustrylist().then(res => {
                this.industryList = res.data
                if (this.industryList.length > 0) {
                    this.queryParams.industryCode = this.industryList[0].industryCode
                    this.getCaseMainList()
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    margin-left: 34px;
    width: 704px;
    // padding: 40px 40px 0px 40px;

    /* width: 1860px; */
    .classftion-right-big-tltle {
        font-size: 24px;
        font-weight: 900;
        color: #0066e4;
        display: flex;
        align-items: center;
        margin: 20px 0;

        img {
            width: 18px;
            height: 24px;
            margin-right: 16px;
        }

        .more {
            flex: 1;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
            position: relative;
            padding-right: 10px;
            cursor: pointer;
        }

        .more::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            border-top: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            border-right: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            transform: translateY(-50%) rotate(45deg);
        }
    }

    .partner {
        padding: 16px;
        height: 460px;
        border-radius: 10px;
        opacity: 1;
        background: #ffffff;
        display: flex;
        box-sizing: border-box;

        .partner-left {
            width: 160px;
            margin-right: 20px;

            .partner-left-title {
                font-size: 20px;
                font-weight: 700;
                text-align: center;
                margin-bottom: 20px;
                // width: 176px;
                // height: 35px;
                // background: url(../../assets/images/click/partner-left-title.png) no-repeat center center;
                // background-size: 100%;
                // text-align: center;
                // margin-bottom: 20px;
                // margin-top: 25px;
            }

            .partner-left-list {
                overflow-y: auto;
                overflow-x: hidden;
                height: 360px;
                padding-right: 10px;
                box-sizing: border-box;
            }

            .partner-left-item {
                cursor: pointer;
                width: 148px;
                height: 40px;
                border-radius: 4px;
                opacity: 1;
                font-size: 18px;
                font-weight: 700;
                // border: 1px solid #acc1d0;
                background: #eaeff8;
                color: #005cdc;
                text-align: center;
                line-height: 40px;
                margin-bottom: 10px;
            }

            .acitve {
                background: linear-gradient(180deg, #68befa 0%, #003eff 100%);
                color: #fff;
            }
        }

        .partner-right {
            text-align: center;

            .title {
                height: 48px;
                display: flex;
                margin: 0;

                // width: 1437px;
                .btn {
                    width: 161px;
                    height: 48px;
                    line-height: 48px;
                    border-radius: 9px 9px 0 0;
                    font-size: 20px;
                    font-weight: 800;
                    color: #222222;
                    cursor: pointer;
                    border-bottom: 1px solid #cad4e6;
                }

                .active {
                    // background: linear-gradient(180deg, #31d6a9 0%, #0f75c3 100%);
                    color: #005cdc;
                    border: 1px solid #cad4e6;
                    border-bottom: none;
                }

                .date {}
            }

            .top {
                width: 1437px;
                height: 7px;
                float: left;
                background: linear-gradient(90deg, #31d6a9 0%, #0f75c3 100%);
            }

            .list-title {
                border-left: 1px solid #cad4e6;
                border-right: 1px solid #cad4e6;
                height: 42px;
                width: 487px;
                box-sizing: border-box;
                // padding: 0 3.5%;
                margin: auto;
                display: flex;

                >div {
                    flex: 1;
                    text-align: center;
                    font-size: 16px;
                    color: #666666;
                    font-weight: 400;
                    line-height: 42px;
                }
            }

            // .list-line {
            //     width: 100%;
            //     height: 2px;
            //     box-sizing: border-box;
            //     padding: 0px 3.5%;
            //     .img {
            //         width: 100%;
            //         height: 2px;
            //         background: url(../../assets/images/click/maincase-line.png) no-repeat center center;
            //         background-size: 100%;
            //     }
            // }

            .partner-right-list {
                float: left;
                height: 323px;
                overflow-y: auto;
                width: 487px;
                // border: 1px solid #cad4e6;
                border-left: 1px solid #cad4e6;
                border-right: 1px solid #cad4e6;
                border-bottom: 1px solid #cad4e6;
                border-radius: 0 0 10px 10px;

                >div {
                    // height: 80px;
                    width: 100%;
                    // border-bottom: 1px solid #e6eff9;
                    margin: auto;
                    display: flex;

                    >div {
                        flex: 1;
                        text-align: center;
                        font-size: 16px;
                        color: #222;
                        font-weight: 800;
                        line-height: 42px;

                        .item-img-open {
                            height: 20px;
                            width: 20px;
                            margin-right: 10px;
                            background: url(../../assets/images/index/maincase-open.png) no-repeat center center;
                            background-size: 100%;
                            cursor: pointer;
                        }

                        .item-img-div {
                            height: 20px;
                            width: 20px;
                            float: left;
                            margin-top: 30px;
                            margin-right: 10px;
                        }

                        .item-img-close {
                            height: 20px;
                            width: 20px;
                            margin-right: 10px;
                            background: url(../../assets/images/index/maincase-close.png) no-repeat center center;
                            background-size: 100%;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}

::v-deep .el-input__inner {
    border: 0;
}

::v-deep .el-range-editor--medium .el-range__icon,
.el-range-editor--medium .el-range__close-icon {
    display: none;
}

::v-deep .el-date-editor .el-range-separator {
    padding: 0;
}

::v-deep .el-date-editor .el-range__close-icon {
    display: none;
}

::v-deep .el-date-editor .el-range-input {
    width: 48%;
    font-size: 20px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-range-separator {
    font-size: 20px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-date-editor>input:-moz-placeholder {
    color: #1366bf;
}

::v-deep .el-date-editor>input:-ms-input-placeholder {
    color: #1366bf;
}

::v-deep .el-date-editor>input::-webkit-input-placeholder {
    color: #1366bf;
}

/*修改滚动条样式*/
div::-webkit-scrollbar {
    width: 6px;
    height: 10px;
    /**/
}

div::-webkit-scrollbar-track {
    background: rgb(239, 239, 239);
    border-radius: 2px;
}

div::-webkit-scrollbar-thumb {
    background: #2269de;
    border-radius: 10px;
}

div::-webkit-scrollbar-thumb:hover {
    background: #2269de;
}

div::-webkit-scrollbar-corner {
    background: #2269de;
}
</style>
