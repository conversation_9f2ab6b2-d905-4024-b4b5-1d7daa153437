<template>
  <div class="iframe-wrap">
    <iframe
      ref="frame"
      :src="`${url}?token=${token}`"
      :style="{ height: iframeH + 'px' }"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script>
export default {
  data() {
    return {
      token: '',
      url: '',
      iframeH: 600
    }
  },
  mounted() {
    this.token = localStorage.getItem('Admin-Token')
    this.url = process.env.VUE_APP_ISHOWA_URL
    this.$nextTick(() => {
      this.resizeIframe()
      window.addEventListener('resize', this.resizeIframe)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeIframe)
  },
  methods: {
    resizeIframe() {
      // 让 iframe 底部贴住窗口底部
      const top = this.$refs.frame.getBoundingClientRect().top
      this.iframeH = Math.max(0, window.innerHeight - top)
    }
  }
}
</script>

<style scoped>
.iframe-wrap { width: 100%; }
.iframe-wrap iframe { width: 100%; display: block; border: 0; }
</style>
