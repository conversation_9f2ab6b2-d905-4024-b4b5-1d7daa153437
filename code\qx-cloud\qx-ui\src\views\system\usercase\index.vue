<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账号" prop="account">
        <el-input v-model="queryParams.account" placeholder="请输入账号" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:usercase:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:usercase:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:usercase:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="usercaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="账号" align="center" prop="account" />
      <el-table-column label="昵称" align="center" prop="nickname" />

      <el-table-column label="电话号码" align="center" prop="phone" />
      <el-table-column label="区域" align="center" prop="region" />
      <el-table-column label="部门名称" align="center" prop="departmentName">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.departmentName"
            raw-content
            placement="top-start"
            v-if="scope.row.departmentName"
          >
            <span v-if="scope.row.departmentName && scope.row.departmentName.length <= 30">
               {{ scope.row.departmentName }}
          </span>
            <span v-if="scope.row.departmentName && scope.row.departmentName.length > 30">
               {{ scope.row.departmentName.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.departmentName== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="用户职务" align="center" prop="userJob">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.userJob"
            raw-content
            placement="top-start"
            v-if="scope.row.userJob"
          >
            <span v-if="scope.row.userJob && scope.row.userJob.length <= 30">
               {{ scope.row.userJob }}
          </span>
            <span v-if="scope.row.userJob && scope.row.userJob.length > 30">
               {{ scope.row.userJob.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.userJob== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" key="accountState">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.accountState" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:user:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdatePwd(scope.row)" v-hasPermi="['system:usercase:edit']">修改密码</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:usercase:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改添加案例的用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="账号" prop="account">
          <el-input v-model="form.account" placeholder="请输入账号" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入密码" maxlength="32" show-word-limit />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="电话号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入电话号码" maxlength="20" show-word-limit @input="handlePhoneInput" />
        </el-form-item>
        <el-form-item label="部门名称" prop="departmentName">
          <el-input v-model="form.departmentName" placeholder="请输入部门名称" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item label="用户职务" prop="userJob">
          <el-input v-model="form.userJob" placeholder="请输入用户职务" maxlength="50" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改添加案例的用户对话框 -->
    <el-dialog :title="title" :visible.sync="open2" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules2" label-width="80px">
        <el-form-item label="账号" prop="account">
          <el-input v-model="form.account" placeholder="请输入账号" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="电话号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入电话号码" maxlength="11" show-word-limit @input="handlePhoneInput" />
        </el-form-item>
        <el-form-item label="部门名称" prop="departmentName">
          <el-input v-model="form.departmentName" placeholder="请输入部门名称" maxlength="100" show-word-limit />
        </el-form-item>
        <el-form-item label="用户职务" prop="userJob">
          <el-input v-model="form.userJob" placeholder="请输入用户职务" maxlength="50" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改前端客户用户对话框 -->
    <el-dialog title="修改密码" :visible.sync="openSetPwd" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="pwdform" :model="pwdform" :rules="pwdrules" label-width="80px">
        <el-form-item label="密码" prop="password">
          <el-input v-model="pwdform.password" type="password" placeholder="请输入密码" maxlength="32" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPwdForm">确 定</el-button>
        <el-button @click="cancelPwd">取 消</el-button>
      </div>
    </el-dialog>
    <TreasuryVerify ref="treasuryVerifyRef" functionCode="600001" operationCode="1001" />
  </div>
</template>

<script>
import { listUser, getUser, delUser, caseAddUser, updateUser, exportUser } from "@/api/system/qxuser";
import TreasuryVerify from "../../../components/TreasuryVerify/TreasuryVerify.vue";
import md5 from 'md5';
import { Loading } from 'element-ui'
export default {
  name: "Usercase",
  components: {
    TreasuryVerify
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 添加案例的用户表格数据
      usercaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      open2: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        region: null
      },
      openSetPwd: false,
      pwdform: {},
      // 表单校验
      pwdrules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,32}/,
            message: "密码为数字字母，且长度为8至32位",
            trigger: "blur"
          }
        ]
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        account: [
          { required: true, message: "用户账号不能为空", trigger: "blur" },
          // {
          //   pattern: /^[a-zA-Z0-9].{6,50}$/,
          //   message: "账号为数字字母，且长度为6至50位",
          //   trigger: "blur"
          // }
          {
            type: "email",
            message: "用户账号请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,32}/,
            message: "密码为数字字母，且长度为8至32位",
            trigger: "blur"
          }
        ],
        phone: [
          { required: true, message: "用户手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        nickname: [
          { required: true, message: "用户名称不能为空", trigger: "blur" }
        ],
      },
      // 表单校验
      rules2: {
        account: [
          { required: true, message: "用户账号不能为空", trigger: "blur" },
          {
            pattern: /^[a-zA-Z0-9].{6,50}$/,
            message: "账号为数字字母，且长度为6至50位",
            trigger: "blur"
          }
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,32}/,
            message: "密码为数字字母，且长度为8至32位",
            trigger: "blur"
          }
        ],
        phone: [
          { required: true, message: "用户手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ],
        nickname: [
          { required: true, message: "用户名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handlePhoneInput(value) {
      // 使用正则表达式限制输入为数字
      this.form.phone = value.replace(/[^0-9]/g, '');
    },
    generatePwd() {
      let str = "qwertyuiopasdfghjklzxcvbnm";
      let num = "**********";
      let password = "";
      for (let i = 0; i < 12; i++) {
        if (i < 6) {
          let a = Math.round(Math.random() * 25);
          password += str.substring(a, a + 1);
        } else {
          let b = Math.round(Math.random() * 9);
          password += num.substring(b, b + 1);
        }
      }
      return password;
    },
    /** 查询添加案例的用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.accountType = 2;
      listUser(this.queryParams).then(response => {
        this.usercaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.open2 = false;
      this.reset();
    },
    cancelPwd() {
      this.openSetPwd = false;
      this.pwdform = {
        id: null,
        password: null
      }
      this.resetForm("pwdform");
    },
    //用户状态
    handleStatusChange(row) {
      let that = this;
      let text = row.accountState === "0" ? "启用" : "停用";
      this.$confirm('确认要"' + text + '""' + row.account + '"用户吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        updateUser({
          id: row.id,
          accountState: row.accountState
        }).then(response => {
          that.msgSuccess(text + "成功");
        }).catch(function () {
          row.accountState = row.accountState === "0" ? "1" : "0";
        });
      });
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        createTime: null,
        delFlag: null,
        region: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加案例的用户";
      this.form.password = this.generatePwd();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getUser(id).then(response => {
        this.form = response.data;
        this.open2 = true;
        this.title = "修改案例的用户";
        this.form.password = null;
      });
    },
    /** 修改按钮操作 */
    handleUpdatePwd(row) {
      const id = row.id || this.ids;
      this.openSetPwd = true;
      this.pwdform = {
        id: id,
        password: null
      }
      this.resetForm("pwdform");
    },
    /** 提交按钮 */
    submitPwdForm() {
      this.$refs["pwdform"].validate(valid => {
        if (valid) {
          if (this.pwdform.id != null) {
            this.pwdform.password = md5(this.pwdform.password);
            console.log(this.pwdform.password);
            updateUser(this.pwdform).then(response => {
              this.msgSuccess("修改密码成功");
              this.openSetPwd = false;
            });
          }
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateUser(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open2 = false;
              this.getList();
            });
          } else {
            this.form.password = md5(this.form.password);
            caseAddUser(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除添加案例的用户编号为"' + ids + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delUser(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that=this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      this.$confirm('是否确认导出所有添加案例的用户数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        let downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", });
        exportUser(exportParams).then(response => {
          if (response.msg != "-1") {
            that.download(response.msg);
          } else {
            that.$refs.treasuryVerifyRef.getTreasuryApprover();
          }
          if (downloadLoadingInstance != null)
            downloadLoadingInstance.close();
        }).catch((r) => {
          if (downloadLoadingInstance != null)
            downloadLoadingInstance.close();
        });

      })
    }
  }
};
</script>
