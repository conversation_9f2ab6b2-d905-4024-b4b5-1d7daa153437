<template>
    <div class="app-container" :style="open ? 'height:800px' : ''">
        <!-- <div class="region-dialog-bg" v-show="open">
            <div class="region-dialog">
                <div class="dialog-title">{{ industryName }}</div>
                <div class="dialog-colse" @click="colseDialog()"></div>
                <div class="dialog-list">
                    <template v-for="(row, index) in itemList">
                        <div class="dialog-item-content" :key="index">
                            <div style="padding: 0 10px;">
                                <div class="">
                                    <span>NO.{{ index + 1 }}&nbsp;</span>
                                    <span>{{ row.provinceCode.replace('省', '') }}</span>
                                </div>
                                <div style="margin-right:80px">{{ row.clickNumber }}</div>
                            </div>
                            <div>
                                <div style="width: 100%;">
                                    <el-progress :percentage="row.ratio"></el-progress>
                                </div>
                            </div>
                        </div>
                    </template>
</div>
</div>
</div> -->
        <div>
            <div class="dashboard-editor-header mt20">
                <img src="@/assets/images/index/biaoti.png" alt="" />
                各行业用户分省点击
                <span style="font-size: 16px;">(前五名)</span>
                <div class="more" @click="handleMoreClick('user-region')">查看更多</div>
            </div>
        </div>
        <div class="region-list">
            <div v-for="(item, indexs) in this.dataList.filter((item, index) => index < 3)" :key="indexs"
                class="region-item" :style="{ border: customColor[item.industryName] || '#f5f5f5' }">
                <div class="region-item-tlite">
                    <div class="region-item-t">{{ item.industryName }}</div>
                    <!-- <div class="region-more" @click="">more</div> -->
                    <!-- isMore(item) -->
                </div>

                <div class="region-item-list" :style="{ 'border-right': indexs < 2 ? '1px dashed #c4c4c4' : '' }"
                    v-if="item.industryModuleClick.length">
                    <template v-for="(row, index) in item.industryModuleClick">
                        <div class="region-item-content" :key="index" v-if="index < 5">
                            <div style="padding: 0 30px;">
                                <div>
                                    <span>NO.{{ index + 1 }}&nbsp;</span>
                                    <span>{{ row.provinceCode.replace('省', '') }}</span>
                                </div>
                                <div style="color: #005CDC;">{{ row.clickNumber }}</div>
                            </div>
                            <div>
                                <div style="width: 100%;">
                                    <el-progress :percentage="row.ratio"></el-progress>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="region-item-list" :style="{ 'border-right': indexs < 2 ? '1px dashed #c4c4c4' : '' }"
                    v-else>
                    <div style="text-align:center;">暂无数据</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
import { provinceclick } from '@/api/system/sum.js'
export default {
    name: 'Config',
    data() {
        return {
            // dayDate: [],
            open: false,
            industryName: '',
            itemList: [],
            customColor: {},
            dataList: [],
            queryParams: {}
        }
    },
    props: {
        dayDate: {
            type: Array,
            default: []
        }
    },
    watch: {
        // 监听 Date 数组的变化
        dayDate: {
            handler() {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
                this.getList()
            },
            immediate: true, // 初始化时立即执行一次
            deep: true // 深度监听
        }
    },
    created() {
        setScale()
        window.onresize = () => {
            setScale()
        }
        // this.getList()
    },
    methods: {
        // 跳转传参数 开始 结束时间
        handleMoreClick(url) {
            this.$router.push("indexDetail/" + url + "?dayDate=" + this.dayDate)

            console.log(url)
        },
        colseDialog() {
            this.open = false
        },
        //显示更多
        isMore(item) {
            this.industryName = item.industryName
            this.itemList = item.industryModuleClick
            this.open = true
        },
        getList() {
            provinceclick(this.queryParams).then(res => {
                this.dataList = res.data
                console.log(res.data)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;

    .dashboard-editor-header {
        font-size: 24px;
        font-weight: 900;
        color: #0066e4;
        display: flex;
        align-items: center;

        img {
            width: 18px;
            height: 24px;
            margin-right: 16px;
        }

        .more {
            flex: 1;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
            position: relative;
            padding-right: 10px;
            cursor: pointer;
        }

        .more::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            border-top: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            border-right: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            transform: translateY(-50%) rotate(45deg);
        }
    }

    .region-dialog-bg {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: rgb(0, 0, 0, 0.3);
        z-index: 999;

        .region-dialog {
            float: left;
            margin-top: 20px;
            margin-left: calc(50% - 628px);
            width: 1256px;
            height: 900px;
            background: url(../../assets/images/click/user-region-dialog.png) no-repeat center center;
            background-size: 100% 100%;

            .dialog-title {
                float: left;
                width: 120px;
                margin-top: 20px;
                margin-left: 16px;
                height: 33px;
                font-size: 33px;
                font-family: MiSans-Heavy, MiSans;
                font-weight: 800;
                color: #1366bf;
                line-height: 33px;
            }

            .dialog-colse {
                float: right;
                cursor: pointer;
                height: 25px;
                width: 25px;
                margin-right: 15px;
                margin-top: 15px;
                background: url(../../assets/images/click/user-region-colse.png) no-repeat center center;
                background-size: 100% 100%;
            }

            .dialog-list {
                float: left;
                width: 100%;
                height: 800px;
                margin-top: 50px;
                box-sizing: border-box;
                padding-left: 20px;

                .dialog-item-content {
                    float: left;
                    width: 400px;
                    height: 50px;
                    margin: 0px auto 20px auto;
                    /* height: 300px; */
                    /* background: #000; */
                }
            }

            >div {
                >div {
                    >div {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        font-size: 20px;
                        color: #1366bf;
                    }
                }
            }
        }
    }

    .region-list {
        width: 896px;
        height: 460px;
        border-radius: 10px;
        opacity: 1;
        background: #ffffff;
        display: flex;

        .region-item {
            width: 308px;
            height: 100%;

            box-sizing: border-box;

            // margin: 40px 18px;
            .region-item-tlite {
                display: flex;
                justify-content: space-between;
                align-items: center;
                // height: 30px;
                margin: 32px 25px;
                padding-bottom: 15px;
                border-bottom: 1px solid #005cdc;
            }

            .region-more {
                cursor: pointer;
                height: 30px;
                line-height: 29px;
                width: 60px;
                font-size: 12px;
                border-radius: 5px;
                border: 1px solid #1366bf;
                text-align: center;
                color: #1366bf;
            }

            .region-item-t {
                // padding: 32px;
                font-size: 20px;
                color: #1366bf;
                font-weight: bold;
                text-align: left;
                color: #222222;
            }

            .region-item-list {
                margin-top: 20px;

                .region-item-content {
                    width: 100%;
                    margin: 0px auto 20px auto;
                    padding: 0 25px;
                    /* height: 300px; */
                    /* background: #000; */
                }
            }

            // .region-item-list:nth-child(1) {
            //     border-right: 1px solid #c4c4c4 !important;
            // }
            >div {
                >div {
                    >div {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        font-size: 20px;
                        color: #222;
                    }
                }
            }
        }
    }

    /* justify-content: space-between; */
}

.region-item:nth-child(4n + 1),
.region-item:first-child {
    margin-left: 0 !important;
}

.region-item:nth-child(4n),
.region-item:last-child {
    margin-right: 0 !important;
}

::v-deep .el-progress__text {
    display: none;
}

::v-deep .el-progress-bar__inner {
    background: linear-gradient(90deg, #68e5fa 0%, #013fff 100%) !important;
}

::v-deep .el-progress-bar__outer {
    background: #eaeff8;

    /* opacity: 0.2; */
}

/*修改滚动条样式*/
// .region-item-list::-webkit-scrollbar {
//     width: 6px;
//     height: 10px;
// }
// .region-item-list::-webkit-scrollbar-track {
//     background: rgb(239, 239, 239);
//     border-radius: 2px;
// }
// .region-item-list::-webkit-scrollbar-thumb {
//     background: #31d6a9;
//     border-radius: 10px;
// }
// .region-item-list::-webkit-scrollbar-thumb:hover {
//     background: #42a5a7;
// }
// .region-item-list::-webkit-scrollbar-corner {
//     background: #179a16;
// }

::v-deep .el-range-editor--medium .el-range__icon,
.el-range-editor--medium .el-range__close-icon {
    display: none;
}

::v-deep .el-date-editor .el-range__icon {
    line-height: 30px;
}

::v-deep .el-date-editor .el-range-separator {
    padding: 0;
}

::v-deep .el-date-editor .el-range__close-icon {
    display: none;
}

::v-deep .el-date-editor .el-range-input {
    width: 48%;
    font-size: 20px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-range-separator {
    font-size: 20px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-date-editor>input:-moz-placeholder {
    color: #1366bf;
}

::v-deep .el-date-editor>input:-ms-input-placeholder {
    color: #1366bf;
}

::v-deep .el-date-editor>input::-webkit-input-placeholder {
    color: #1366bf;
}

.date {
    float: right;
    margin-top: 35px;
    margin-right: 20px;
}

/* ::v-deep .el-progress-bar {
        width: 124%;
    } */

// .region-item-list:nth-child(1),
// .region-item-list:nth-child(2) {
//     border-right: 1px solid #c4c4c4 !important;
// }

.mt20 {
    margin-bottom: 20px;
}
</style>
