<template>
  <div class="index">
    <Preview ref="previewRef" v-show="previewPage" @change-preview="handlePreviewPageChange" />
    <div class="app-container" v-show="!previewPage">
      <el-form style="margin-top:30px;" :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="行业场景" prop="sceneId">
          <el-cascader v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear">
            <!-- 监听清除事件 -->
          </el-cascader>
        </el-form-item>
        <el-form-item label="合作伙伴" prop="userId">
          <el-select size="small" v-model="queryParams.userId" filterable placeholder="请选择合作伙伴">
            <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态" prop="state">
          <el-select size="small" v-model="queryParams.state" placeholder="请选择审核状态">
            <el-option :key="0" label="审核中" :value="0"></el-option>
            <el-option :key="5" label="二级审核中" :value="5"></el-option>
            <el-option :key="4" label="审核通过" :value="4"></el-option>
            <el-option :key="2" label="审核未通过" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          <!-- <el-button type="primary" size="small" :disabled="multiple" @click="agree">一键同意</el-button>
          <el-button type="danger" size="small" :disabled="multiple" @click="reject">一键驳回</el-button> -->
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain size="mini" :disabled="multiple" @click="agree">一键同意</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain size="mini" :disabled="multiple" @click="reject">一键驳回</el-button>
        </el-col>
      </el-row>

      <el-table :data="developmentList" v-loading="loading" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55">
        </el-table-column>
        <el-table-column prop="industryName" align="center" label="行业" width="100"> </el-table-column>
        <el-table-column prop="sceneName" align="center" label="场景" width="260"> </el-table-column>
        <el-table-column prop="nickname" align="center" label="合作伙伴" width="260">
        </el-table-column>
        <el-table-column prop="state" align="center" label="审核进度" width="160">
          <template slot-scope="scope">
            <span>{{scope.row.state===0?'审核中':scope.row.state===4?'审核通过':scope.row.state===5?'二级审核中':scope.row.state===2?'审核不通过':''}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="showHide" align="center" label="显示隐藏" width="160">
          <template slot-scope="scope">
            <span>{{scope.row.showHide==0?'显示':'隐藏'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" align="center" label="查看详情" width="160">
          <template slot-scope="scope">
            <el-button type="primary" size="small" @click="getDetailList(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="260">
          <template slot-scope="scope">
            <el-button type="primary" v-show="(scope.row.state===0&&scope.row.examineType===1)||(scope.row.state===5&&scope.row.examineType===2)||(scope.row.state===0&&scope.row.examineType==null)" size="small"
              @click="agree(scope.row)">同意</el-button>
            <el-button type="danger" v-show="(scope.row.state===0&&scope.row.examineType===1)||(scope.row.state===5&&scope.row.examineType===2)||(scope.row.state===0&&scope.row.examineType==null)" size="small"
              @click="reject(scope.row)">驳回</el-button>
            <el-button :type="scope.row.showHide==0?'warning':'success'" size="small" @click="showHide(scope.row)">{{scope.row.showHide==0?'隐藏':'显示'}}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="explain" align="center" label="驳回缘由">
          <template #default="scope">
            <el-tooltip :content="scope.row.explain" raw-content placement="top-start" v-if="scope.row.explain">
              <span v-if="scope.row.explain && scope.row.explain.length <= 30">
                {{ scope.row.explain }}
              </span>
              <span v-if="scope.row.explain && scope.row.explain.length > 30">
                {{ scope.row.explain.substr(0, 30) + "..." }}
              </span>
            </el-tooltip>
            <span v-else-if="scope.row.explain== null"> </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>

  </div>
</template>

<script>
import Preview from "../develop/preview.vue";
import { getProvince } from "../../../../utils/province";

import {
  developmentlist,
  userlist,
  scenelist,
  industrylist,
  reject,
  showhide,
  agree,
  servicelist,
} from "@/api/hzhb/exmaine";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";

export default {
  components: {
    Preview,
  },
  data() {
    return {
      // 遮罩层
      loading: true,

      updPwdClose: false,
      pwdTitle: "修改密码",
      openSetPwd: false,
      pwdform: {},
      // 表单校验
      pwdrules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,50}/,
            message: "密码为数字字母，且长度为8至50位",
            trigger: "blur",
          },
        ],
      },

      sceneIds: [],
      previewPage: false,
      // 总条数
      total: 0,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      props: { multiple: true },
      name: null,
      value: "",
      optionsProvince: [],
      developmentList: [],
      industryList: [],
      userList: [], //合作伙伴
      nikeName: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sceneId: null,
        industryId: null,
        userId: null,
        state: null,
      },
      selectDeviceTableList: [], //表格数据
      selectSoftwareTableList: [], //软件数据
      selectIntegrateTableList: [], //集成服务
      selectOtherTableList: [], //集成服务
      qxCaseList: [], //案例
      qxLinkList: [], //链接
      developmentCityList: [], //城市的列表
      cityName: "",
      scene: {},
      development: {},
      industryCode: "",
      options: [],
    };
  },
  created() {
    this.setProvince();
    this.getIndustryList();
    this.getUserList();
    this.getList();
    this.Move();
    this.nikeName = localStorage.getItem("examinenickName");

    var html = document.getElementsByTagName("html")[0];
    //屏幕的宽度（兼容处理）
    var w = document.documentElement.clientWidth || document.body.clientWidth;
    //750这个数字是根据你的设计图的实际大小来的，所以值具体根据设计图的大小
    html.style.fontSize = w / 2300 + "px";
  },
  mounted() {},
  methods: {
    handlePreviewPageChange() {
      this.previewPage = false; // 改变 previewPage 为 false
    },
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    //关闭全景
    panoramaClose() {
      this.iframeUr = "";
      document.body.removeChild(this.divSFYX);
      this.Move();
      this.getPanorama();
    },
    //停止页面滚动
    stopMove() {
      let m = function (e) {
        e.preventDefault();
      };
      document.documentElement.style.overflow = "hidden";
      document.documentElement.scrollTop = 0;
      document.addEventListener("touchmove", m, { passive: false }); //禁止页面滑动
    },
    //开启页面滚动
    Move() {
      let m = function (e) {
        e.preventDefault();
      };
      document.documentElement.style.overflow = ""; //出现滚动条
      document.removeEventListener("touchmove", m, { passive: true });
    },
    //item.linkUrl
    //type=0显示审核的数据type=1显示不需要审核的数据这样

    cancelPwd() {
      this.openSetPwd = false;
      this.pwdform = {
        password: null,
      };
      this.resetForm("pwdform");
    },

    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.queryParams.industryId = value[0];
      }
      if (value != null || value.length > 1) {
        this.queryParams.sceneId = value[1];
      }
    },
    handleChange(value) {
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach((row) => {
        if (row.id === value[1]) {
          this.selectItem = row;
        }
      });
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach((element) => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: [],
        };
        this.sceneList.forEach((row) => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName,
            });
          }
        });
        this.options.push(item);
      });
    },
    download(item) {
      item.pdtArr.forEach((i) => {
        window.open(i, "_blank");
      });
    },
    linkUrl(url) {
      window.open(url);
    },
    reject(row) {
      let that = this;
      const ids = row == null || row.id == null ? this.ids : [row.id];
      this.$prompt("请输入驳回理由", "驳回信息", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "驳回理由不能为空",
      })
        .then(({ value }) => {
          let select = [];
          ids.forEach((id) => {
            that.developmentList.forEach((item) => {
              if (id == item.id) {
                select.push({ id: id, state: item.state });
              }
            });
          });

          let row = {
            qxDevelopmentExamineList: select,
            explain: value,
          };
          return reject(row).then((res) => {
            if (res.code === 200) {
              that.$message.success("审批成功");
              that.getList();
            }
          });
        })
        .catch(() => {});
    },
    agree(row) {
      let that = this;
      const ids = row == null || row.id == null ? this.ids : [row.id];
      let select = [];
      ids.forEach((id) => {
        that.developmentList.forEach((item) => {
          if (id == item.id) {
            select.push({ id: id, state: item.state });
          }
        });
      });

      agree({ qxDevelopmentExamineList: select }).then((res) => {
        if (res.code === 200) {
          that.$message.success("审批成功");
          that.getList();
        }
      });
    },
    showHide(row) {
      let that = this;
      let params = {
        id: row.id,
        showHide: row.showHide == 0 ? 1 : 0,
      };
      return showhide(params).then((res) => {
        if (res.code === 200) {
          that.$message.success(row.showHide == 0 ? "隐藏" : "显示") + "成功";
          that.getList();
        }
      });
    },

    /** 重置按钮操作 */
    resetQuery() {
      if (this.$refs["queryForm"]) {
        this.$refs["queryForm"].resetFields();
      }
      this.queryParams.sceneIds = [];
      this.sceneIds = [];
      this.queryParams.industryId = null;
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    getLogout() {
      let that = this;
      that
        .$confirm("是否确认退出登录?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(function () {
          localStorage.removeItem("examinetoken");
          that.$router.push({ name: "examinelogin" });
        })
        .catch(() => {});
    },
    getIndustryList() {
      listIndustry().then((response) => {
        this.industryList = response.rows;
        listScene().then((response) => {
          this.sceneList = response.rows;
          this.setOption();
        });
      });
    },
    getUserList() {
      let that = this;
      userlist({}).then((res) => {
        if (res.code === 200) {
          that.userList = res.data;
        }
      });
    },
    getList() {
      let that = this;
      this.loading = true;
      developmentlist(this.queryParams).then((res) => {
        if (res.code === 200) {
          that.developmentList = res.rows;
          that.total = res.total;
          that.loading = false;
        }
      });
    },
    getDetailList(row) {
      let that = this;
      that.previewPage = true;
      let params = {
        developmentId: row.id,
      };
      this.clearList();
      servicelist(params).then((res) => {
        if (res.code === 200) {
          this.scene = res.data.scene;
          this.serviceList = res.data.serviceList;
          this.serviceList.forEach((row) => {
            if (row.serviceType === 0) {
              this.selectDeviceTableList.push(row);
            } else if (row.serviceType === 1) {
              this.selectSoftwareTableList.push(row);
            } else if (row.serviceType === 2) {
              this.selectIntegrateTableList.push(row);
            } else if (row.serviceType === 3) {
              this.selectOtherTableList.push(row);
            } else if (row.serviceType === 4) {
              this.qxCaseList.push(row);
            }
          });
          this.qxLinkList = res.data.linkList;
          this.qxCaseList = res.data.caseList;
          this.developmentCityList = res.data.developmentCityList;
          this.development = res.data.development;

          this.qxCaseList.forEach((item) => {
            let pdtArr = [];
            this.$set(item, "caseUrl", JSON.parse(item.caseUrl));
            item.caseUrl.forEach((i) => {
              i.type = i.url.substring(i.url.length - 3);
              if (i.type == "png" || i.type == "jpg" || i.type == "jpeg")
                item.img = i.url;
              if (i.type == "mp4") item.mp4 = i.url;
              if (
                i.type == "pdf" ||
                i.type == "txt" ||
                i.type == "ppt" ||
                i.type == "docx" ||
                i.type == "doc" ||
                i.type == "pptx"
              ) {
                item.pdtname = i.name;
                pdtArr.push(i.url);
              }
              item.pdtArr = pdtArr;
            });
          });

          this.industryCode = res.data.qxIndustry.industryCode;

          if (
            this.developmentCityList != null &&
            this.developmentCityList.length === 340
          ) {
            this.developmentCityList = [{ label: "全国" }];
          } else {
            //显示市级城市
            this.developmentCityList.forEach((row) => {
              this.optionsProvince.forEach((item) => {
                if (row.provinceId == item.value) {
                  item.children.forEach((child) => {
                    if (row.cityId == child.value) {
                      row.label = child.label;
                    }
                  });
                }
              });
            });
          }

          this.$refs.previewRef.imgUrl =
            this.development == null || this.development.imgUrl == null
              ? null
              : this.development.imgUrl;
          this.$refs.previewRef.sendName = this.scene.sceneName;
          console.log("asdfasdfa" + this.$refs.previewRef.imgUrl);
          this.$refs.previewRef.solutions = {
            phone: "137xxxxxxxx",
            number: parseInt(this.development.number), //全国案例数
            achieveNumber: parseInt(this.development.achieveNumber), //可支撑人数,
            qxDevelopmentCityList: this.developmentCityList,
            qxLinkList: this.qxLinkList,
            demoVos: this.development.qxProductList
              ? this.development.qxProductList
              : [],
            qxCaseVoList: this.qxCaseList,
            qxServiceList: [
              ...this.selectDeviceTableList,
              ...this.selectSoftwareTableList,
              ...this.selectIntegrateTableList,
              ...this.selectOtherTableList,
            ],
          };

          //显示查询详情图片
          this.previewPage = true;
        }
      });
    },
    clearList() {
      this.serviceList = [];
      this.selectDeviceTableList = []; //表格数据
      this.selectSoftwareTableList = []; //软件数据
      this.selectIntegrateTableList = []; //集成服务
      this.selectOtherTableList = []; //集成服务
      this.qxCaseList = [];
      this.qxLinkList = [];
    },

    setProvince() {
      this.optionsProvince = getProvince();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      //this.ids = selection.map(item => item.id);
      this.ids = [];
      selection.forEach((element) => {
        if (element.state === 0) {
          this.ids.push(element.id);
        }
      });
      this.multiple = !this.ids.length;
    },
  },
};
</script>

<style lang="scss" scoped>
.index {
  width: 100%;
  height: 100vh;
  font-family: FZLTXHK--GBK1-0, FZLTXHK--GBK1;
  .head {
    float: left;
    width: 100%;
    background: #f0f6f9;
    text-align: center;
    height: 3.3vw;
    font-size: 1.3vw;
    font-family: FZLTZHK--GBK1-0, FZLTZHK--GBK1;
    font-weight: normal;
    color: #131415;
    line-height: 3.3vw;
  }
  .right {
    position: absolute;
    right: 0;
    top: 0;
    height: 3.3vw;
    width: 35vw;
    padding-right: 1.7vw;
    .myinfo {
      float: right;
      width: 16.3vw;
      font-size: 1vw;
      color: #131415;
      cursor: pointer;
      text-align: right;
      margin-right: 1.5vw;
    }
    .login-out {
      color: #ff5656;
      padding-left: 1.3vw;
      border-left: 1px solid #d8d8d8;
      float: right;
      width: 4.3vw;
      font-size: 1vw;
      height: 1vw;
      line-height: 1vw;
      margin-top: 1.15vw;
      cursor: pointer;
    }
    .update-pwd {
      color: #000;
      margin-right: 1.3vw;
      padding-left: 1.3vw;
      border-left: 1px solid #d8d8d8;
      float: right;
      width: 4.3vw;
      font-size: 1vw;
      height: 1vw;
      line-height: 1vw;
      margin-top: 1.15vw;
      cursor: pointer;
    }
  }
  .title {
    float: left;
    width: 96.6vw;
    margin: 0 1.7vw;
    box-sizing: border-box;
    border-bottom: 2px solid #d1d1d1;

    .label {
      font-size: 1.7vw;
      font-weight: normal;
      color: #131415;
      line-height: 1.7vw;
      width: 100%;
      float: left;
      height: 5.8vw;
      line-height: 5.8vw;
      text-align: center;
    }
    .input {
      float: left;
      width: 8.3vw;
      height: 2.8vw;
      border-radius: 0.3vw;
      border: 1px solid #dcdcdc;
      font-size: 1vw;
      box-sizing: border-box;
      padding: 0 1.3vw;
      color: #1c1c1d;
      margin-top: 1.5vw;
      margin-left: 0.8vw;
    }
    .btn-upload {
      width: 8.3vw;
      height: 2.8vw;
      border-radius: 0.3vw;
      background: #0052d9;
      font-size: 0.8vw;
      text-align: center;
      line-height: 2.8vw;
      margin-left: 1vw;
      color: #fff;
      float: left;
      border: none;
      cursor: pointer;
      margin-top: 1.5vw;
    }
  }
}

.bg-img {
  width: 1216px;
  height: 689px;
  background: url(../../../../assets/images/preview/bg.png) no-repeat center
    center;
  background-size: 100%;
}
</style>
