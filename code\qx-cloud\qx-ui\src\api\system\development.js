import request from '@/utils/request'

// 查询定制开发列表
export function listDevelopment(query) {
  return request({
    url: '/system/development/list',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 3,
  })
}

// 查询定制开发详细
export function getDevelopment(id) {
  return request({
    url: '/system/development/' + id,
    method: 'get'
  })
}
// 查询定制开发详细
export function getDevelopmentDetail(id) {
  return request({
    url: '/system/development/detail/' + id,
    method: 'get'
  })
}


// 新增定制开发
export function addDevelopment(data) {
  return request({
    url: '/system/development',
    method: 'post',
    data: data
  })
}

// 修改定制开发
export function updateDevelopment(data) {
  return request({
    url: '/system/development',
    method: 'put',
    data: data
  })
}

// 删除定制开发
export function delDevelopment(id) {
  return request({
    url: '/system/development/' + id,
    method: 'delete'
  })
}

// 导出定制开发
export function exportDevelopment(query) {
  return request({
    url: '/system/development/export',
    method: 'get',
    params: query
  })
}