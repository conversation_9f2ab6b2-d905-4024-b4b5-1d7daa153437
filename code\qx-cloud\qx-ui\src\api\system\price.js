import request from '@/utils/request'

// 查询网络报价项列表
export function listPrice(query) {
  return request({
    url: '/system/price/list',
    method: 'get',
    params: query
  })
}

// 查询网络报价项详细
export function getPrice(id) {
  return request({
    url: '/system/price/' + id,
    method: 'get'
  })
}

// 新增网络报价项
export function addPrice(data) {
  return request({
    url: '/system/price',
    method: 'post',
    data: data
  })
}

// 修改网络报价项
export function updatePrice(data) {
  return request({
    url: '/system/price',
    method: 'put',
    data: data
  })
}

// 删除网络报价项
export function delPrice(id) {
  return request({
    url: '/system/price/' + id,
    method: 'delete'
  })
}

// 导出网络报价项
export function exportPrice(query) {
  return request({
    url: '/system/price/export',
    method: 'get',
    params: query
  })
}