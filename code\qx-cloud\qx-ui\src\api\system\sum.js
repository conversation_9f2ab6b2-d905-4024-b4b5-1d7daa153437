import request from '@/utils/request'
// 平台总体数据
export function clicksumpopulation(query) {
    return request({
        url: '/system/clicksum/population',
        method: 'get',
        params: query
    })
}
// 各省用户数据
export function clicksumusercount(query) {
    return request({
        url: '/system/clicksum/usercount',
        method: 'get',
        params: query
    })
}

// 导出前端客户用户
export function clickSumusercountExport(query) {
    return request({
      url: '/system/clicksum/usercountexport',
      method: 'get',
      params: query,
      timeout: 1000*60*15
    })
  }

// 各行业用户分省点击
export function provinceclick(query) {
    return request({
        url: '/system/clicksum/provinceclick',
        method: 'get',
        params: query,
    })
}

// 渠道数据


export function channel(query) {
    return request({
        url: '/system/clickrecords/channel/useNumber',
        method: 'get',
        params: query,
    })
}
// 日活跃人数
export function datesum(query) {
    return request({
        url: '/system/clicksum/datesum',
        method: 'get',
        params: query
    })
}

export function monthsum(query) {
    return request({
        url: '/system/clicksum/monthsum',
        method: 'get',
        params: query
    })
}

export function casemainregion(query) {
    return request({
        url: '/system/clicksum/casemainregion',
        method: 'get',
        params: query
    })
}
export function casemainsum(query) {
    return request({
        url: '/system/clicksum/casemainsum',
        method: 'get',
        params: query
    })
}

// 各行业用户分省点击
export function sumavg(query) {
    return request({
        url: '/system/clicksum/sumavg',
        method: 'get',
        params: query
    })
}

//获取行业
export function getindustrylist(query) {
    return request({
        url: '/system/clicksum/industrylist',
        method: 'get',
        params: query
    })
}


// 获取行业合作伙伴列表
export function getuserlist(query) {
    return request({
        url: '/system/clicksum/getqxuser/',
        method: 'get',
        params: query
    })
}
export function getregionuser(query) {
    return request({
        url: '/system/clicksum/getregionuser/',
        method: 'get',
        params: query
    })
}


export function getdetailuser(query) {
    return request({
        url: '/system/clicksum/getdetailuser/',
        method: 'get',
        params: query
    })
}



export function provincialSecondaryAdministrator() {
    return request({
        url: '/system/clicksum/provincialSecondaryAdministrator',
        method: 'get'
    })
}
