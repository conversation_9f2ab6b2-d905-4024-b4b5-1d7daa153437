{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\api\\system\\popular.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\api\\system\\popular.js", "mtime": 1755845888319}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\babel.config.js", "mtime": 1744356874228}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747036191864}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1747036188749}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9Temh0YS9EZXNrdG9wL1x1NEVFM1x1NzgwMS90ZW1wX2lzaG93L2lTaG93X01hcmtldF9QbGF0Zm9ybV9RWC9jb2RlL3F4LWNsb3VkL3F4LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdC5qcyIpLmRlZmF1bHQ7Ck9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5hZGRQb3B1bGFyID0gYWRkUG9wdWxhcjsKZXhwb3J0cy5kZWxQb3B1bGFyID0gZGVsUG9wdWxhcjsKZXhwb3J0cy5nZXRQb3B1bGFyID0gZ2V0UG9wdWxhcjsKZXhwb3J0cy5saXN0UG9wdWxhciA9IGxpc3RQb3B1bGFyOwpleHBvcnRzLnVwZGF0ZVBvcHVsYXIgPSB1cGRhdGVQb3B1bGFyOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i54Ot6Zeo6K+N5YiX6KGoCmZ1bmN0aW9uIGxpc3RQb3B1bGFyKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvaG9tZS9wb3B1bGFyL21hbmFnZS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoueDremXqOivjeivpue7hgpmdW5jdGlvbiBnZXRQb3B1bGFyKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvaG9tZS9wb3B1bGFyL21hbmFnZS8nICsgaWQsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOaWsOWinueDremXqOivjQpmdW5jdGlvbiBhZGRQb3B1bGFyKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9ob21lL3BvcHVsYXIvbWFuYWdlL2FkZCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS554Ot6Zeo6K+NCmZ1bmN0aW9uIHVwZGF0ZVBvcHVsYXIoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL2hvbWUvcG9wdWxhci9tYW5hZ2UvdXBkJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTng63pl6jor40KZnVuY3Rpb24gZGVsUG9wdWxhcihpZHMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9ob21lL3BvcHVsYXIvbWFuYWdlL2RlbCcsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IHsKICAgICAgaWRzOiBpZHMKICAgIH0KICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPopular", "query", "request", "url", "method", "params", "getPopular", "id", "addPopular", "data", "updatePopular", "delPop<PERSON>", "ids"], "sources": ["C:/Users/<USER>/Desktop/代码/temp_ishow/iShow_Market_Platform_QX/code/qx-cloud/qx-ui/src/api/system/popular.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询热门词列表\nexport function listPopular(query) {\n  return request({\n    url: '/home/<USER>/manage/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询热门词详细\nexport function getPopular(id) {\n  return request({\n    url: '/home/<USER>/manage/' + id,\n    method: 'get'\n  })\n}\n\n// 新增热门词\nexport function addPopular(data) {\n  return request({\n    url: '/home/<USER>/manage/add',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改热门词\nexport function updatePopular(data) {\n  return request({\n    url: '/home/<USER>/manage/upd',\n    method: 'post',\n    data: data\n  })\n}\n\n// 删除热门词\nexport function delPopular(ids) {\n  return request({\n    url: '/home/<USER>/manage/del',\n    method: 'post',\n    data: { ids: ids }\n  })\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,EAAE,EAAE;EAC7B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB,GAAGI,EAAE;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,UAAUA,CAACC,GAAG,EAAE;EAC9B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAE;MAAEG,GAAG,EAAEA;IAAI;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}]}