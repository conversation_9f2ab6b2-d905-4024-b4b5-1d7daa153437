<template>
  <div class="app-container">
    <div class="partner-bg"></div>
    <div class="partner">
      <div class="partner-left">
        <div class="partner-left-title"></div>
        <div class="partner-left-list">
          <div class="partner-left-item" v-for="(item,index) in  industryList" :key="index" @click="selectItemClick(item.industryCode)" :class="selectItem==item.industryCode ? 'acitve' : ''">{{item.industryName}}
          </div>
        </div>
      </div>
      <div class="partner-right">
        <div style="height: 120rem;border-bottom: 0;background: rgba(0, 0, 0, 0);"></div>
        <div class="partner-right-list">
          <div v-for="(item,index) in userList" :key="index">
            <div style="text-align: left;padding-left: 10rem;">{{item.companyName}}</div>
            <div style="padding-right: 80rem;">{{item.timeLength.toFixed(2)}}</div>
            <div style="padding-left: 20rem">{{item.clickCount}}</div>
          </div>
          <div v-if="userList.length==0">
            <div style="text-align: left;padding-left: 10rem;"></div>
            <div style="padding-right: 80rem;">没有数据</div>
            <div style="padding-left: 20rem"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getindustrylist, getuserlist } from '@/api/system/sum.js'
import { setScale } from '@/utils/setScale'
export default {
  name: "Config",
  data() {
    return {
      list: [
        { name: '冶金' },
        { name: '电力' },
        { name: '教育' },
        { name: '工厂' },
        { name: '矿山(井工矿)' },
        { name: '矿山(露天矿矿)' },
      ],
      selectItem: '',
      industryList: [],
      userList: [],
    }
  },
  created() {
    setScale();
    window.onresize = () => {
      setScale();
    };
    this.getIndustryData();
  },
  methods: {
    selectItemClick(code) {
      this.selectItem = code;
      this.getuserlistData(code);
    },
    getuserlistData(value) {
      getuserlist(value).then(res => {
        this.userList = res.data;
      });
    },
    getIndustryData() {
      getindustrylist().then(res => {
        this.industryList = res.data;
        if (this.industryList.length > 0)
          this.selectItem = this.industryList[0].industryCode;
        this.getuserlistData(this.selectItem);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  padding: 40rem;

  /* width: 1860rem; */
  .partner-bg {
    width: 1840rem;
    height: 110rem;
    background: url(../../../assets/images/click/partner-bg.png) no-repeat
      center center;
    background-size: 100%;
    text-align: center;
  }

  .partner {
    display: flex;

    .partner-left {
      width: 383rem;
      margin-right: 20rem;

      .partner-left-title {
        width: 176rem;
        height: 35rem;
        background: url(../../../assets/images/click/partner-left-title.png)
          no-repeat center center;
        background-size: 100%;
        text-align: center;
        margin-bottom: 20rem;
        margin-top: 55rem;
      }
      .partner-left-list {
        overflow-y: auto;
        overflow-x: hidden;
        height: 754rem;
      }
      .partner-left-item {
        cursor: pointer;
        width: 373rem;
        height: 118rem;
        background: #f1f5f7;
        line-height: 118rem;
        margin: 10rem 0;
        /* opacity: 0.1; */
        border: 2px solid #fdfeff;
        font-size: 30rem;
        font-family: MiSans-Heavy, MiSans;
        font-weight: 800;
        text-align: center;
        color: #1366bf;
      }

      .acitve {
        background: linear-gradient(#30d3a9, #1078c1);
        color: #fff;
      }
    }

    .partner-right {
      width: 1437rem;
      height: 824rem;
      background: url(../../../assets/images/click/partner-right.png) no-repeat
        center center;
      background-size: 100%;
      text-align: center;

      margin-top: 48rem;

      .partner-right-list {
        height: 708rem;
        overflow-y: auto;

        > div {
          height: 95rem;
          width: 93%;
          border-bottom: 1px solid #e6eff9;
          margin: auto;
          display: flex;

          > div {
            flex: 1;
            text-align: center;
            font-size: 24rem;
            color: #1366bf;
            font-weight: 600;
            line-height: 95rem;
          }
        }
      }
    }
  }
}
</style>