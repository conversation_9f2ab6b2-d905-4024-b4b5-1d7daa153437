<template>
    <div class="app-container">
        <div class="app-conter-box">
            <div>
                <div class="dashboard-editor-header mt20">
                    各功能板块使用时长
                    <div class="date">
                        <el-date-picker v-model="dayDate" @change="getList()" style="width:300px" format="yyyy/MM/dd"
                            prefix-icon="" :clearable="false" ref="elDatePickControl" type="daterange"
                            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                                disabledDate: time => {
                                    const today = new Date();
                                    // 禁止选择今天之后的时间
                                    if (time.getTime() > today.getTime()) {
                                        return true;
                                    }
                                    // 禁止结束时间选择今天
                                    if (this.dayDate && this.dayDate.length === 2) {
                                        return time.getTime() === today.setHours(0, 0, 0, 0);
                                    }
                                    return false;
                                }
                            }"></el-date-picker>
                    </div>
                </div>
                <!-- <div class="classftion"></div> -->
                <div class="classftion-box">
                    <div class="classftion-left-box">
                        <div class="classftion-left-content">
                            <div class="classftion-left-box-tlite">
                                <img src="@/assets/images/index/biaoti.png" alt="" />
                                平均使用时长(秒)
                            </div>
                            <div v-for="(item, index) in titleOptions" :key="index" class="classftion-left">
                                <div>
                                    <img style="width: 56px;" :src="item.url" alt="" />
                                </div>
                                <div>
                                    <div class="classftion-left-name">{{ item.name }}</div>
                                    <div class="classftion-left-num">{{ item.timeLength }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="classftion-right-box">
                        <div class="classftion-left-box-tlite">
                            <img src="@/assets/images/index/biaoti.png" alt="" />
                            各行业使用详情(秒)
                        </div>

                        <!-- <div class="classftion-right-tltle"></div> -->
                        <div class="classftion-list222">
                            <div>行业</div>
                            <div v-for="(item, index) in titleOptions" :key="index">{{ item.name }}</div>
                        </div>

                        <div class="classftion-list222ss">
                            <div v-for="(item, index) in qxIndustryList" :key="index">
                                <div>{{ item.industryName }}</div>
                                <div>{{ item.timeLength1 }}</div>
                                <div>{{ item.timeLength4 }}</div>
                                <div>{{ item.timeLength5 }}</div>
                                <div>{{ item.timeLength6 }}</div>
                                <div>{{ item.timeLength7 }}</div>
                                <div>{{ item.timeLength8 }}</div>
                                <div>{{ item.timeLength9 }}</div>
                                <div>{{ item.timeLength12 }}</div>
                                <div>{{ item.timeLength13 }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <template v-for="(item, maxindex) in (qxIndustryList.length - 2 - ((qxIndustryList.length - 2) % 5)) / 5 + ((qxIndustryList.length - 2) % 5 > 0 ? 1 : 0)">
            <div :key="maxindex">
                <div class="classftion-box">
                    <div class="classftion-right-box">
                        <div class="classftion-right-big-tltle"></div>
                        <div class="classftion-list222">
                            <template v-for="(item, index) in qxIndustryList">
                                <div :key="index" v-if="maxindex * 5 + 2 <= index && index < (maxindex + 1) * 5 + 2" class="classftion-list2222">
                                    <div class="classftion-list222-tltle">
                                        {{ item.industryName }}
                                    </div>

                                    <template v-for="(row, itemindex) in item.itemList">
                                        <div class="classftion-list222-item" v-if="(item.industryCode != 'HY030' && row.name !== '产品介绍' && row.name !== '行业场景') || (item.industryCode == 'HY030' && row.name !== '网络方案' && row.name !== '商业价值')" :key="itemindex">
                                            <div style="font-size: 24px;font-weight: 800;color: #1366BF;">{{ row.name }}</div>
                                            <div style="font-size:28px;color:#30D4A9;font-weight: 800">{{ row.timeLength.toFixed(2) }}</div>
                                        </div>
                                    </template>
</div>
</template>
</div>
</div>
</div>
</div>
</template> -->
    </div>
</template>

<script>
import { sumavg } from '@/api/system/sum.js'
import { setScale } from '@/utils/setScale'
export default {
    name: 'Config',
    data() {
        return {
            // topImg: require('../../../assets/images/click/平均时长.png'),
            titleOptions: [
                { id: 1, name: '行业场景', url: require('../../../assets/images/index/行业场景.png') },
                { id: 4, name: '网络方案', url: require('../../../assets/images/index/网络方案.png') },
                { id: 5, name: '商业价值', url: require('../../../assets/images/index/商业价值.png') },
                { id: 6, name: '落地案例', url: require('../../../assets/images/index/落地案例.png') },
                { id: 7, name: 'VR看现场', url: require('../../../assets/images/index/vr看现场.png'), timeLength: '0' },
                { id: 8, name: '集成报价', url: require('../../../assets/images/index/行业生态链.png') },
                { id: 9, name: '行业生态链', url: require('../../../assets/images/index/集成报价器.png') },
                { id: 12, name: '产品介绍', url: require('../../../assets/images/index/产品介绍.png'), timeLength: '0' },
                { id: 13, name: '基础能力', url: require('../../../assets/images/index/基础能力.png'), timeLength: '0' }
            ],
            qxIndustryList: [],

            list1: [
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                },
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                }
            ],
            list2: [
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                },
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                },
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                },
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                },
                {
                    tltle: '电力',
                    name: '行业场景',
                    num: 28
                }
            ],
            dayDate: [],
            pamas: {}
        }
    },

    created() {
        setScale()
        window.onresize = () => {
            setScale()
        }
        this.dayDate = this.$route.query.dayDate.split(',')
        this.pamas = {
            startDate: this.parseTime(this.dayDate[0], '{y}-{m}-{d}'),
            endDate: this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
        }
        this.getList()
        // this.getList()
    },
    methods: {
        getList() {
            this.pamas = {
                startDate: this.parseTime(this.dayDate[0], '{y}-{m}-{d}'),
                endDate: this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            console.log("测试日志：", this.titleOptions)
            sumavg(this.pamas).then(res => {
                this.titleOptions.forEach(row => {
                    res.data.avgList.forEach(element => {
                        if (element.clickType == row.id) this.$set(row, 'timeLength', element.timeLength.toFixed(2))
                    })
                })
                // 假设 res.data.qxIndustryList 和 res.data.sumList 是从 API 获取的数据
                // 假设 res.data.qxIndustryList 和 res.data.sumList 是从 API 获取的数据
                // 假设 res.data.qxIndustryList 和 res.data.sumList 是从 API 获取的数据
                const qxIndustryList = res.data.qxIndustryList
                const sumList = res.data.sumList

                // 遍历 qxIndustryList
                qxIndustryList.forEach(industry => {
                    // 初始化 9 个字段
                    this.titleOptions.forEach(option => {
                        industry[`timeLength${option.id}`] = 0
                        industry[`name${option.id}`] = option.name
                    })

                    // 找到与 industry.industryCode 相同的 sumList 项
                    sumList.forEach(sum => {
                        if (sum.clickValue === industry.industryCode) {
                            // 找到与 sum.clickType 相同的 titleOptions 项
                            const matchingTitleOption = this.titleOptions.find(option => option.id === parseInt(sum.clickType))

                            if (matchingTitleOption) {
                                // 将 timeLength 的值添加到 qxIndustryList 中对应的字段
                                industry[`timeLength${sum.clickType}`] = sum.timeLength.toFixed(2)
                            }
                        }
                    })
                })
                this.qxIndustryList = qxIndustryList
                //this.dataList = res.data;
            })
        }
    }
}
</script>
<style lang="scss" scoped>
page {
    background: #f0f2f5;
}

.app-container {
    padding: 0;
    width: 100%;
    font-family: '思源黑体';
    background: #f0f2f5;
    min-height: calc(100vh - 84px);

    .app-conter-box {
        width: 1200px;
        margin: auto;
        overflow: hidden;

        .dashboard-editor-header {
            font-size: 24px;
            font-weight: 900;
            color: #0066e4;
            display: flex;
            align-items: center;
            height: 60px;
            border-radius: 10px;
            opacity: 1;
            padding: 0 24px;
            box-sizing: border-box;
            background: #ffffff;

            .date {
                flex: 1;
                text-align: right;
                font-size: 18px;
                font-weight: 500;
                position: relative;
                padding-right: 10px;
                cursor: pointer;
            }
        }
    }

    .classftion-box {

        // display: flex;
        // display: flex;
        .classftion-left-content {
            margin: 30px 0;
            padding: 30px;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            /* 3列 */
            grid-gap: 30px;
            /* 列间距 */
            justify-items: center;
            /* 水平居中 */
            align-items: center;
            /* 垂直居中 */

            // height: 326px;
        }

        .classftion-left-box {
            border-radius: 10px;
            background: #fff;

            &-tlite {
                padding: 20px;
                font-size: 20px;
                font-weight: 700;
                color: #1c1c1c;
            }

            .classftion-left {
                background: #ffffff;
                box-shadow: 0 2px 4px 0 #0a515940;
                width: 200px;
                height: 80px;
                border-radius: 4px;
                opacity: 1;
                background: #ffffff;
                box-shadow: 0 2px 4px 0 #0a515940;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-around;
                align-items: center;

                .classftion-left-name {
                    font-size: 14px;
                    font-weight: 700;
                }

                .classftion-left-num {
                    font-size: 24px;
                    font-weight: 900;
                    font-family: '思源黑体';
                    text-align: right;
                    color: #0066e4;
                }
            }
        }

        .classftion-right-box {
            // width: 1000px;
            width: 100%;
            // height: 575px;
            border-radius: 10px;
            padding: 20px;
            box-sizing: border-box;
            background: #fff;

            .classftion-right-tltle {
                padding: 20px;
                font-size: 20px;
                font-weight: 700;
                color: #1c1c1c;
            }
        }

        .classftion-right-big-tltle {
            width: 1840px;
            height: 110px;
            background: url(../../../assets/images/click/big-title.png) no-repeat center center;
            background-size: 100%;
            text-align: center;
            margin: 56px 0 36px 0;
        }

        .classftion-list222 {
            display: flex;
            width: 100%;
            margin: 20px 0;
            justify-content: space-around;
            height: 40px;
            font-size: 14px !important;
            color: #000;
            background: #eaf5f8;
            align-items: center;
            box-sizing: border-box;

            >div {
                flex: 1;
                text-align: center;
            }

            // > div {
            //     width: 415px;
            //     height: 690px;
            //     background: url(../../../assets/images/click/classlistbg.png) no-repeat center center;
            //     background-size: 100%;
            //     text-align: center;
            //     margin-right: 30px;

            //     .classftion-list222-tltle {
            //         height: 77px;
            //         padding-left: 23px;
            //         line-height: 77px;
            //         color: #14be98;
            //         font-size: 28px;
            //         font-weight: 800;
            //         text-align: left;
            //     }

            //     .classftion-list222-item {
            //         height: 87.5px;
            //         line-height: 87.5px;
            //         display: flex;
            //         align-items: center;
            //         justify-content: space-between;
            //         padding: 0 45px 0 37px;

            //         > div {
            //         }
            //     }
            // }

            // .classftion-list2222 {
            //     width: 336px;
            //     height: 690px;
            //     background: url(../../../assets/images/click/classlist2bg.png) no-repeat center center;
            //     background-size: 100%;
            //     text-align: center;
            //     margin-right: 35px;
            // }
        }
    }
}

.classftion-list2222:last-child,
.classftion-list222>div:last-child {
    margin-right: 0 !important;
}

.classftion-list222ss {
    color: #2269de;
    font-weight: 700;
    // max-height: 244px;
    // overflow-y: auto;
}

.classftion-list222ss>div {
    display: flex;
    align-items: center;
    width: 100%;
    font-size: 14px;
    height: 40px;
    border-bottom: 1px dashed #cccccc;
    /* 添加下边框虚线 */
    justify-content: space-around;
    margin-top: 20px;

    >div {
        flex: 1;
        text-align: center;
    }
}

.classftion-list222ss::-webkit-scrollbar {
    display: none;
}

.classftion-left-box-tlite {
    padding: 0 !important;
    height: 100%;
    display: flex;
    align-items: flex-start;

    img {
        width: 18px;
        height: 24px;
        margin-right: 20px;
    }
}
</style>
