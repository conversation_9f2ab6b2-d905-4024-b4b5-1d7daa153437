import request from '@/utils/request'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/qxuserto/list',
    method: 'get',
    params: query
  })
}
// 查询用户列表
export function examinelistUser(query) {
  return request({
    url: '/system/qxuserto/examinelist',
    method: 'get',
    params: query
  })
}
// 查询用户详细
export function getUser(id) {
  return request({
    url: '/system/qxuserto/' + id,
    method: 'get'
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/qxuserto',
    method: 'post',
    data: data
  })
}
// 新增用户
export function caseAddUser(data) {
  return request({
    url: '/system/qxuserto/caseadd',
    method: 'post',
    data: data
  })
}




// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/qxuser',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser(id) {
  return request({
    url: '/system/qxuserto/' + id,
    method: 'delete'
  })
}

// 导出用户
export function exportUser(query) {
  return request({
    url: '/system/qxuserto/export',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 3,
  })
}
export function getDict(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: query,
    timeout: 1000 * 60 * 3
  })
}
