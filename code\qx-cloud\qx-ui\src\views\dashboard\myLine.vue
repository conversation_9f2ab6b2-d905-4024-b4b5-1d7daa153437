<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        },
        title: {
            type: String,
            default: ''
        },
        List: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            chart: null
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')
            console.log(this.List, '--------------------------')
            // 提取 Xdata 和 Ydata
            let Xdata = this.List.map(item => item.provinceName)
            let Ydata = this.List.map(item => item.peopleNumber)
            this.chart.setOption({
                title: {
                    text: this.title,
                    left: 'left', // 设置标题位置为左上角
                    top: 'top', // 设置标题位置为左上角
                    textStyle: {
                        fontWeight: 'bold', // 设置标题粗体
                        fontFamily: '思源黑体',
                        color: '#000', // 设置标题颜色为黑色
                        fontSize: 18 // 设置标题字体大小
                    }
                },
                grid: {
                    left: '0%', // 调整左边距
                    right: '0%', // 调整右边距
                    top: '20%', // 调整上边距，为标题留出空间
                    bottom: '10%', // 调整下边距
                    containLabel: true // 确保标签在图表区域内
                },
                xAxis: {
                    type: 'category',
                    data: Xdata,
                    axisLabel: {
                        color: '#000' // 设置字体颜色为黑色
                    },
                    axisTick: {
                        show: false // 移除 x 轴的刻度线
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#000' // 设置 x 坐标轴线的颜色为黑色
                        }
                    },
                    boundaryGap: true // 设置边界间隙
                },
                yAxis: {
                    type: 'value',
                    minInterval: 1,
                    splitLine: {
                        lineStyle: {
                            type: 'dashed', // 设置分割线为虚线
                            color: '#D5D7D9' // 设置分割线颜色
                        }
                    },
                    axisLabel: {
                        color: '#D5D7D9', // 设置字体颜色
                        formatter: value => {
                            return value
                        }
                    },
                    axisTick: {
                        show: false // 移除 y 轴的刻度线
                    },
                    axisLine: {
                        show: false // 移除 y 轴的坐标轴线
                    }
                },
                series: [
                    {
                        type: 'line',
                        data: Ydata,
                        lineStyle: {
                            width: 3, // 设置折线宽度为3px
                            color: '#2269de' // 设置折线颜色
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(0, 106, 255, 1)' }, // 最高点颜色，带透明度

                                { offset: 1, color: 'rgba(104, 229, 250, 0.2)' } // 最低点颜色，带透明度
                            ])
                        },
                        label: {
                            show: true,
                            position: 'top', // 设置标签位置在折线顶点上方
                            textStyle: {
                                color: '#000', // 设置标签颜色为黑色
                                fontSize: 12 // 设置标签字体大小
                            }
                        },
                        smooth: false // 设置折线为直线
                    }
                ]
            })
        }
    }
}
</script>
