<template>
  <div v-loading="loading" :element-loading-text="loadingText" element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)" class="scene">

    <!-- <div class="panorama-ifram" v-if="dialogTableVisible">
      <img :src="colseImg" @click="panoramaClose()" :style="isShowClose?'':'display:none'" class="preview-img" />
    </div> -->
    <Preview ref="previewRef" v-show="previewPage" @change-preview="handlePreviewPageChange" />

    <div v-show="!previewPage" class="main">
      <div class="content" style="overflow-y: hidden">
        <div style="height: 2.1vw;" class="table-title">
          <div class="table-label-text">一、场景编辑</div>
        </div>
        <div class="device-select">
          <div ref="deviceImg" id="deviceImgId" class="right-back">
            <div class="right-back-panorama" v-if="panorama==null||panorama.imgUrl==null">
              <div class="right-back-content" @click="panoramaEdit()">
                点击编辑效果图
              </div>
            </div>
            <img v-else class="right-back-img" @click="panoramaEdit()" :src="panorama.imgUrl" />
          </div>
          <!-- <div class="panorama-footer">
            <el-button class="panorama-btn" @click="panoramaEdit()">编辑效果图</el-button>
          </div> -->
        </div>
        <div style="height: 2.1vw;" class="table-title">
          <!-- <div class="table-btn">一键上传</div> -->
          <ExcelImport @change="ExportChange()" v-model="exportList" />
          <div class="table-btn" @click="handleExport()">报价模板下载</div>
          <div class="table-label-text">二、硬件类别（点击添加）</div>
        </div>

        <div class="table-div">
          <el-table :data="selectDeviceTableList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column v-if="industryCode=='HY001'||industryCode=='HY004'" prop="smallType" align="center" label="功能分类" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入功能分类" maxlength="50" v-model="scope.row.smallType">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="serviceName" align="center" label="硬件名称" width="180">
              <template slot-scope="scope">
                <el-input v-if="scope.row.deviceId==null" maxlength="100" placeholder="输入硬件名称" v-model="scope.row.serviceName">
                </el-input>
                <span v-else>{{ scope.row.serviceName }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="serviceExplain" align="center" label="硬件设备介绍">
              <template slot-scope="scope">
                <el-input placeholder="合作伙伴对已选中设备进行文字描述" maxlength="500" v-model="scope.row.serviceExplain">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="servicePrice" align="center" label="单价(元)" width="180">
              <template slot-scope="scope">
                <el-input-number :min="0" :max="999999" :controls="false" placeholder="单价(元)" width="150" v-model="scope.row.servicePrice" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceNumber" align="center" label="数量" width="180">
              <template slot-scope="scope">
                <el-input-number v-if="scope.row.deviceId==null" :min="1" :max="9999" :controls="false" placeholder="数量" width="150" v-model="scope.row.serviceNumber" style="text-align:center;" />

                <span v-else>{{scope.row.serviceNumber}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="serviceUnit" align="center" label="单位" width="180">
              <template slot-scope="scope">
                <el-input v-if="scope.row.deviceId==null" placeholder="输入硬件设备单位" class="serviceUnit" maxlength="5" v-model="scope.row.serviceUnit">
                </el-input>
                <span v-else>{{ scope.row.serviceUnit }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,0)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(0)" :src="timeSelect" />
          </div>
        </div>

        <div style="height: 2.1vw;" class="table-title">
          <div class="table-label-text">三、软件类别（点击添加）</div>
        </div>

        <div class="table-div">
          <el-table :data="selectSoftwareTableList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column v-if="industryCode=='HY001'||industryCode=='HY004'" prop="smallType" align="center" label="功能分类" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入功能分类" maxlength="50" v-model="scope.row.smallType">
                </el-input>
              </template>
            </el-table-column>

            <el-table-column prop="serviceName" align="center" label="软件名称" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入软件名称" maxlength="100" v-model="scope.row.serviceName">
                </el-input>
              </template>
            </el-table-column>

            <el-table-column prop="serviceExplain" align="center" label="软件介绍">
              <template slot-scope="scope">
                <el-input placeholder="请输入软件介绍" maxlength="500" type="textarea" v-model="scope.row.serviceExplain">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="servicePrice" align="center" label="单价(元)" width="180">
              <template slot-scope="scope">
                <el-input-number :min="0" :max="999999" :controls="false" placeholder="单价(元)" width="150" v-model="scope.row.servicePrice" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceNumber" align="center" label="数量" width="180">
              <template slot-scope="scope">
                <el-input-number :min="1" :max="9999" :controls="false" placeholder="数量" width="150" v-model="scope.row.serviceNumber" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceUnit" align="center" label="单位" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入软件单位" maxlength="5" class="serviceUnit" v-model="scope.row.serviceUnit">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,1)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(1)" :src="timeSelect" />
          </div>
        </div>

        <div v-show="industryCode!=='HY001'" style="height: 2.1vw;" class="table-title">
          <div class="table-label-text">四、集成服务（点击添加）</div>
        </div>

        <div v-show="industryCode!=='HY001'" class="table-div">
          <el-table :data="selectIntegrateTableList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column v-if="industryCode=='HY001'||industryCode=='HY004'" prop="smallType" align="center" label="功能分类" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入功能分类" maxlength="50" v-model="scope.row.smallType">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="serviceName" align="center" label="服务名称" width="180">
              <template slot-scope="scope">
                <el-input placeholder="请输入服务名称" maxlength="100" v-model="scope.row.serviceName">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="serviceExplain" align="center" label="服务介绍">
              <template slot-scope="scope">
                <el-input placeholder="请输入介绍" maxlength="500" type="textarea" v-model="scope.row.serviceExplain">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="servicePrice" align="center" label="单价(元)" width="180">
              <template slot-scope="scope">
                <el-input-number :min="0" :max="999999" :controls="false" placeholder="单价(元)" width="150" v-model="scope.row.servicePrice" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceNumber" align="center" label="数量" width="180">
              <template slot-scope="scope">
                <el-input-number :min="1" :max="9999" :controls="false" placeholder="数量" width="150" v-model="scope.row.serviceNumber" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceUnit" align="center" label="单位" width="180">
              <template slot-scope="scope">
                <el-input placeholder="请输入单位" maxlength="5" class="serviceUnit" v-model="scope.row.serviceUnit">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,2)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(2)" :src="timeSelect" />
          </div>
        </div>

        <div style="height: 2.1vw;" class="table-title">
          <div class="table-label-text">{{ industryCode !== 'HY001' ? '五' : '四' }}、其他服务 (点击添加)</div>
        </div>

        <div class="table-div">
          <el-table :data="selectOtherTableList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column v-if="industryCode=='HY001'" prop="smallType" align="center" label="功能分类" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入功能分类" v-model="scope.row.smallType">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="serviceName" align="center" label="服务名称" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入服务名称" maxlength="100" v-model="scope.row.serviceName">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="serviceExplain" align="center" label="服务介绍">
              <template slot-scope="scope">
                <el-input placeholder="请输入服务介绍" maxlength="500" type="textarea" v-model="scope.row.serviceExplain">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="servicePrice" align="center" label="单价(元)" width="180">
              <template slot-scope="scope">
                <el-input-number :min="0" :max="999999" :controls="false" placeholder="单价(元)" width="150" v-model="scope.row.servicePrice" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceNumber" align="center" label="数量" width="180">
              <template slot-scope="scope">
                <el-input-number :min="1" :max="9999" :controls="false" placeholder="数量" width="150" v-model="scope.row.serviceNumber" style="text-align:center;" />
              </template>
            </el-table-column>
            <el-table-column prop="serviceUnit" align="center" label="单位" width="180">
              <template slot-scope="scope">
                <el-input placeholder="输入服务单位" class="serviceUnit" maxlength="5" v-model="scope.row.serviceUnit">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,3)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(3)" :src="timeSelect" />
          </div>
        </div>
        <div style="height: 2.1vw;margin-top:4.4vw" class="table-title">
          <div class="name">{{ scene.sceneName }}</div>
          <div class="table-label-text">场景1-案例上传</div>
        </div>

        <div class="table-div">
          <el-table :data="qxCaseList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column prop="caseIndex" align="center" label="案例名称" width="200">
              <template slot-scope="scope">
                {{ '案例' + scope.row.caseIndex }}
              </template>
            </el-table-column>
            <el-table-column prop="caseName" align="center" label="服务客户名称" width="200">
              <template slot-scope="scope">
                <el-input placeholder="输入服务客户名称" maxlength="50" v-model="scope.row.caseName">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="上传文件" align="center" label="案例编辑" width="200">
              <template slot-scope="scope">
                <!-- <ImageUpload @change="testChange()" v-model="scope.row.caseUrlList" /> -->
                <img :src="scope.row.img" alt="" style="width: 160px;height: 90px;">
                <el-button type="primary" size="mini" @click="EArrangeEdit(scope.row)">点击进入</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="caseExplain" align="center" label="案例说明">
              <template slot-scope="scope">
                <el-input placeholder="输入文字介绍" type="textarea" :rows="3" v-model="scope.row.caseExplain">
                </el-input>
              </template>
            </el-table-column>

            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,4)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(4)" :src="timeSelect" />
          </div>
        </div>
        <div style="height: 2.1vw;margin-top:4.4vw" class="table-title">
          <div class="table-label-text">友情链接</div>
        </div>

        <div class="table-div">
          <el-table :data="qxLinkList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column prop="linkName" align="center" label="链接名称" width="200">
              <template slot-scope="scope">
                <el-input placeholder="输入链接名称" maxlength="50" v-model="scope.row.linkName">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="linkUrl" align="center" label="链接地址">
              <template slot-scope="scope">
                <el-input placeholder="输入链接地址" maxlength="255" v-model="scope.row.linkUrl">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,5)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(5)" :src="timeSelect" />
          </div>
        </div>

        <div style="height: 2.1vw;margin-top:4.4vw" class="table-title">
          <div class="table-label-text">相关产品</div>
        </div>

        <div class="table-div">
          <el-table :data="qxProductList" border :header-cell-style="{background:'#F2F2F2',color:'#131415',borderColor:'#D6DDDF'}" style="width: 100%">
            <el-table-column prop="name" align="center" label="产品名称" width="200">
              <template slot-scope="scope">
                <el-input placeholder="输入产品名称" maxlength="50" v-model="scope.row.name">
                </el-input>
              </template>
            </el-table-column>
            <el-table-column prop="url" align="center" label="产品图片">
              <template slot-scope="scope">
                <el-upload class="avatar-uploader" :action="uploadImgUrl" :headers="uploadHeaders" :show-file-list="false" :on-success="(res) => handleUploadSuccess(res, scope.row)" :before-upload="beforeUpload">
                  <img v-if="scope.row.url" :src="scope.row.url" class="avatar">
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" style="color:#FF4343" icon="el-icon-delete" @click="confirmDelete(scope.row,6)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-row">
            <img class="add-row-img" @click="addServiceRow(6)" :src="timeSelect" />
          </div>
        </div>

      </div>
    </div>

    <div v-show="!previewPage" class="footer">
      <button v-show="qxdevelopmentData.state!=0&&qxdevelopmentData.state!=5" class="save" @click="saveDataExamine()">
        上传审核
      </button>
      <button v-show="qxdevelopmentData.state!=0&&qxdevelopmentData.state!=5" class="save" @click="saveData('数据保存中...')">保存数据
      </button>
      <button v-show="!previewPage&&showPreview" class="preview" @click="setPreview()">效果预览</button>
      <button v-show="previewPage" class="preview" @click="previewPage=false">返回编辑</button>
    </div>

    <div id="previewPageId" v-show="false" class="saveimg main">
      <div class="content">
        <div id="deviceImgMaxId" ref="deviceImgMax" class="save-back">
          <img class="save-back-img" :src="scene.sceneUrl" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageUpload from "../../../../components/MultipleFilesTo/MultipleFilesTo";
import ExcelImport from "../../../../components/ExcelImport";
import timeSelect from "../../../../assets/images/add.png";
import { getProvince } from "../../../../utils/province";

import colseImg from "../../../../assets/images/close.png";
import item1 from "../../../../assets/images/partners/item-1.png";
import item2 from "../../../../assets/images/partners/item-2.png";
import item3 from "../../../../assets/images/partners/item-3.png";
import ppt from "../../../../assets/images/partners/ppt.png";
import undow from "../../../../assets/images/partners/undow.png";
import map from "../../../../assets/images/partners/map.png";
import detail from "../../../../assets/images/partners/detail.png";
import yuan from "../../../../assets/images/partners/yuan.png";
import marshalling from "../../../../assets/images/partners/marshalling.png";
import comecart from "../../../../assets/images/partners/comecart.png";
import anliBgitem from "../../../../assets/images/partners/anli-bgitem.png";
import { getToken } from "@/utils/auth";
import store from "@/store";

import Preview from "./preview.vue";

import {
  getpanorama,
  devicelist,
  download,
  serviceexport,
  servicelist,
  update,
  examine,
  servicedefault,
} from "@/api/hzhb/api";

export default {
  components: {
    Preview,
    ImageUpload,
    ExcelImport,
  },

  data() {
    return {
      showPreview:false,
      iframeUrl: "",
      linkurl: "",
      item1,
      item2,
      item3,
      ppt,
      undow,
      map,
      yuan,
      marshalling,
      comecart,
      colseImg,
      anliBgitem,
      exportList: [],
      industryCode: null,
      previewPage: false,
      loading: false,
      loadingText: "加载中",
      timeSelect,
      id: null,
      props: { multiple: true },
      name: null,
      checked: false,
      options: [
        {
          value: "0",
          label: "冶金",
        },
        {
          value: "1",
          label: "电力",
          disabled: true,
        },
      ],
      value: "",
      scene: {},
      deviceList: [], //设备列表
      deviceImgList: [], //设备图片列表
      serviceList: [],
      selectDeviceList: [], //选中的设备
      selectDeviceTableList: [], //表格数据
      selectSoftwareTableList: [], //软件数据
      selectIntegrateTableList: [], //集成服务
      selectOtherTableList: [], //集成服务
      qxCaseList: [], //案例
      qxLinkList: [], //链接
      qxProductList: [],
      qxdevelopmentData: {},
      previewData: {},
      achieveNumber: 0,
      number: 0,
      dragDevice: null, //要拖拽的设备
      developmentCityList: [], //城市的列表
      optionsProvince: [], //城市options
      cityName: "",
      panorama: {},
      tempImgUrl: "",
      token: "",
      email: store.getters.email,
      deviceNumber: [],
      web_socket: null,
      divSFYX: null,
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/system/common/upload",
      uploadHeaders: {
        Authorization: "Bearer " + getToken(),
      },
    };
  },
  created() {
    this.showPreview=false;
    if (this.$route.query.id == null) this.$router.push({ name: "index" });
    else this.id = this.$route.query.id;
    this.industryCode = this.$route.query.industryCode;
    this.number = this.$route.query.number;
    this.achieveNumber = this.$route.query.achieveNumber;
    this.getList(this.id);
    this.getPanorama();
    this.Move();
    console.log(this.email);
    this.token = getToken();
    this.optionsProvince = getProvince();

    let that = this;
    window.addEventListener(
      "message",
      (e) => {
        if (e.data.data == "Close") {
          that.panoramaClose();
        } else if (e.data != null) {
          if (e.data.data == "closeArrange") {
            that.arrangeClose();
            let data = e.data;
            let isSave = false;
            for (let i = 0; i < that.qxCaseList.length; i++) {
              let element = that.qxCaseList[i];
              if (
                element.uuid != null &&
                element.uuid == data.uuid &&
                data.image != ""
              ) {
                isSave = true;
                element.img = data.image;
                element.arrangeId = data.id;
                element.caseUrlList = [{ name: "场景案例", url: data.image }];
              }
            }
            if (isSave) {
              that.qxCaseList = JSON.parse(JSON.stringify(that.qxCaseList));
              that.saveData("数据自动保存中...");
            }
          }
        }
        console.log(e.data, e.data.data);
      },
      false
    );
  },
  mounted() {},
  methods: {
    handlePreviewPageChange() {
      this.previewPage = false; // 改变 previewPage 为 false
    },
    //获取全景
    getPanorama() {
      getpanorama(this.id).then((res) => {
        if (res.code === 200) {
          this.panorama = res.QxScenePanorama;
          devicelist({ developmentId: this.id }).then((res) => {
            this.deviceImgList = res.data;
            console.log(this.deviceImgList);
            this.$nextTick(() => {
              if (
                this.panorama != null &&
                this.panorama.deviceNumber != null &&
                this.panorama.deviceNumber != ""
              ) {
                this.deviceNumber = JSON.parse(this.panorama.deviceNumber);
                this.deviceImgList.forEach((deviceimg) => {
                  deviceimg.deviceNumber = 0;
                  let num = this.deviceNumber[deviceimg.id];
                  if (num != null) {
                    deviceimg.deviceNumber = num;
                  }
                });
                console.log(this.deviceImgList);
                this.$forceUpdate();
              }
            });
          });
          console.log(this.panorama);
        }
      });
    },
    EArrangeEdit(row) {
      let that = this;
      if (that.qxdevelopmentData.state == 0) {
        that.$message.error("编辑失败，数据正在审核中！");
        return false;
      }
      let updateForm = this.editHandleData();
      if (updateForm === false) {
        return false;
      }

      if (row.arrangeId != null && row.arrangeId != undefined)
        row.uuid = row.arrangeId;
      else if (row.id == null)
        row.uuid =
          Math.floor(Math.random() * (99999999 - 10000000)) +
          10000000 +
          "" +
          (Math.floor(Math.random() * (99999999 - 10000000)) + 10000000);
      else row.uuid = row.id;

      if (row.arrangeId == null || row.arrangeId == undefined) {
        row.arrangeId = row.uuid;
      }
      let url =
        "http://36.134.94.250:18080/ishowa/ebp/#/project/items?Email=" +
        this.email +
        "&Authorization=" +
        this.token +
        "&ProjectId=" +
        row.arrangeId +
        "&Uuid=" +
        row.uuid;

      //let url = "http://36.139.142.86:8082/ebp/#/project/items?Email=" + this.email + "&Authorization=" + this.token + "&ProjectId=" + row.arrangeId + "&Uuid=" + row.uuid;
      //that.$socket.emit("message",row.uuid)
      //window.open(url, '_blank');
      this.iframeUrl = url;

      that.divSFYX = window.document.createElement("div");
      let html =
        " <div style='width: 100vw;overflow: hidden;height: 100vh;position: absolute;top: 0;left: 0;z-index: 1001;'>" +
        "<iframe style='width: 100%;padding: 0;border: 0;margin: 0;height: 100%;overflow: hidden;' scrolling='no' src='" +
        url +
        "' class='panorama-ifram-item'></iframe></div>";
      that.divSFYX.innerHTML = html;
      document.body.appendChild(that.divSFYX);
      this.stopMove();
    },
    //编辑全景
    panoramaEdit() {
      let that = this;
      if (this.qxdevelopmentData.state == 0) {
        this.$message.error("编辑失败，数据正在审核中！");
        return false;
      }

      window.panoramaClose = (data) => {
        // 使用_this可以继续调用vue中的属性和方法，也可以完成绑定事件
        that.Move();
        that.getPanorama();
        document.body.removeChild(this.divSFYX);
      };

      //this.iframeUrl = 'https://jiekou.meswl.com/profile/panorama/index.html?id=' + this.id + '&token=' + this.token;
      this.iframeUrl =
        "http://36.134.94.250:18080/ishowa/profile/panorama/index.html?id=" +
        this.id +
        "&token=" +
        this.token;
      that.divSFYX = window.document.createElement("div");
      let html =
        " <div style='width: 100vw;overflow: hidden;height: 100vh;position: absolute;top: 0;left: 0;z-index: 1001;'>" +
        "<img src='" +
        colseImg +
        "' onclick='panoramaClose()'  style='  right: 30px;top: 15px;z-index: 2002; position: absolute;width: 73px;height: 24px;cursor: pointer;' />" +
        "<iframe style='width: 100%;padding: 0;border: 0;margin: 0;height: 100%;overflow: hidden;' scrolling='no' src='" +
        this.iframeUrl +
        "' class='panorama-ifram-item'></iframe></div>";
      that.divSFYX.innerHTML = html;
      document.body.appendChild(that.divSFYX);
      this.stopMove();
    },
    //关闭e编排
    arrangeClose() {
      this.iframeUr = "";
      document.body.removeChild(this.divSFYX);
      this.Move();
    },
    //关闭全景
    panoramaClose() {
      this.iframeUr = "";
      document.body.removeChild(this.divSFYX);
      this.Move();
      this.getPanorama();
    },
    //停止页面滚动
    stopMove() {
      let m = function (e) {
        e.preventDefault();
      };
      document.documentElement.style.overflow = "hidden";
      document.documentElement.scrollTop = 0;
      document.addEventListener("touchmove", m, { passive: false }); //禁止页面滑动
    },
    //开启页面滚动
    Move() {
      let m = function (e) {
        e.preventDefault();
      };
      document.documentElement.style.overflow = ""; //出现滚动条
      document.removeEventListener("touchmove", m, { passive: true });
    },
    downloadCase(item) {
      item.pdtArr.forEach((i) => {
        window.open(i, "_blank");
      });
    },
    linkUrl(url) {
      window.open(url);
    },
    routeIndex() {
      this.$router.push({ name: "index" });
    },
    ExportChange() {
      let data = this.exportList;
      for (let i = 0; i < this.selectDeviceList.length; i++) {
        for (let y = 0; y < data.length; y++) {
          if (this.selectDeviceList[i].deviceId == data[y].deviceId) {
            this.$message.error(data[y].deviceName + "设备已添加！");
            return false;
          }
        }
      }
      data.forEach((row) => {
        if (row.serviceType === 0) {
          this.selectDeviceTableList.push(row);
          //匹配对应设备
          this.deviceList.forEach((device) => {
            if (device.id === row.deviceId) {
              device.serviceNumber = row.serviceNumber;
              this.$set(device, "serviceNumber", row.serviceNumber);
            }
          });
        } else if (row.serviceType === 1) {
          this.selectSoftwareTableList.push(row);
        } else if (row.serviceType === 2) {
          this.selectIntegrateTableList.push(row);
        } else if (row.serviceType === 3) {
          this.selectOtherTableList.push(row);
        } else if (row.serviceType === 4) {
          this.qxCaseList.push(row);
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$confirm("是否确认下载报价模板?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(function () {
        // 创建一个链接元素
        const link = document.createElement("a");
        link.href =
          process.env.VUE_APP_BASE_API + "/system/hzhb/service/export";
        link.setAttribute("download", "模板.xlsx"); // 可选：指定下载文件的名称

        // 触发点击事件下载文件
        document.body.appendChild(link);
        link.click();
        // 清理
        document.body.removeChild(link);
      });
    },
    setPreview() {
      if (
        this.developmentCityList != null &&
        this.developmentCityList.length === 340
      ) {
        this.developmentCityList = [{ label: "全国" }];
      } else {
        this.developmentCityList.forEach((row) => {
          this.optionsProvince.forEach((item) => {
            if (row.provinceId == item.value) {
              item.children.forEach((child) => {
                if (row.cityId == child.value) {
                  row.label = child.label;
                }
              });
            }
          });
        });
      }

      this.qxCaseList.forEach((item) => {
        let pdtArr = [];
        if (item.caseUrl != null && item.caseUrl != "") {
          console.log("sdfsdf" + item.caseUrl);
          if (typeof item.caseUrl === "string") {
            try {
              item.caseUrl = JSON.parse(item.caseUrl); // 解析为数组
            } catch (e) {
              console.error("Failed to parse item.caseUrl:", e);
              return;
            }
          }
          if (Array.isArray(item.caseUrl)) {
            item.caseUrl.forEach((i) => {
              i.type = i.url.substring(i.url.length - 3);
              if (i.type == "png" || i.type == "jpg" || i.type == "jpeg")
                item.img = i.url;
              if (i.type == "mp4") item.mp4 = i.url;
              if (
                i.type == "pdf" ||
                i.type == "txt" ||
                i.type == "ppt" ||
                i.type == "docx" ||
                i.type == "doc" ||
                i.type == "pptx"
              ) {
                item.pdtname = i.name;
                pdtArr.push(i.url);
              }

              item.pdtArr = pdtArr;
            });
          }
        }
      });

      this.$refs.previewRef.imgUrl =
        this.panorama == null || this.panorama.imgUrl==null
          ? null
          : this.panorama.imgUrl;
      this.$refs.previewRef.sendName = this.scene.sceneName;
      console.log('asdfasdfa'+ this.$refs.previewRef.imgUrl)
      this.$refs.previewRef.solutions = {
        phone: "137xxxxxxxx",
        number: parseInt(this.number), //全国案例数
        achieveNumber: parseInt(this.achieveNumber), //可支撑人数,
        qxDevelopmentCityList: this.developmentCityList,
        qxLinkList: this.qxLinkList,
        demoVos: this.qxProductList,
        qxCaseVoList: this.qxCaseList,
        qxServiceList: [
          ...this.selectDeviceTableList,
          ...this.selectSoftwareTableList,
          ...this.selectIntegrateTableList,
          ...this.selectOtherTableList,
        ],
      };
      this.previewPage = true;
    },
    setPreview2() {
      this.$set(
        this,
        "tempImgUrl",
        this.panorama == null ? null : this.panorama.imgUrl
      );

      this.qxCaseList.forEach((item) => {
        let pdtArr = [];
        if (item.caseUrl != null && item.caseUrl != "") {
          item.caseUrl.forEach((i) => {
            i.type = i.url.substring(i.url.length - 3);
            if (i.type == "png" || i.type == "jpg" || i.type == "jpeg")
              item.img = i.url;
            if (i.type == "mp4") item.mp4 = i.url;
            if (
              i.type == "pdf" ||
              i.type == "txt" ||
              i.type == "ppt" ||
              i.type == "docx" ||
              i.type == "doc" ||
              i.type == "pptx"
            ) {
              item.pdtname = i.name;
              pdtArr.push(i.url);
            }

            item.pdtArr = pdtArr;
          });
        }
      });

      this.previewPage = true;
      this.$forceUpdate();
    },
    //重复校验
    dataNoReplace(list) {
      let boo = true;
      list.forEach((row) => {
        list.forEach((item) => {
          if (row != item && row.serviceName == item.serviceName && boo) {
            this.$message.error(
              (row.serviceType == 0
                ? "硬件"
                : row.serviceType == 1
                ? "软件"
                : row.serviceType == 2
                ? "集成服务"
                : "其它服务") + "名称不能重复"
            );
            boo = false;
          }
        });
      });
      return boo;
    },
    getLogout() {
      let that = this;
      that
        .$confirm("是否确认退出登录?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(function () {
          localStorage.removeItem("token");
          that.$router.push({ name: "login" });
        })
        .catch(() => {});
    },
    confirmDelete(row, type) {
      this.$confirm("确定删除当前行？", "提示", {
        confirmButtonText: "是",
        cancelButtonText: "否",
        type: "warning",
      })
        .then(() => {
          this.handleDelete(row, type);
        })
        .catch(() => {
          // 用户点击取消，不做任何处理
        });
    },
    handleDelete(row, type) {
      let list = [];
      if (type === 0) {
        list = this.selectDeviceTableList;
        for (let i = 0; i < this.selectDeviceList.length; i++) {
          if (row.deviceId == this.selectDeviceList[i].deviceId) {
            this.selectDeviceList.splice(i, 1);
            i--;
          }
        }
        this.deviceList.forEach((element) => {
          if (element.id == row.deviceId) {
            this.$set(element, "serviceNumber", 0);
            element.serviceNumber = 0;
          }
        });
      } else if (type === 1) {
        list = this.selectSoftwareTableList;
      } else if (type === 2) {
        list = this.selectIntegrateTableList;
      } else if (type === 3) {
        list = this.selectOtherTableList;
      } else if (type === 4) {
        list = this.qxCaseList;
      } else if (type === 5) {
        list = this.qxLinkList;
      } else if (type === 6) {
        list = this.qxProductList;
      }
      for (let i = 0; i < list.length; i++) {
        if (row === list[i]) {
          list.splice(i, 1);
          return;
        }
      }
    },
    testChange(value) {
      console.log(value);
    },
    addServiceRow(type) {
      let id = new Date().getTime();
      if (type === 0) {
        this.selectDeviceTableList.push({
          id: id,
          serviceNumber: undefined,
          serviceType: type,
        });
      } else if (type === 1) {
        this.selectSoftwareTableList.push({
          id: id,
          serviceNumber: undefined,
          serviceType: type,
        });
      } else if (type === 2) {
        this.selectIntegrateTableList.push({
          id: id,
          serviceNumber: undefined,
          serviceType: type,
        });
      } else if (type === 3) {
        this.selectOtherTableList.push({
          id: id,
          serviceNumber: undefined,
          serviceType: type,
        });
      } else if (type === 4) {
        this.qxCaseList.push({
          id: id,
          caseUrlList: [],
          caseIndex: this.qxCaseList.length + 1,
        });
      } else if (type === 5) {
        this.qxLinkList.push({ id: id, linkIndex: this.qxLinkList.length + 1 });
      } else if (type === 6) {
        this.qxProductList.push({
          id: id,
          linkIndex: this.qxProductList.length + 1,
        });
      }
    },
    // 将base64文件转换为三进制
    dataURLtoBlob(dataUrl) {
      let arr = dataUrl.split(",");
      let mime = arr[0].match(/:(.*?);/)[1];
      let bstr = atob(arr[1]);
      let n = bstr.length;
      let u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new Blob([u8arr], { type: mime });
    },
    editHandleData() {
      let that = this;
      for (let i = 0; i < that.selectDeviceTableList.length; i++) {
        let row = that.selectDeviceTableList[i];
        if (
          (row.serviceName === "" || row.serviceName == null) &&
          this.industryCode !== "HY002"
        ) {
          that.$message.error("硬件名称不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceUnit == null && this.industryCode !== "HY002") {
          that.$message.error("硬件单位不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("硬件介绍不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("硬件价格不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("硬件数量不能为空,编辑前请补充完基础数据");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectDeviceTableList)) return false;

      for (let i = 0; i < that.selectSoftwareTableList.length; i++) {
        let row = that.selectSoftwareTableList[i];
        if (row.serviceName === "" || row.serviceName == null) {
          that.$message.error("软件名称不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("软件介绍不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("软件价格不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceUnit == null) {
          that.$message.error("软件单位不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("软件数量不能为空,编辑前请补充完基础数据");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectSoftwareTableList)) return false;

      for (let i = 0; i < that.selectIntegrateTableList.length; i++) {
        let row = that.selectIntegrateTableList[i];
        if (row.serviceName === "" || row.serviceName == null) {
          that.$message.error("集成服务名称不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("集成服务介绍不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("集成服务价格不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceUnit == null) {
          that.$message.error("集成服务单位不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("集成服务数量不能为空,编辑前请补充完基础数据");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectIntegrateTableList)) return false;

      for (let i = 0; i < that.selectOtherTableList.length; i++) {
        let row = that.selectOtherTableList[i];
        if (row.serviceName === "" || row.serviceName == null) {
          that.$message.error("其它服务名称不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("其它服务介绍不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("其它服务价格不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceUnit == null) {
          that.$message.error("其它服务单位不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("其它服务数量不能为空,编辑前请补充完基础数据");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectOtherTableList)) return false;

      for (let i = 0; i < that.qxCaseList.length; i++) {
        let row = that.qxCaseList[i];
        if (row.caseName === "" || row.caseName == null) {
          that.$message.error("服务客户名称不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.caseExplain === "" || row.caseExplain == null) {
          that.$message.error("介绍不能为空,编辑前请补充完基础数据");
          return false;
        }
        let caseUrlListVo = [];
        row.caseUrlList.forEach((item) => {
          caseUrlListVo.push({ name: item.name, url: item.url });
        });
        that.qxCaseList[i].caseUrl = JSON.stringify(caseUrlListVo);
      }

      for (let i = 0; i < that.qxLinkList.length; i++) {
        let row = that.qxLinkList[i];
        if (row.linkName === "" || row.linkName == null) {
          that.$message.error("链接名称不能为空,编辑前请补充完基础数据");
          return false;
        }
        if (row.linkUrl === "" || row.linkUrl == null) {
          that.$message.error("链接地址不能为空,编辑前请补充完基础数据");
          return false;
        }
      }
    },

    handleData() {
      let that = this;
      for (let i = 0; i < that.selectDeviceTableList.length; i++) {
        let row = that.selectDeviceTableList[i];
        if (
          (row.serviceName === "" || row.serviceName == null) &&
          this.industryCode !== "HY002"
        ) {
          that.$message.error("硬件名称不能为空");
          return false;
        } else {
          if (row.serviceName.length > 100) {
            that.$message.error("硬件名称长度为100个字符");
            return false;
          }
        }
        if (row.serviceUnit == null && this.industryCode !== "HY002") {
          that.$message.error("硬件单位不能为空");
          return false;
        } else {
          if (row.serviceUnit.length > 20) {
            that.$message.error("硬件单位长度为20个字符");
            return false;
          }
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("硬件介绍不能为空");
          return false;
        } else {
          if (row.serviceExplain.length > 500) {
            that.$message.error("硬件介绍长度为500个字符");
            return false;
          }
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("硬件价格不能为空");
          return false;
        } else if (row.servicePrice < 1 || row.servicePrice > 9999999999999) {
          this.$message.error("硬件价格范围为1到9999999999999");
          return false;
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("硬件数量不能为空");
          return false;
        } else if (row.serviceNumber < 1 || row.serviceNumber > 99999999) {
          this.$message.error("硬件价格范围为1到99999999");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectDeviceTableList)) return false;

      for (let i = 0; i < that.selectSoftwareTableList.length; i++) {
        let row = that.selectSoftwareTableList[i];
        if (row.serviceName === "" || row.serviceName == null) {
          that.$message.error("软件名称不能为空");
          return false;
        } else {
          if (row.serviceName.length > 100) {
            that.$message.error("软件名称长度为100个字符");
            return false;
          }
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("软件介绍不能为空");
          return false;
        } else {
          if (row.serviceExplain.length > 500) {
            that.$message.error("软件介绍长度为500个字符");
            return false;
          }
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("软件价格不能为空");
          return false;
        } else if (row.servicePrice < 1 || row.servicePrice > 9999999999999) {
          this.$message.error("软件价格范围为1到9999999999999");
          return false;
        }
        if (row.serviceUnit == null) {
          that.$message.error("软件单位不能为空");
          return false;
        } else {
          if (row.serviceUnit.length > 20) {
            that.$message.error("软件单位长度为20个字符");
            return false;
          }
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("软件数量不能为空");
          return false;
        } else if (row.serviceNumber < 1 || row.serviceNumber > 99999999) {
          this.$message.error("软件数量范围为1到99999999");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectSoftwareTableList)) return false;

      for (let i = 0; i < that.selectIntegrateTableList.length; i++) {
        let row = that.selectIntegrateTableList[i];
        if (row.serviceName === "" || row.serviceName == null) {
          that.$message.error("集成服务名称不能为空");
          return false;
        } else {
          if (row.serviceName.length > 100) {
            that.$message.error("集成服务名称长度为100个字符");
            return false;
          }
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("集成服务介绍不能为空");
          return false;
        } else {
          if (row.serviceExplain.length > 500) {
            that.$message.error("集成服务介绍长度为500个字符");
            return false;
          }
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("集成服务价格不能为空");
          return false;
        } else if (row.servicePrice < 1 || row.servicePrice > 9999999999999) {
          this.$message.error("集成服务价格范围为1到9999999999999");
          return false;
        }
        if (row.serviceUnit == null) {
          that.$message.error("集成服务单位不能为空");
          return false;
        } else {
          if (row.serviceUnit.length > 20) {
            that.$message.error("集成服务单位长度为20个字符");
            return false;
          }
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("集成服务数量不能为空");
          return false;
        } else if (row.serviceNumber < 1 || row.serviceNumber > 99999999) {
          this.$message.error("集成服务数量范围为1到99999999");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectIntegrateTableList)) return false;

      for (let i = 0; i < that.selectOtherTableList.length; i++) {
        let row = that.selectOtherTableList[i];
        if (row.serviceName === "" || row.serviceName == null) {
          that.$message.error("其它服务名称不能为空");
          return false;
        } else {
          if (row.serviceName.length > 100) {
            that.$message.error("其他服务名称长度为100个字符");
            return false;
          }
        }
        if (row.serviceExplain === "" || row.serviceExplain == null) {
          that.$message.error("其它服务介绍不能为空");
          return false;
        } else {
          if (row.serviceExplain.length > 500) {
            that.$message.error("其他服务介绍长度为500个字符");
            return false;
          }
        }
        if (row.servicePrice === "" || row.servicePrice == null) {
          that.$message.error("其它服务价格不能为空");
          return false;
        } else if (row.servicePrice < 1 || row.servicePrice > 9999999999999) {
          this.$message.error("其它服务价格范围为1到9999999999999");
          return false;
        }
        if (row.serviceUnit == null) {
          that.$message.error("其它服务单位不能为空");
          return false;
        } else {
          if (row.serviceUnit.length > 20) {
            that.$message.error("其他服务单位长度为20个字符");
            return false;
          }
        }
        if (row.serviceNumber === "" || row.serviceNumber == null) {
          that.$message.error("其他服务数量不能为空");
          return false;
        } else if (row.serviceNumber < 1 || row.serviceNumber > 99999999) {
          this.$message.error("其他服务数量范围为1到99999999");
          return false;
        }
      }
      if (!this.dataNoReplace(that.selectOtherTableList)) return false;

      for (let i = 0; i < that.qxCaseList.length; i++) {
        let row = that.qxCaseList[i];
        if (row.caseName === "" || row.caseName == null) {
          that.$message.error("服务客户名称不能为空");
          return false;
        }
        if (row.caseUrlList == null || row.caseUrlList.length === 0) {
          that.$message.error("案例编辑不能为空");
          return false;
        }
        if (row.caseExplain === "" || row.caseExplain == null) {
          that.$message.error("介绍不能为空");
          return false;
        }
        let caseUrlListVo = [];
        row.caseUrlList.forEach((item) => {
          caseUrlListVo.push({ name: item.name, url: item.url });
        });
        that.qxCaseList[i].caseUrl = JSON.stringify(caseUrlListVo);
      }

      for (let i = 0; i < that.qxLinkList.length; i++) {
        let row = that.qxLinkList[i];
        if (row.linkName === "" || row.linkName == null) {
          that.$message.error("链接名称不能为空");
          return false;
        }
        if (row.linkUrl === "" || row.linkUrl == null) {
          that.$message.error("链接地址不能为空");
          return false;
        } else if (!/^https?:\/\/.+/.test(row.linkUrl)) {
          that.$message.error("链接格式错误，请输入有效的http或https链接");
          return false;
        }
      }

      // 在验证完成后，创建并返回updateForm对象
      let updateForm = {
        id: this.id,
        imgUrl: this.panorama == null ? null : this.panorama.imgUrl,
        qxServiceList: [],
        qxCaseList: this.qxCaseList,
        qxLinkList: this.qxLinkList,
        qxProductList: this.qxProductList,
      };

      // 合并服务列表
      updateForm.qxServiceList = updateForm.qxServiceList
        .concat(that.selectDeviceTableList)
        .concat(that.selectSoftwareTableList)
        .concat(that.selectIntegrateTableList)
        .concat(that.selectOtherTableList);

      return updateForm;
    },

    saveData(text) {
      let that = this;
      let updateForm = this.handleData();
      if (updateForm === false) {
        return false;
      }
      this.loading = true;
      this.loadingText = text;
      updateForm.imgUrl = this.panorama == null ? null : this.panorama.imgUrl;
      update(updateForm)
        .then((res) => {
          if (res.code === 200) {
            that.$message.success("保存成功");
            that.getList();
            this.loading = false;
          }
        })
        .catch((error) => {
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //保存并审核数据
    saveDataExamine() {
      let that = this;
      let updateForm = this.handleData();
      if (updateForm === false) {
        return false;
      }
      this.loading = true;
      this.loadingText = "数据保存上传中...";
      updateForm.imgUrl = this.panorama == null ? null : this.panorama.imgUrl;
      examine(updateForm)
        .then((res) => {
          if (res.code === 200) {
            that.$message.success("上传成功");
            that.getList();
          }
          this.loading = false;
        })
        .catch((error) => {
          this.loading = false;
        });
    },
    itemClick(row) {
      console.log(row.serviceNumber);
      this.$set(row, "serviceNumber", row.serviceNumber);
      let isAdd = true;
      this.selectDeviceTableList.forEach((item) => {
        if (row.id == item.deviceId) {
          isAdd = false;
          this.$set(item, "serviceNumber", row.serviceNumber);
        }
      });
      if (row.serviceNumber > 1) {
        console.log("row");
        this.selectDeviceList.forEach((item) => {
          if (item.deviceId == row.id) {
            item.serviceNumber = row.serviceNumber;
          }
        });
      } else if (row.serviceNumber === 1 && isAdd) {
        this.selectDeviceTableList.splice(0, 0, {
          serviceName: row.deviceName,
          deviceId: row.id,
          serviceUnit: row.deviceUnit,
          serviceType: 0,
          serviceNumber: 1,
        });
      } else {
        for (let i = 0; i < this.selectDeviceList.length; i++) {
          if (row.id == this.selectDeviceList[i].deviceId) {
            this.selectDeviceList.splice(i, 1);
            i--;
          }
        }
        for (let i = 0; i < this.selectDeviceTableList.length; i++) {
          if (row.id == this.selectDeviceTableList[i].deviceId) {
            this.selectDeviceTableList.splice(i, 1);
            i--;
          }
        }
      }
    },
    //匹配对应设备并在图片上显示出来
    addDeviceListItem(row) {
      this.deviceList.forEach((device) => {
        if (device.id === row.deviceId) {
          device.serviceNumber = row.serviceNumber;
        }
      });
    },
    getList() {
      let that = this;
      let params = {
        developmentId: this.id,
      };
      this.clearList();
      servicelist(params).then((res) => {
        if (res.code === 200) {
          this.scene = res.data.scene;
          this.deviceList = res.data.deviceList;
          this.deviceList.forEach((device) => {
            this.$set(device, "serviceNumber", 0);
          });

          this.serviceList = res.data.serviceList;
          console.log("返回的结果集：", res.data);

          this.serviceList.forEach((row) => {
            if (row.serviceType === 0) {
              this.selectDeviceTableList.push(row);
              //匹配对应设备并在图片上显示出来
              this.addDeviceListItem(row);
            } else if (row.serviceType === 1) {
              this.selectSoftwareTableList.push(row);
            } else if (row.serviceType === 2) {
              this.selectIntegrateTableList.push(row);
            } else if (row.serviceType === 3) {
              this.selectOtherTableList.push(row);
            } else if (row.serviceType === 4) {
              this.qxCaseList.push(row);
            }
          });
          this.qxdevelopmentData = res.data.development;
          this.qxLinkList = res.data.linkList;
          this.qxCaseList = res.data.caseList;
          console.log('aaaaa',res.data.developmentCityList)
          this.developmentCityList = res.data.developmentCityList;
          this.qxCaseList.forEach((element) => {
            this.$set(element, "caseUrlList", JSON.parse(element.caseUrl));
            this.$set(element, "caseUrl", JSON.parse(element.caseUrl));
            element.caseUrl.forEach((i) => {
              i.type = i.url.substring(i.url.length - 3);
              if (i.type == "png" || i.type == "jpg" || i.type == "jpeg")
                element.img = i.url;
            });
          });

          if (this.qxdevelopmentData.qxProductList != null) {
            this.qxProductList = this.qxdevelopmentData.qxProductList;
          }
          if (
            (this.serviceList == null || this.serviceList.length == 0) &&
            this.industryCode !== "HY002"
          ) {
            let paramsTo = {
              sceneId: this.scene.id,
            };
            servicedefault(paramsTo).then((res) => {
              if (res.code === 200) {
                if (res.data != null && res.data.length > 0) {
                  res.data.forEach((row) => {
                    let item = {
                      serviceType: row.serviceType,
                      serviceName: row.serviceName,
                      smallType: row.smallType,
                      serviceExplain: row.serviceExplain,
                      servicePrice:
                        row.servicePrice == null ? undefined : row.servicePrice,
                      serviceUnit: row.serviceUnit,
                      serviceNumber:
                        row.serviceNumber == null
                          ? undefined
                          : row.serviceNumber,
                    };
                    if (row.serviceType === 0) {
                      this.selectDeviceTableList.push(item);
                    } else if (row.serviceType === 1) {
                      this.selectSoftwareTableList.push(item);
                    } else if (row.serviceType === 2) {
                      this.selectIntegrateTableList.push(item);
                    } else if (row.serviceType === 3) {
                      this.selectOtherTableList.push(item);
                    }
                  });
                }
                
              }
            });
          }

          this.showPreview=true;
        }
      });
    },
    clearList() {
      this.deviceList = [];
      this.serviceList = [];
      this.selectDeviceList = []; //选中的设备
      this.selectDeviceTableList = []; //表格数据
      this.selectSoftwareTableList = []; //软件数据
      this.selectIntegrateTableList = []; //集成服务
      this.selectOtherTableList = []; //集成服务
      this.qxCaseList = [];
      this.qxLinkList = [];
    },
    handleSelectionChange() {},
    handleUploadSuccess(res, row) {
      if (res.code === 200) {
        this.$set(row, "url", res.url);
        this.$message.success("上传成功");
      } else {
        this.$message.error("上传失败");
      }
    },
    beforeUpload(file) {
      const isImage = file.type.startsWith("image/");
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error("只能上传图片文件!");
        return false;
      }
      if (!isLt2M) {
        this.$message.error("图片大小不能超过 2MB!");
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.panorama-close {
  color: red;
  position: absolute;
  right: 1vw;
  top: 0.5vw;
  z-index: 1002;
  cursor: pointer;
}

.panorama-ifram {
  width: 100vw;
  overflow: hidden;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1001;
}

.panorama-ifram-item {
  width: 100%;
  padding: 0;
  border: 0;
  margin: 0;
  height: 100%;
  overflow: hidden;
}

::v-deep .el-dialog {
  height: 90vh;
  margin-top: 5vh;
  width: 100vw;
}

::v-deep .el-dialog__wrapper {
  overflow: hidden;
}

.panorama-footer {
  text-align: center;
  width: 100%;
  float: left;
  margin-top: 1vw;

  .panorama-btn {
    background: #0052d9;
    color: #fff;
  }
}

.saveimg {
  position: absolute;
  background-color: #fff;
  height: 55.1vw;
  width: 96.6vw;
  z-index: 1000;
}

.scene {
  width: 100%;
  display: inline-block;
  font-family: FZLTXHK--GBK1-0, FZLTXHK--GBK1;

  .head {
    float: left;
    width: 100%;
    background: #f0f6f9;
    text-align: center;
    height: 3.3vw;
    font-size: 1.3vw;
    font-family: FZLTZHK--GBK1-0, FZLTZHK--GBK1;
    font-weight: normal;
    color: #131415;
    line-height: 3.3vw;
  }

  .right {
    position: absolute;
    right: 0;
    top: 0;
    height: 3.3vw;
    width: 35vw;
    padding-right: 1.7vw;

    .myinfo {
      float: right;
      width: 15.3vw;
      font-size: 1vw;
      color: #131415;
      cursor: pointer;
      text-align: right;
      margin-right: 1.5vw;
    }

    .login-out {
      color: #ff5656;
      padding-left: 1.3vw;
      border-left: 1px solid #d8d8d8;
      float: right;
      width: 4.3vw;
      font-size: 1vw;
      height: 1vw;
      line-height: 1vw;
      margin-top: 1.15vw;
      cursor: pointer;
    }

    .update-pwd {
      color: #000;
      margin-right: 1.3vw;
      padding-left: 1.3vw;
      border-left: 1px solid #d8d8d8;
      float: right;
      width: 4.3vw;
      font-size: 1vw;
      height: 1vw;
      line-height: 1vw;
      margin-top: 1.15vw;
      cursor: pointer;
    }
  }

  .title {
    float: left;
    height: 5.8vw;
    width: 96.6vw;
    margin: 0 1.7vw;
    box-sizing: border-box;
    border-bottom: 2px solid #d1d1d1;

    .label {
      font-size: 1.7vw;
      font-weight: normal;
      color: #131415;
      line-height: 1.7vw;
      width: 100%;
      float: left;
      height: 5.8vw;
      line-height: 5.8vw;
      text-align: center;
    }

    .name {
      position: absolute;
      right: 1.7vw;
      padding-left: 0.5vw;
      padding-right: 0.5vw;
      height: 2.9vw;
      background-color: #cfdcf8;
      font-size: 0.8vw;
      text-align: center;
      line-height: 2.8vw;
      color: #0052d9;
      border: none;
      cursor: pointer;
      margin-top: 1.5vw;
    }
  }

  .content {
    float: left;
    margin: 0.3% 1.7% 0;
    width: 96.6%;
    padding-bottom: 9.4vw;

    .device-select {
      width: 96.6%;
      float: left;
      margin-bottom: 1.4vw;

      .device-left {
        width: 44.6vw;
        float: left;
        margin: 0;
        padding: 0;
        min-height: 29.4vw;

        .device-item {
          margin-top: 2.1vw;
          margin-right: 1.3vw;
          height: 14.7vw;
          width: 12.2vw;
          float: left;

          .device-number {
            height: 1.81vw;
            line-height: 1.81vw;
            width: 1.81vw;
            position: absolute;
            background-color: #0052d9;
            margin-left: 10vw;
            border-top-right-radius: 0.3vw;
            border-bottom-left-radius: 0.3vw;
            color: #fff;
            text-align: center;
          }

          .d-item {
            cursor: pointer;
            float: left;
            height: 11.5vw;
            width: 11.5vw;
            border-radius: 0.3vw;
            margin-left: 0.4vw;
            box-sizing: border-box;
            border: 1px solid #979797;

            .d-item-img {
              height: 6vw;
              width: 6vw;
              float: left;
              margin-top: 1.8vw;
              margin-left: 2.7vw;
              border-radius: 0.3vw;
              cursor: pointer;
            }

            .d-item-div {
              cursor: pointer;
              float: left;
              width: 100%;
              font-weight: normal;
              color: #131415;
              line-height: 1vw;
              font-size: 1vw;
              text-align: center;
              margin-top: 0.8vw;
            }
          }

          ::v-deep .el-checkbox__inner {
            width: 1.5vw;
            height: 1.5vw;
            margin-top: -0.5vw;
          }

          ::v-deep .el-checkbox__label {
            font-size: 1.3vw;
            font-family: FZLTXHK--GBK1-0, FZLTXHK--GBK1;
            margin-top: 0.3vw;
          }

          ::v-deep .el-checkbox__inner::after {
            height: 1vw;
            left: 0.45vw;
            width: 0.4vw;
            top: 0;
          }
        }
      }

      .right-back {
        height: 29.4vw;
        margin-top: 2.1vw;
        width: 60vw;
        margin-left: calc(50% - 30vw);
        float: left;
        text-align: left;

        .right-back-img {
          height: 29.4vw;
          width: 60vw;
          float: left;
        }

        .right-back-panorama {
          height: 29.4vw;
          width: 60vw;
          float: left;
          border: 2px dashed #0052d9;
          box-sizing: border-box;

          .right-back-content {
            height: 27.4vw;
            width: 58vw;
            background-color: #cdd6ec;
            line-height: 27.4vw;
            color: #6d7276;
            font-size: 30px;
            cursor: pointer;
            float: left;
            margin-left: 0.95vw;
            margin-top: 0.95vw;
            text-align: center;
          }
        }

        .right-back-div {
          height: 29.4vw;
          width: 52vw;
          position: absolute;
          z-index: 998;
          background-color: rgba(0, 0, 0, 0.5);
        }
      }
    }

    .table-title {
      margin-top: 3.5vw;
      width: 100%;
      height: 1.7vw;
      float: left;

      .table-label-text {
        text-align: left;
        width: 40vw;
        height: 1.7vw;
        font-size: 1.7vw;
        font-family: FZLTZHK--GBK1-0, FZLTZHK--GBK1;
        font-weight: normal;
        color: #131415;
        line-height: 1.7vw;
      }

      .table-btn {
        box-sizing: border-box;
        width: 8.3vw;
        height: 2.1vw;
        border-radius: 0.2vw;
        text-align: center;
        border: 1px solid #0052d9;
        font-size: 0.8vw;
        color: #0052d9;
        line-height: 2.1vw;
        float: right;
        margin-left: 1.3vw;
        cursor: pointer;
      }

      .name {
        position: absolute;
        right: 1.7vw;
        width: 8.3vw;
        height: 2.9vw;
        background-color: #cfdcf8;
        font-size: 0.8vw;
        text-align: center;
        line-height: 2.8vw;
        color: #0052d9;
        border: none;
        cursor: pointer;
        margin-top: -1vw;
      }
    }

    .table-div {
      float: left;
      width: 100%;
      margin-top: 2.1vw;

      .add-row {
        border: 1px solid #c2ccd0;
        height: 3.8vw;
        width: 100%;
        box-sizing: border-box;
        border-top: none;
        text-align: center;

        .add-row-img {
          height: 1.3vw;
          width: 6.3vw;
          margin-top: 1.3vw;
          cursor: pointer;
        }
      }

      ::v-deep .el-table--border:after,
      .el-table--group:after,
      .el-table:before {
        background-color: #c2ccd0;
      }

      ::v-deep .el-button--text {
        color: #ff4343;
      }

      ::v-deep .el-table--border,
      .el-table--group {
        border-color: #c2ccd0;
      }

      ::v-deep .el-table td,
      .el-table th.is-leaf {
        border-bottom: 1px solid #c2ccd0;
      }

      ::v-deep .el-table--border th,
      .el-table--border th.gutter:last-of-type {
        border-bottom: 1px solid #c2ccd0;
      }

      ::v-deep .el-table--border td,
      .el-table--border th {
        border-right: 1px solid #c2ccd0;
      }

      ::v-deep .el-input-number.is-without-controls .el-input__inner {
        padding-left: 0;
        padding-right: 0;
      }

      ::v-deep .el-input-number {
        width: 80%;
      }
    }

    .save-back {
      height: 55.1vw;
      width: 96.6vw;
      margin-top: 2.1vw;
      float: left;
      text-align: left;

      .save-back-img {
        height: 55.1vw;
        width: 96.6vw;
        float: left;
      }

      .save-back-div {
        height: 55.1vw;
        width: 96.6vw;
        position: absolute;
        z-index: 998;
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }
}

.preview-back {
  width: 515px;
  height: 290px;
  margin: auto;
  margin-top: 20px;

  // height: 35.625vw;
  // width: 63.333vw;
  // margin-top: 2.1vw;
  // float: left;
  // text-align: left;
  .preview-back-img {
    width: 515px;
    height: 290px;
    float: left;
  }

  .preview-back-div {
    height: 35.625vw;
    width: 63.333vw;
    position: absolute;
    z-index: 998;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.footer {
  z-index: 1000;
  background-color: #fff;
  opacity: 1;
  border-top: 1px solid #999;
  position: fixed;
  width: 100%;
  float: left;
  bottom: 0;
  left: 0;
  height: 7.2vw;

  .delete:disabled {
    background-color: #999;
    cursor: auto;
  }

  .save {
    float: right;
    margin-right: 1.6vw;
    width: 16vw;
    height: 5vw;
    background: #0052d9;
    border-radius: 0.3vw;
    font-size: 1.2vw;
    line-height: 5vw;
    text-align: center;
    border: none;
    margin-top: 1.2vw;
    color: #fff;
    cursor: pointer;
    border-color: #0052d9;
  }

  .preview {
    float: right;
    margin-right: 1.6vw;
    width: 16vw;
    height: 5vw;
    background: #fff;
    border-radius: 0.3vw;
    font-size: 1.2vw;
    line-height: 5vw;
    text-align: center;
    border: none;
    margin-top: 1.2vw;
    color: #0052d9;
    cursor: pointer;
    border: 1px solid #0052d9;
  }
}

[v-cloak] {
  display: none !important;
}

::v-deep .serviceUnit input {
  text-align: center;
}

.preview-div {
  width: 100%;
  height: calc(100vh - 3.4vw);
  overflow: hidden;
}

.preview-img {
  right: 30px;
  top: 15px;
  z-index: 2002;
  position: absolute;
  width: 73px;
  height: 24px;
  cursor: pointer;
}

.content-box {
  background-color: rgba(0, 0, 0);
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2001;

  /* 定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 5px !important;
    height: 5px;
    background-color: rgba(0, 0, 0, 0);
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
    border-radius: 5px;
    background-color: rgba(240, 240, 240, 0.5);
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
    width: 5px !important;
    height: 5px !important;
    background-color: #fff;
  }
}

.content-box > div {
  cursor: pointer;
}

.cart-modoul {
  position: fixed;
  top: 36%;
  left: 36%;
  width: 600px;
  height: 280px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 5px solid #5a96ff;

  text-align: center;
}

.cart-modoul img {
  width: 72px;
  height: 72px;

  padding-top: 76px;
  padding-bottom: 24px;
  text-align: center;
}

.e_table,
.e_table tr,
.e_table td,
.e_table tr td:last-child {
  font-size: 12px;
  line-height: 22px;
  padding: 5px 0;
}

.e_table {
  width: 100%;
  /* min-height: 100%; */
}

.e_table tr,
.e_table td,
.e_table tr td:last-child {
  /* padding-left: 20px;] */
  text-align: center;
  min-width: 60px;
}

.e_table tr:nth-child(odd) {
  background: #162332;
}

.e_table tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.2);
}

.e_table tbody {
  display: block;
}

[v-cloak] {
  display: none !important;
}

.send_bgbox {
  color: #fff;
  position: absolute;
  top: 38px;
  left: 20vw;
  background: url(../../../../assets/images/partners/send_bg.png) no-repeat
    center center;
  width: 1158px;
  height: 164px;
  background-size: 100%;
  text-align: center;
  line-height: 164px;
  font-size: 48px;
  font-weight: 600;
}

.send_contentbox {
  position: absolute;
  top: 220px;
  left: 3vw;
  width: 1890px;
  height: 751px;
  background: url(../../../../assets/images/partners/send_content_bg.png)
    no-repeat center center;
  background-size: 100%;
}

.send_content-t {
  /* margin: 41rem auto 46rem auto; */
  position: absolute;
  top: 500px;
  left: 50px;
  width: 1781px;
  height: 105px;

  background: url(../../../../assets/images/partners/send_title.png) no-repeat
    center center;
  background-size: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px 0 27px;
  box-sizing: border-box;
}

.kezhichi-box {
  width: 76px;
  height: 76px;
  background: url(../../../../assets/images/partners/kechizhi.png) no-repeat
    center center;
  background-size: 100%;
  margin-right: 17px;
  text-align: center;
  line-height: 76px;
  font-size: 14px;
  color: #fff;
}

.dian-box {
  width: 7px;
  height: 7px;
  background: url(../../../../assets/images/partners/dian.png) no-repeat center
    center;
  background-size: 100%;
}

.fengexian {
  width: 1px;
  height: 79px;
  margin: 0 58px;
  background: url(../../../../assets/images/partners/fengexian.png) no-repeat
    center center;
  background-size: 100%;
}

.anli-box {
  background: url(../../../../assets/images/partners/anlishu.png) no-repeat
    center center;
  background-size: 100%;
}

.send-cont-box > div {
  display: flex;
  position: relative;
  width: 576px;

  justify-content: space-between;
  text-align: center;
  margin: auto;
}

.link-box {
  width: 428px;
  height: 59px;
  background: linear-gradient(
    360deg,
    rgba(0, 140, 205, 0.2) 0%,
    rgba(0, 128, 196, 0.16) 20%,
    rgba(0, 116, 186, 0.11) 56%,
    rgba(0, 113, 184, 0.1) 100%
  );
  opacity: 0.6;
  border: 1px solid #0ff;
  overflow-y: auto;
  text-align: center;
  line-height: 28px;
}

.send-item1-box {
  position: absolute;
  top: 78px;
  background: url(../../../../assets/images/partners/item-1-bg.png) no-repeat
    center center;
  background-size: 100%;
  width: 576px;
  height: 363px;
}

.send-item2-box {
  position: absolute;
  top: 78px;
  background: url(../../../../assets/images/partners/item-2-bg.png) no-repeat
    center center;
  background-size: 100%;
  width: 576px;
  height: 363px;
}

.send-item3-box {
  /*
        background: url(../imgs/partners/item3-bg.png) no-repeat center center;
        background-size: 100%; */
  width: 219px;
  font-size: 12px;
  /* background: #435573; */
  /* height: 24px;] */
  /* line-height: 24px;] */
  margin: auto;
  color: #fff;
}

.send-item3-item {
  background: url(../../../../assets/images/partners/item-3-bg.png) no-repeat
    center center;
  background-size: 100%;
  width: 585px;
  height: 327px;
  /* overflow-y: auto; */
}

.send-item3-item1 {
  background: url(../../../../assets/images/partners/lenth2.png) no-repeat
    center center;
  background-size: 100%;
  width: 278px;
  height: 327px;
  width: 268px;
  height: 327px;
  margin-right: 20px;
  margin-top: 10px;
  /* overflow-y: auto; */
}

.send-item3-item2 {
  background: url(../../../../assets/images/partners/lenth3.png) no-repeat
    center center;
  background-size: 100%;
  width: 175px;
  height: 327px;
  margin: auto;
  /* overflow-y: auto; */
}

.length2 {
  margin-top: 20px;
}

.length1 {
  margin-top: 5px;
}

.length2 .send-item3-item1:nth-child(odd) {
  margin-right: 0;
}

.anli-bgitem {
  /* background: url(../imgs/partners/anli-bgitem.png) no-repeat center center;
        background-size: 100%; */
  width: 116px;
  height: 29px;
  text-align: right;
  padding-right: 20px;
  margin: auto;
  position: relative;
}

.avatar-uploader {
  ::v-deep .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: #409eff;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
