<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VR冶金</title>

    <!-- <link href="./css/materialdesignicons.min.css" rel="stylesheet"> -->
    <script>
        //获取html元素
        var html = document.getElementsByTagName('html')[0];
        //屏幕的宽度（兼容处理）
        var w = document.documentElement.clientWidth || document.body.clientWidth;
        //750这个数字是根据你的设计图的实际大小来的，所以值具体根据设计图的大小
        html.style.fontSize = w / 1920 + "px";
        console.log(html.style.fontSize)
    </script>
</head>


<style>
    /* 定义滚动条样式 */
    ::-webkit-scrollbar {
        width: 5rem !important;
        height: 5rem;
        background-color: rgba(0, 0, 0, 0);
    }

    /*定义滚动条轨道 内阴影+圆角*/
    ::-webkit-scrollbar-track {
        box-shadow: inset 0 0 0rem rgba(240, 240, 240, .5);
        border-radius: 5rem;
        background-color: rgba(240, 240, 240, .5);
    }

    /*定义滑块 内阴影+圆角*/
    ::-webkit-scrollbar-thumb {
        border-radius: 10rem;
        box-shadow: inset 0 0 0rem rgba(240, 240, 240, .5);
        width: 5rem !important;
        height: 5rem !important;
        background-color: #fff;
    }




    .back {
        width: 58rem;
        height: 58rem;
        position: absolute;
        top: 80rem;
        left: 2%;
        background: url(../imgs/back.png) no-repeat center center;
        background-size: 100%;
        z-index: 999999;
        cursor: pointer;
    }

    .my-header {
        width: 100%;
        height: 50rem;
        background: rgba(0, 0, 0, 0.55);
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: center;
        padding: 0 2.68%;
        color: #fff;
        box-sizing: border-box;
    }


    .logo-boder {
        background: url(../images/send/logo-boder.png) no-repeat center center;
        width: 1812rem;
        height: 421rem;
        background-size: 100%;
        margin: 136rem auto auto auto;
        padding: 71rem 79rem 46rem 79rem;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        /* justify-content: space-between; */
    }

    .logo-boder-right {
        flex: 1;
    }

    .logo-boder-right-tltie {
        display: flex;
        align-items: center;
    }

    .tltie {
        font-size: 35rem;
        color: #fff;
        font-weight: bold;
    }

    .nengli {
        margin-left: auto;
        display: flex;
        align-items: center;
    }

    .content {
        margin-top: 56rem;
        font-size: 22rem;
        color: #fff;
        height: 214rem;
        overflow-y: auto;
    }

    ::-webkit-scrollbar {
        width: 6rem;
        /* height: 6rem; */
        background-color: rgba(0, 0, 0, 0);
    }

    /*定义滚动条轨道 内阴影+圆角*/
    ::-webkit-scrollbar-track {
        box-shadow: inset 0 0 0rem rgba(240, 240, 240, 0.5);
        border-radius: 10rem;
        background-color: rgba(240, 240, 240, 0.5);
    }

    /*定义滑块 内阴影+圆角*/
    ::-webkit-scrollbar-thumb {
        border-radius: 10rem;
        box-shadow: inset 0 0 0rem rgba(240, 240, 240, 0.5);
        width: 6rem;
        height: 6rem;
        background-color: #fff;
    }

    .nengli>div {
        color: #1693ee;
        padding: 5rem 10rem;
        border-radius: 30rem;
        font-size: 18rem;
        border: 1rem solid #1693ee;
        margin-right: 10rem;
    }

    .case-bg {
        width: 1755rem;
        height: 413rem;
        border-radius: 24rem;
        border: 4rem solid #ffffff30;
        margin: 40rem auto auto auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 38rem;
        box-sizing: border-box;
        background: linear-gradient(180deg, #ffffff17 0%, #ffffff08 100%);
    }

    .case-list {

        margin: 0 40rem;
        margin-top: 40rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .case-item {
        background: url(../images/send/case-item.png) no-repeat center center;
        width: 402rem;
        height: 345rem;
        background-size: 100% 100%;
        padding: 58rem 15rem 10rem 24rem;
        box-sizing: border-box;
        cursor: pointer;
    }

    /* css选择器,第2,5,8,11,14这样的规律怎么写 */

    .case-list>.case-item:nth-child(2) {
        margin: 0 80rem;
    }

    .case-item-icons {
        display: flex;
        align-items: center;

        color: #fff;
        font-size: 20rem;
    }

    .case-item-btns {
        display: flex;
        color: #fff;
        width: 100%;
    }

    .case-item-btns>div {
        display: flex;
        align-items: center;
        font-size: 15rem;
        height: 50rem;
        text-align: center;
        flex: 1;
    }

    .case-item-btns-1 {
        border-right: 1rem solid #fff;
    }

    .bg_img {
        width: 1920rem;
        height: 1080rem;
        transform-origin: 0 0;
        /* background-color: rgba(0, 0, 0, 0); */
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0;
        background: url(../images/send/case_bg.png) no-repeat center center;
        background-size: 100%;
        z-index: 999;
    }


    #app {
        position: relative;
        background-image: url(../imgs/index/home_bg.jpg);

        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100;
        zoom: 1;
        background-repeat: no-repeat;
        background-size: cover;
        background-size: 100%;
        -webkit-background-size: cover;
        -o-background-size: cover;
        background-position: center 0;
        font-size: 28rem;
        color: #fff;
        /* overflow: hidden; */
        /* overflow-y: scroll; */
    }

    .content-box {
        background-color: rgba(0, 0, 0, 0.75);
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .logo-boder-left {
        width: 402rem;
        height: 304rem;
        border-radius: 25rem;
        margin-right: 100rem;
    }

    .cart-box {
        position: fixed;
        bottom: 134rem;
        right: 24rem;
        width: 101rem;
        height: 86rem;
        cursor: pointer;
        z-index: 999999;
    }

    .cart-box img {
        width: 100%;
        height: 100%;
    }

    .cart-box>div {
        width: 25rem;
        height: 25rem;
        line-height: 25rem;
        border-radius: 50%;
        background: #f00;
        color: #fff;
        text-align: center;
        position: fixed;
        bottom: 188rem;
        right: 34rem;
        font-size: 16rem;
    }


    .bg_server_img {
        width: 1920rem;
        height: 1080rem;
        transform-origin: 0 0;
        /* background-color: rgba(0, 0, 0, 0); */
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0;
        background: url(../images/send/sever-bg.png) no-repeat center center;
        background-size: 100%;
    }

    .title {
        margin: 104rem auto 39rem auto;
        font-size: 35rem;
        color: #0094ff;
        font-weight: bold;
        text-align: center;
    }

    .content222 {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        align-items: center;
        margin: 0 121rem;
    }

    .left-box {
        width: 612rem;
        height: 815rem;
        border-radius: 25rem;
        background: linear-gradient(180deg, #ffffff1a 0%, #ffffff21 100%);
        border: 4rem solid #ffffff29;
        box-shadow: 0 3rem 6rem #00000029;
    }

    .right-box {
        width: 938rem;
    }

    .right-contet {
        width: 938rem;
        height: 738rem;
        border-radius: 25rem;
        background: linear-gradient(180deg, #ffffff1a 0%, #ffffff21 100%);
        border: 4rem solid #ffffff29;
        box-shadow: 0 3rem 6rem #00000029;
        padding: 30rem 45rem;
        box-sizing: border-box;
        display: flex;
        /* align-items: center; */
        justify-content: space-between;
    }

    .right-contet-nengli {
        width: 536rem;
    }

    .right-contet-canpin {
        width: 231rem;
    }

    .nengli-box {
        width: 100%;
        text-align: center;
        border-bottom: 2rem solid #6a6a6a;
        line-height: 40rem;
        color: #1c9efd;
        font-size: 20rem;
    }

    .nengli-contet {
        display: flex;
    }

    .nengli-contet>div {
        width: 50%;
        text-align: center;
    }

    .nengli-contet-title {
        margin: 30rem 0 20rem 0;
        font-size: 20rem;
    }

    .nengli-citys {
        margin: 35rem 0;
        text-align: center;
    }

    .citylist {
        width: 95%;
        margin: 0 auto 70rem auto;
        overflow: auto;
        display: flex;
        align-items: center;
        justify-content: center;
        justify-content: flex-start;
    }

    .cityitem {
        text-align: center;
        margin: auto 4rem;
        white-space: nowrap;
    }

    .rigth-btns {
        height: 55rem;
        border-radius: 14rem;
        background: linear-gradient(180deg, #ffffff1f 0%, #ffffff17 100%);
        border: 2rem solid #ffffff1f;
        box-shadow: 0 3rem 6rem #00000029;
        width: 100%;
        margin-bottom: 30rem;
        display: flex;
        align-items: center;
        /*  */
    }

    .rigth-btns>div:first-child {
        width: 158rem;
        height: 55rem;
        border-radius: 14rem;
        background: #1c9efd;
        box-shadow: 0 3rem 6rem #00000029;
        text-align: center;
        line-height: 55rem;
        /* 弹性盒子不被挤压 */
        flex-shrink: 0;
    }

    .rigth-btns>div:last-child {
        flex: 1;
        text-align: center;
        line-height: 55rem;
        cursor: pointer;
        /* 最对显示2行文字 */
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        width: 369rem;
        overflow: hidden;
    }

    .canpin-box {
        width: 100%;
        text-align: center;
        border-bottom: 2rem solid #6a6a6a;
        line-height: 40rem;
        color: #1c9efd;
        font-size: 20rem;
    }

    .canpin-list {
        height: 560rem;
        overflow-y: auto;
        overflow-x: hidden;
        margin-top: 30rem;
    }

    .canpin-list::-webkit-scrollbar {
        display: none;
    }

    .canpin-item {
        width: 231rem;
        height: 147rem;
        border-radius: 16rem;
        background: linear-gradient(180deg, #ffffff29 0%, #ffffff21 100%);
        border: 1rem solid #ffffff1a;
        margin-bottom: 13rem;
        text-align: center;
        font-size: 20rem;
    }

    .right-btns {
        display: flex;
        margin-top: 22rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .right-btns>div {
        width: 443rem;
        height: 55rem;
        border-radius: 13rem;
        background: #1c9efd;
        box-shadow: 0 3rem 6rem #00000029;
        text-align: center;
        line-height: 55rem;
        font-size: 20rem;
        cursor: pointer;
    }

    .left-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
    }

    .left-box img {
        width: 508rem;
        height: 214rem;
        cursor: pointer;
    }


    [v-cloak] {
        display: none !important
    }

    .imgbox {
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        overflow: hidden;
    }

    .sever {
        width: 1767rem;
        height: 842rem;
        border-radius: 34rem;
        background: linear-gradient(180deg, #ffffff1c 0%, #ffffff1c 100%);
        border: 4rem solid #ffffff33;
        box-shadow: 0 3rem 15rem #00000029;
        margin: 186rem auto 0;
        padding: 68rem;
        box-sizing: border-box;
        overflow-y: auto;


    }

    .sever1212>div {
        display: flex;
        align-items: center;
    }

    .sever1212 {
        padding: 0;
    }

    .download-box {
        margin-top: 50rem;
        justify-content: center;
        display: flex;
    }

    .content111 {
        margin-top: 80rem;
        width: 100%;
    }

    .content111-box {
        width: 1571rem;
        height: 588rem;
        border-radius: 33rem;
        background: linear-gradient(180deg, #ffffff1c 0%, #ffffff1c 100%);
        border: 4rem solid #ffffff33;
        box-shadow: 0 3rem 15rem #00000029;
        padding: 57rem 50rem 93rem 50rem;
        box-sizing: border-box;
        margin: auto;
    }

    .title111 {
        height: 56rem;
        background: #1c9efd;
        width: 100%;
        text-align: center;
        line-height: 56rem;
        font-size: 25rem;
        font-weight: bold;
    }

    .imgs111 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 72rem;
    }

    .anli-desc {
        width: 634rem;
        font-size: 18rem;
        color: #fff;
    }

    .anli-desc>div:first-child {
        color: #1c9efd;
    }

    .anli-desc>div:last-child {
        height: 320rem;
        overflow-y: auto;
    }

    .download {
        width: 236rem;
        height: 48rem;
        line-height: 48rem;
        text-align: center;
        background: #1c9efd;
        border-radius: 8rem;
        cursor: pointer;
    }


    .el-table tr {
        background: transparent !important;
        color: #fff !important;
    }

    .has-gutter tr th {
        background: #1c9efd !important;
        color: #fff !important;
        text-align: center !important;
    }

    .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
        background-color: #000 !important;
        /* 修改为需要的背景颜色 */
        color: #fff !important;
        /* 如果需要修改文字颜色 */
    }

    .el-progress path:first-child {
        stroke: #606577 !important;
    }

    .el-progress__text {
        font-size: 40rem !important;
    }

    .el-carousel__container {
        height: 100% !important;
    }



    .el-icon-arrow-left {
        background: url(../images/send/pren.png) no-repeat center center !important;
        width: 46rem !important;
        height: 46rem !important;
        background-size: 100% !important;
    }

    .el-icon-arrow-right {
        background: url(../images/send/next.png) no-repeat center center !important;
        width: 46rem !important;
        height: 46rem !important;
        background-size: 100% !important;
    }

    .el-icon-arrow-left::before,
    .el-icon-arrow-right::before {
        content: '' !important;
    }

    .cart-modoul {
        position: fixed;
        top: 36%;
        left: 36%;
        width: 600rem;
        height: 280rem;
        background-color: rgba(0, 0, 0, 0.8);
        border: 5rem solid #447de0;

        text-align: center;
    }

    .cart-modoul img {
        width: 72rem;
        height: 72rem;

        padding-top: 76rem;
        padding-bottom: 24rem;
        text-align: center;
    }

    .adfsdsdfsd {
        cursor: pointer;
    }

    .adfsdsdfsd+.anli-hovers {
        position: absolute;
        top: 0;
        left: 0;
        width: 715rem;
        height: 330rem;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        /* border-radius: 20rem; */
    }

    .img_icon+span {
        display: none;
    }

    .img_icon {
        cursor: pointer;
    }

    .img_icon:hover+span {
        display: block;
    }
</style>


<body>
    <div id="app" v-cloak>
        <div class="back" @click="gotoback"></div>
        <div class="my-header" v-if="myheader == 1">
            <div style="width: 122rem;"></div>
            <div style="font-size: 26rem;margin-left: 34%;display: flex;align-items: center;width: 40%;">双碳能耗
                <img style="width:20rem ;height: 20rem;margin-left: 10rem;padding-right: 5rem;"
                    src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/tips/header.png" alt=""
                    class="img_icon">
                <span style="font-size: 16rem;padding: 0 24rem; border-radius: 13rem;border: 1px solid #FFFFFF;">政企事业部
                    、物联网公司 联合策划制作</span>
            </div>
            <div>
                <img style="width: 122rem;" src="../images/logo.png" alt="">
            </div>
        </div>
        <div class="cart-box" @click="jumpcart()">
            <div v-if="cartNum > 0">{{cartNum}}</div>
            <img src="../imgs/partners/icon_craft.png" alt="">
        </div>
        <div class="content-box" v-if="step == 1">

            <div class="logo-boder">
                <img class="logo-boder-left" :src="detalis.iconUrl" alt="" />
                <div class="logo-boder-right">
                    <div class="logo-boder-right-tltie">
                        <div class="tltie">{{ detalis.title }}</div>
                        <div class="nengli">
                            <div v-for="item in detalis.tagCode" :key="item">{{ item }}</div>
                            <img v-if="detalis.tagCode?.length > 2" src="../images/send/msg.png" alt=""
                                style="width: 27rem; height: 27rem" />
                        </div>
                    </div>
                    <div class="content">
                        {{ detalis.introduce }}
                    </div>
                </div>
            </div>

            <div class="case-bg" v-if="detalis.solutionInfoVos && detalis.solutionInfoVos.length > 0">
                <el-carousel :interval="5000" style="width: 100%;height: 100%;" :arrow="arrowDisplay">
                    <el-carousel-item v-for="(itemGroup, index) in groupedItems" :key="index">
                        <div class="case-list">
                            <div class="case-item" v-for="(item, idx) in itemGroup" :key="idx"
                                @click="gosolution(item)">
                                <div class="case-item-icons">
                                    <div>{{ item.title }}</div>
                                    <img src="../images/send/yanjin.png" alt=""
                                        style="width: 46rem; margin-left: auto" />
                                    <img src="../images/send/onload.png" alt="" style="width: 46rem" />
                                </div>
                                <img :src="item.url" alt=""
                                    style="width: 344rem; height: 162rem; border-radius: 20rem; margin-bottom: 0rem"
                                    @click="isImg = true" />
                                <div class="case-item-btns">
                                    <div class="case-item-btns-1">
                                        <img src="../images/send/read.png" alt="" style="width: 95rem" />
                                        <div>
                                            <div style="color: #a5daff; font-size: 25rem">{{ item.readNumber }}</div>
                                            <div>阅读次数</div>
                                        </div>
                                    </div>
                                    <div>
                                        <img src="../images/send/load.png" alt="" style="width: 95rem" />
                                        <div>
                                            <div style="color: #a5daff; font-size: 25rem">{{ item.shareNumber }}</div>
                                            <div>分享人次</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-carousel-item>
                </el-carousel>
                <!-- <img src="../images/send/next.png" alt="" style="width: 46rem; cursor: pointer" /> -->
            </div>

        </div>
        <div v-if="step == 2 " class="content-box" style="background: rgba(0,0,0,0.9);">

            <div class="title">{{ sendName}}</div>
            <div v-if="solutions.number">
                <div class="content222">
                    <div class="left-box">
                        <img src="../images/send/case.png" alt="" @click="imgShow = true" />
                        <img src="../images/send/sever.png" alt="" @click="isSever = true" />
                        <img src="../images/send/anli.png" alt="" @click="isAnli = true;log()" />
                    </div>
                    <div class="right-box">
                        <div class="right-contet">
                            <div class="right-contet-nengli">
                                <div class="nengli-box">支撑能力</div>
                                <div class="nengli-contet">
                                    <div>
                                        <!-- border: 15rem solid #1c9efd; -->
                                        <div class="nengli-contet-title">可支撑人数</div>
                                        <el-progress type="circle" :percentage="solutions.number" :width="100"
                                            :stroke-width="15" color="#1c9efd" text-color="#fff"
                                            :format="formatPercentage"></el-progress>
                                    </div>
                                    <div>
                                        <div class="nengli-contet-title">全国案例数</div>
                                        <el-progress type="circle" :percentage="solutions.achieveNumber" :width="100"
                                            :stroke-width="15" color="#5493ff" text-color="#fff"
                                            :format="formatPercentage"></el-progress>
                                    </div>
                                </div>
                                <div class="nengli-citys">可支撑地市</div>
                                <div class="citylist">
                                    <div class="cityitem" v-for="(item, index) in solutions.qxDevelopmentCityList"
                                        :key="index">
                                        <img src="../images/send/city-icon.png" alt=""
                                            style="width: 71rem; height: 43rem; margin: 0 10rem" />
                                        <div style="text-align: center">{{ item.label }}</div>
                                    </div>
                                </div>

                                <div class="rigth-btns" v-if=" solutions.qxLinkList.length">
                                    <div>友情链接</div>
                                    <div>{{ solutions.qxLinkList[0].linkUrl }}</div>
                                </div>
                                <div class="rigth-btns">
                                    <div>联系生态</div>
                                    <img src="../images/send/phone.png" alt=""
                                        style="width: 61rem; margin: 0 0 0 56rem" />
                                    <div style="text-align: left">{{ solutions.phone }}</div>
                                </div>
                            </div>
                            <div class="right-contet-canpin">
                                <div class="canpin-box">相关产品</div>
                                <div class="canpin-list">
                                    <div class="canpin-item" v-for="(item, index) in solutions.demoVos" :key="index">
                                        <img :src="item.url" alt=""
                                            style="width: 212rem; height: 98rem; border-radius: 14rem; margin: 9rem auto 0 auto" />
                                        <div>{{ item.name }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="right-btns">
                            <div @click="comecart()">加入购物车</div>
                            <div @click="gotojicheng()">集成报价器</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="imgbox" v-if="imgShow">
            <img style="width: 100%;height: 100%;" :src="imgUrl" alt="">
        </div>

        <div class="imgbox bg_server_img" v-if="isSever">
            <div class="sever">
                <el-table :data="solutions.qxServiceList" border style="width: 100%; background: transparent">
                    <el-table-column prop="serviceType" label="类别">
                        <template slot-scope="scope">
                            <div style="text-align: center;">
                                {{
                                scope.row.serviceType === 0 ? '硬件' :
                                scope.row.serviceType === 1 ? '软件' :
                                scope.row.serviceType === 2 ? '集成服务' : '其他'
                            }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="serviceName" label="名称"></el-table-column>
                    <el-table-column prop="serviceExplain" label="功能介绍"></el-table-column>
                    <el-table-column prop="serviceNumber" label="数量"></el-table-column>
                    <el-table-column prop="serviceUnit" label="单位"></el-table-column>
                    <el-table-column prop="servicePrice" label="单价"></el-table-column>
                </el-table>
            </div>
        </div>
        <div class="imgbox bg_server_img" v-if="isAnli">
            <div class="sever1212 sever">
                <div>
                    <img v-if="solutions.qxCaseVoList.length > 1" src="../images/send/pren.png" alt=""
                        style="width: 46rem; margin: 354rem 25rem 0; height: 53rem; cursor: pointer;"
                        @click="prenCase()" />
                    <div class="content111">
                        <div class="content111-box">
                            <div class="title111">{{ solutions.qxCaseVoList[caseIndex].caseName }}</div>
                            <div class="imgs111" style="position: relative;">
                                <div @mouseover="handleMouseOver($event)" @mouseleave="handleMouseLeave($event)">
                                    <img :src="solutions.qxCaseVoList[caseIndex].img" alt=""
                                        style="width: 715rem; height: 310rem" class="adfsdsdfsd" />
                                    <div class="anli-hovers" v-show="solutions.qxCaseVoList[caseIndex].show"
                                        @click="gotoUrl(solutions.qxCaseVoList[caseIndex].linkUrl)">
                                        <img src="../images/send/ebianpai.png" alt="" style="width: 74rem;">
                                    </div>
                                </div>
                                <div class="anli-desc">
                                    <div>案例描述</div>
                                    <div>
                                        {{solutions.qxCaseVoList[caseIndex].caseExplain}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <img v-if="solutions.qxCaseVoList.length > 1" src="../images/send/next.png" alt=""
                        style="width: 46rem; margin: 354rem 25rem 0; height: 53rem; cursor: pointer;"
                        @click="nextCase()" />
                </div>
                <div class="download-box" v-if="solutions.qxCaseVoList[caseIndex].pdtArr.length">
                    <img src="../images/send/download.png" alt=""
                        style="width: 51rem; height: 55rem; margin-right: 50rem; cursor: pointer" />
                    <div class="download" @click="downloadPdt(solutions.qxCaseVoList[caseIndex].pdtArr)">下载</div>
                </div>
            </div>

            <div class="imgbox" v-if="anliiframeShow" style="background: #000;">
                <iframe style="width: 100%;height: 100%;border: 0;" :src="anliiframeUrl + '?type=1'"
                    frameborder="0"></iframe>
            </div>
        </div>
        <div class="imgbox" v-if="iframeShow" style="background: #000;">
            <iframe style="width: 100%;height: 100%;border: 0;" :src="iframeUrl + '?type=1'" frameborder="0"></iframe>
        </div>

        <div class="cart-modoul" v-if="ative">
            <img :src="modoulObj.src" alt="">
            <div :style="{'color': modoulObj.color}">{{modoulObj.text}}</div>
        </div>
    </div>

    <script src="../js/vue.js"></script>
    <script src="../js/qs.js"></script>
    <script src="../js/elementui.js"></script>
    <script src="../js/province.js"></script>
    <script src="../js/baseurl.js"></script>
    <script src="../js/axios.min.js"></script>
    <link rel="stylesheet" href="../css/elementui.css">
    <!-- <link rel="stylesheet" href="part.css"> -->

    <script>
        var vm = new Vue({
            el: '#app',
            data() {
                return {
                    detalis: {},
                    province,
                    qxServiceList: [],
                    qxDevelopmentCityList: [],
                    qxLinkList: [],
                    cartNum: 0,
                    baseurl,
                    step: 1,
                    sceneId: null,
                    industryId: null,
                    sceneList: [],
                    sceneList1: [],
                    imgUrl: '',
                    caseIndex: 0,
                    qxCaseVoList: [],
                    number: '', //全国案例数
                    achieveNumber: '', //可支撑人数,
                    isshow: false,
                    sceneName: '',
                    token: localStorage.getItem('token'),
                    isAnli: false,
                    tilte: '',
                    userId: '',
                    ative: false,
                    modoulObj: {

                        src: '',
                        text: '',
                        color: '',
                    },
                    cartId: '',
                    myheader: '',
                    imgShow: false,
                    developmentId: '',
                    hometips: 0,
                    sendtips: 0,
                    iframeUrl: '',
                    iframeShow: false,
                    isSolution: false,
                    sendName: '',
                    isImg: false,
                    solutions: {},
                    isSever: false,
                    anliiframeShow: false,
                    anliiframeUrl: ''

                }
            },
            mounted() {
                const query = window.Qs.parse(location.search.substring(1));
                if (query.myheader) this.myheader = query.myheader
                console.log(query, '7100000000000000000000000000000000')
                this.industryId = query.industryId
                this.sceneId = query.sceneId;
                this.userId = query.userId;
                this.sceneName = query.sceneName
                if (this.userId != undefined && this.sceneName != undefined) {
                    this.step = 2
                    this.getdetalis1(this.sceneId, this.userId)
                } else {
                    this.getsceneList()
                }
                this.tilte = query.tilte;
                this.ishowtip()
                let that = this;
                that.setScale();
                window.onresize = function windowResize() {
                    that.setScale();
                };
            },
            computed: {
                groupedItems() {
                    const chunkSize = 3;
                    const grouped = [];
                    for (let i = 0; i < this.detalis.solutionInfoVos.length; i += chunkSize) {
                        grouped.push(this.detalis.solutionInfoVos.slice(i, i + chunkSize));
                    }
                    return grouped;
                },
                arrowDisplay() {
                    const length = this.groupedItems.length;
                    if (length <= 1) {
                        return 'never'; // 如果只有1个或0个，不显示箭头
                    }
                    return 'always'; // 其他情况下显示箭头
                }
            },
            methods: {
                gotoUrl(url) {
                    if (!url) return
                    this.anliiframeShow = true
                    this.anliiframeUrl = url

                },

                handleMouseOver(event) {
                    event.stopPropagation(); // 阻止事件传播
                    if (this.solutions.qxCaseVoList[this.caseIndex].linkUrl) {
                        this.solutions.qxCaseVoList[this.caseIndex].show = true
                    }

                    this.$forceUpdate();
                    console.log(this.solutions.qxCaseVoList[this.caseIndex].show)
                },
                handleMouseLeave(event) {
                    event.stopPropagation(); // 阻止事件传播
                    this.solutions.qxCaseVoList[this.caseIndex].show = false
                    this.$forceUpdate();
                    console.log(this.solutions.qxCaseVoList[this.caseIndex].show)
                },
                gotojicheng() {
                    window.location.replace(`integratedquotation.html?myheader=${this.myheader}`)
                },
                log() {
                    console.log(this.qxCaseVoList, this.caseIndex)
                },
                prenCase() {
                    if (this.caseIndex > 0) {
                        this.caseIndex--;
                    }
                },
                nextCase() {
                    if (this.caseIndex < this.solutions.qxCaseVoList.length - 1) {
                        this.caseIndex++;
                    }
                },
                downloadPdt(pptx) {
                    pptx.forEach(i => {
                        window.open(i, '_blank');
                    });
                },
                formatPercentage(percentage) {
                    return `${percentage}`
                },
                gosolution(item) {
                    this.isSolution = true
                    this.getdetalis1(item.sceneId, this.userId)
                    this.sceneId = item.sceneId
                    this.sendName = item.title

                },
                chekiframeUrl(item) {
                    if (item.linkUrl) {
                        this.iframeUrl = item.linkUrl
                        this.iframeShow = true
                    }

                },
                ishowtip() {
                    axios.get(this.baseurl + 'industrytip/ishowtip?type=4', {
                        headers: {
                            Authorization: "Bearer " + this.token,
                        },
                    }).then(res => {
                        this.ishowtip2()
                        console.log(res.data)
                        if (res.data.data.isShowTip == 0) {
                            this.hometips = 1;

                        }
                    })
                },
                ishowtip2() {
                    axios.get(this.baseurl + 'industrytip/ishowtip?type=5', {
                        headers: {
                            Authorization: "Bearer " + this.token,
                        },
                    }).then(res => {
                        console.log(res.data)
                        if (res.data.data.isShowTip == 0) {
                            this.sendtips = 1;
                        }
                    })
                },
                addishowtip(type) {
                    let pamas = {
                        type: type
                    }
                    axios.post(this.baseurl + 'industrytip/addishowtip', pamas, {
                        headers: {
                            Authorization: "Bearer " + this.token,
                        },
                    }).then(res => {
                        if (res.data.code == 200) {
                            type == 4 ? this.hometips = 0 : this.sendtips = 0;
                        }
                    })
                },
                download(item) {
                    item.pdtArr.forEach(i => {
                        window.open(i, '_blank')
                        // window.open(item.img,'_blank')
                    })

                    // top.location.href = item.url
                    //
                    // console.log(item)
                },
                linkUrl(url) {
                    window.open(url, '_blank')
                    // top.location.href = url
                },
                // 
                // 跳转购物车
                jumpcart() {
                    window.location.replace(`cart.html?myheader=${this.myheader}`)
                },
                // 加入购物车
                comecart() {
                    let pamas = {
                        developmentId: this.cartId
                    }
                    axios.post(this.baseurl + 'shopping', pamas, {
                        headers: {
                            Authorization: "Bearer " + this.token,
                        },
                    }).then(res => {
                        this.ative = true
                        console.log(this.ative)
                        let code = res.data.code
                        if (code == 200) {
                            this.modoulObj.text = '已添加至购物车!'
                            this.modoulObj.src = '../imgs/success.png'
                            this.modoulObj.color = "#20bcc0"
                            this.getcartList()
                        } else if (code == 501) {
                            this.modoulObj.text = '请勿重复添加购物车'
                            this.modoulObj.src = '../imgs/error.png'
                            this.modoulObj.color = "#FF5656"
                        } else if (code == 401 || code == 403) {
                            this.modoulObj.text = 'token失效,请重新登录'
                            this.modoulObj.src = '../imgs/error.png'
                            this.modoulObj.color = "#FF5656"
                            setTimeout(() => {
                                window.location.replace(`login.html?myheader=${this.myheader}`)
                            }, 1000)

                        }
                        setTimeout(() => {
                            this.ative = false

                        }, 1000)
                        console.log(res)
                    })
                },

                getcartList() {

                    axios.get(this.baseurl + 'shopping/list?industryCode=HY016', {
                        headers: {
                            Authorization: "Bearer " + this.token,
                        },
                    }).then(res => {
                        this.isshow = true
                        let code = res.data.code;
                        let msg = res.data.msg;
                        if (code == 200) {
                            let cartList = res.data.data;
                            this.cartNum = cartList.length;

                        } else if (code == 403 || code == 401) {
                            this.ative = true;
                            this.modoulObj.src = '../imgs/error.png'
                            this.modoulObj.color = "#FF5656"
                            this.modoulObj.text = 'token失效请登录'
                            setTimeout(() => {
                                this.ative = false;
                            }, 1000)
                        } else {
                            this.ative = true;
                            this.modoulObj.src = '../imgs/error.png'
                            this.modoulObj.color = "#FF5656"
                            this.modoulObj.text = msg
                            setTimeout(() => {
                                this.ative = false;
                            }, 1000)
                        }
                        console.log(res)
                    })
                },

                getdetalis1(item, item1) {
                    // this.getcartList()
                    // this.sceneName = item.sceneName;
                    let pamas = {
                        sceneId: item,
                        userId: parseInt(item1)

                    }
                    axios.get(this.baseurl + 'scenehtml/listDevelopment?sceneId=' + item + '&userId=' + item1, {
                        headers: {
                            Authorization: "Bearer " + this.token,
                        },
                    }).then(res => {
                        this.step = 2
                        this.solutions = res.data.data[0];
                        let qxCaseVoList = this.solutions.qxCaseVoList;
                        this.cartId = res.data.data[0].id
                        this.imgUrl = res.data.data[0].imgUrl;
                        let qxServiceList = this.solutions.qxServiceList;
                        let qxDevelopmentCityList = this.solutions.qxDevelopmentCityList;

                        // 处理开发城市列表
                        let arr = [];
                        qxDevelopmentCityList.forEach(j => {
                            let obj1 = {};
                            province.forEach(z => {
                                z.children.forEach(y => {
                                    if (y.value == j.cityId) obj1.label = y
                                        .label;
                                });
                            });
                            arr.push(obj1);
                        });
                        this.solutions.qxDevelopmentCityList = arr;

                        if (this.solutions.qxDevelopmentCityList != null && this.solutions
                            .qxDevelopmentCityList.length === 340) {
                            this.solutions.qxDevelopmentCityList = [{
                                label: '全国'
                            }];
                        }

                        // 处理服务列表
                        qxServiceList = qxServiceList.sort((a, b) => a.serviceType - b.serviceType);

                        qxServiceList.forEach(item => {
                            item.stype = qxServiceList.reduce((count, e) => {
                                if (item.serviceType === e.serviceType) {
                                    count++;
                                }
                                return count;
                            }, 0);
                        });

                        qxServiceList.forEach((item, index) => {
                            if (index > 0) {
                                if (item.serviceType === qxServiceList[index - 1].serviceType) {
                                    item.stype = 0;
                                }
                            }
                        });

                        this.solutions.qxServiceList = qxServiceList;

                        // 处理案例列表
                        this.solutions.qxCaseVoList = qxCaseVoList;

                        this.solutions.qxCaseVoList.forEach(item => {
                            let pdtArr = [];
                            item.caseUrl = JSON.parse(item.caseUrl);
                            item.caseUrl.forEach(i => {
                                if (i.url) {
                                    let type = i.url.split('.').length;

                                    i.type = i.url.split('.')[type - 1];
                                    if (i.type === 'png' || i.type === 'jpg' || i
                                        .type === 'jpeg') item.img = i.url;
                                    if (i.type === 'mp4') item.mp4 = i.url;
                                    if (['pdf', 'txt', 'ppt', 'docx', 'doc', 'pptx']
                                        .includes(i.type)) {
                                        item.pdtname = i.name;
                                        pdtArr.push(i.url);
                                    }
                                }
                                item.pdtArr = pdtArr;
                            });
                            item.show = false;
                            item.number = this.solutions.number;
                            item.achieveNumber = this.solutions.achieveNumber;
                        });
                        console.log(this.solutions, '12866666666666666666666666666666666')

                    })
                },

                // 获取支持的场景
                getsceneList() {
                    this.getcartList()
                    axios.get(this.baseurl + '/home/<USER>/detail?industryId=' + parseInt(this.industryId) +
                        '&userId=' + parseInt(this.sceneId), {
                            headers: {
                                Authorization: "Bearer " + this.token,
                            },
                        }).then(res => {
                        let code = res.data.code
                        if (code == 200) {
                            this.detalis = res.data.data
                            this.userId = res.data.data.userId
                            if (this.detalis.tagCodeDesc) {
                                this.detalis.tagCode = this.detalis.tagCodeDesc.split(
                                    ','); // 使用 detalis.tagCodeDesc
                            } else {
                                this.detalis.tagCode = [];
                            }
                        }

                    })
                },
                gotoback() {
                    if (this.anliiframeShow) {
                        return this.anliiframeShow = false
                    }
                    if (this.industryId === undefined) {

                        if (this.imgShow || this.iframeShow || this.isAnli || this.isSever) {
                            this.imgShow = false
                            this.iframeShow = false
                            this.isAnli = false
                            this.isSever = false
                        } else {
                            window.location.replace(`cart.html?myheader=${this.myheader}`)
                        }
                    } else {
                        if (this.step == 2) {
                            if (this.imgShow || this.iframeShow || this.isAnli || this.isSever) {
                                this.imgShow = false
                                this.iframeShow = false
                                this.isAnli = false
                                this.isSever = false
                            } else {
                                this.step = 1
                                let endParmas = {
                                    clickType: 11,
                                    clickValue: "HY016",
                                    childType: this.developmentId,
                                    endTime: '1'
                                }
                                axios.post(this.baseurl + "clickrecords/add", endParmas, {
                                    headers: {
                                        Authorization: "Bearer " + this.token
                                    },
                                }).then((res) => {})
                            }
                        } else {


                            // let endParmas = {

                            //     clickType: 11,
                            //     clickValue: "HY016",

                            //     childType: this.sceneId,
                            //     endTime: '1'
                            // }

                            // axios.post(this.baseurl + "clickrecords/add", endParmas, {
                            //     headers: { Authorization: "Bearer " + this.token },
                            // }).then((res) => {
                            //     let code = res.data.code;
                            //     if (code == 200) {
                            window.location.replace(`ecologicalchain.html?myheader=${this.myheader}`)
                            // }
                            //     console.log(res);
                            // });
                            // window.location.replace(`ecologicalchain.html?myheader=${this.myheader}`)

                        }
                    }
                },

                setScale() {
                    var html = document.getElementsByTagName('html')[0];
                    //屏幕的宽度（兼容处理）
                    var w = document.documentElement.clientWidth || document.body.clientWidth;
                    console.log(w)
                    //750这个数字是根据你的设计图的实际大小来的，所以值具体根据设计图的大小
                    html.style.fontSize = w / 1920 + "px";
                    console.log(html.style.fontSize)
                },

            },
            //生命周期 - 创建完成（可以访问当前this实例）
        })
    </script>
</body>


</html>