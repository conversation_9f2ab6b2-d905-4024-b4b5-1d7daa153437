import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

//嵌入共享平台iframe里面不能放到cookie里面
export function getToken() {
  let token = Cookies.get(TokenKey);
  if (token == null)
    token = localStorage.getItem(TokenKey);
  return token


}

export function setToken(token) {
  localStorage.setItem(TokenKey, token)
  return Cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  localStorage.removeItem(TokenKey)
  return Cookies.remove(TokenKey)
}
