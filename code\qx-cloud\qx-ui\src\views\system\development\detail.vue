<template>
  <div style=" width: 100%;height: 100%;">
    <Preview ref="previewRef" />
  </div>
</template>

<script>
import Preview from "../hzhb/develop/preview.vue";
import { getProvince } from "../../../utils/province";
import { getDevelopmentDetail } from "@/api/system/development";
export default {
  components: {
    Preview,
  },
  data() {
    return {
      name: null,
      optionsProvince: [],
      selectDeviceTableList: [], //表格数据
      selectSoftwareTableList: [], //软件数据
      selectIntegrateTableList: [], //集成服务
      selectOtherTableList: [], //集成服务
      qxCaseList: [], //案例
      qxLinkList: [], //链接
      developmentCityList: [], //城市的列表
      cityName: "",
      scene: {},
      development: {},
      industryCode: "",
      developmentId: null,
    };
  },
  created() {
    this.developmentId = this.$route.params && this.$route.params.developmentId;
    if (this.developmentId != null) this.getDetailList(this.developmentId);
  },
  methods: {
    clearList() {
      this.deviceList = [];
      this.serviceList = [];
      this.selectDeviceList = []; //选中的设备
      this.selectDeviceTableList = []; //表格数据
      this.selectSoftwareTableList = []; //软件数据
      this.selectIntegrateTableList = []; //集成服务
      this.selectOtherTableList = []; //集成服务
      this.qxCaseList = [];
      this.qxLinkList = [];
    },

    getDetailList(id) {
      let that = this;
      this.clearList();
      getDevelopmentDetail(id).then((res) => {
        if (res.code === 200) {
          this.scene = res.data.scene;
          this.serviceList = res.data.serviceList;
          this.serviceList.forEach((row) => {
            if (row.serviceType === 0) {
              this.selectDeviceTableList.push(row);
            } else if (row.serviceType === 1) {
              this.selectSoftwareTableList.push(row);
            } else if (row.serviceType === 2) {
              this.selectIntegrateTableList.push(row);
            } else if (row.serviceType === 3) {
              this.selectOtherTableList.push(row);
            } else if (row.serviceType === 4) {
              this.qxCaseList.push(row);
            }
          });
          this.qxLinkList = res.data.linkList;
          this.qxCaseList = res.data.caseList;
          this.developmentCityList = res.data.developmentCityList;
          this.development = res.data.development;

          let pdtArr = [];
          this.qxCaseList.forEach((item) => {
            this.$set(item, "caseUrl", JSON.parse(item.caseUrl));
            item.caseUrl.forEach((i) => {
              i.type = i.url.substring(i.url.length - 3);
              if (i.type == "png" || i.type == "jpg") item.img = i.url;
              if (i.type == "mp4") item.mp4 = i.url;
              if (i.type == "pdf" || i.type == "txt" || i.type == "ppt") {
                item.pdtname = i.name;
                pdtArr.push(i.url);
              }
              item.pdtArr = pdtArr;
            });
          });

          this.industryCode = res.data.qxIndustry.industryCode;

          //显示市级城市
          if (
            this.developmentCityList != null &&
            this.developmentCityList.length === 340
          ) {
            this.developmentCityList = [{ label: "全国" }];
          } else {
            this.developmentCityList.forEach((row) => {
              this.optionsProvince.forEach((item) => {
                if (row.provinceId == item.value) {
                  item.children.forEach((child) => {
                    if (row.cityId == child.value) {
                      row.label = child.label;
                    }
                  });
                }
              });
            });
          }

          this.$refs.previewRef.imgUrl =
            this.development == null || this.development.imgUrl == null
              ? null
              : this.development.imgUrl;
          this.$refs.previewRef.sendName = this.scene.sceneName;
          console.log("asdfasdfa" + this.$refs.previewRef.imgUrl);
          this.$refs.previewRef.solutions = {
            phone: "137xxxxxxxx",
            number: parseInt(this.development.number), //全国案例数
            achieveNumber: parseInt(this.development.achieveNumber), //可支撑人数,
            qxDevelopmentCityList: this.developmentCityList,
            qxLinkList: this.qxLinkList,
            demoVos: this.qxProductList,
            qxCaseVoList: this.qxCaseList,
            qxServiceList: [
              ...this.selectDeviceTableList,
              ...this.selectSoftwareTableList,
              ...this.selectIntegrateTableList,
              ...this.selectOtherTableList,
            ],
          };
        }
      });
    },
  },
};
</script>

<style>
</style>