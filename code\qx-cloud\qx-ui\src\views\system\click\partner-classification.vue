<template>
    <div class="app-container">
        <div class="app-conter-box">
            <div>
                <div class="classftion-right-big-tltle mt20">
                    <!-- <img src="@/assets/images/index/biaoti.png" alt="" /> -->
                    行业生态链点击次数和时长
                    <div class="date">
                        <el-date-picker v-model="dayDate" @change="getIndustryData()" style="width:300px"
                            format="yyyy/MM/dd" prefix-icon="" :clearable="false" ref="elDatePickControl"
                            type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"
                            :picker-options="{
                                disabledDate: time => {
                                    const today = new Date();
                                    // 禁止选择今天之后的时间
                                    if (time.getTime() > today.getTime()) {
                                        return true;
                                    }
                                    // 禁止结束时间选择今天
                                    if (this.dayDate && this.dayDate.length === 2) {
                                        return time.getTime() === today.setHours(0, 0, 0, 0);
                                    }
                                    return false;
                                }
                            }"></el-date-picker>
                    </div>
                </div>
            </div>
            <div class="partner">
                <div class="partner-left">
                    <div class="partner-left-title" style="margin: 0;">行业列表</div>
                    <div class="partner-left-list">
                        <div class="partner-left-item" v-for="(item, index) in industryList" :key="index"
                            @click="selectItemClick(item.industryCode)"
                            :class="queryParams.industryCode == item.industryCode ? 'acitve' : ''">{{ item.industryName
                            }}</div>
                    </div>
                </div>
                <div class="partner-right">
                    <div class="title">
                        <div :class="!titleActive ? 'btn' : 'btn active'" @click="titleActiveClick()" >{{ isSecondaryAdministrator? this.region:'总部' }}</div>
                        <div :class="titleActive ? 'btn' : 'btn active'" @click="titleActiveClick()" v-if="!isSecondaryAdministrator">省公司</div>
                        <div class=" btn btn1"></div>
                        <!-- <div class="date">
                        <el-date-picker
                            v-model="dayDate"
                            @change="getCaseMainList()"
                            style="width:280px"
                            format="yyyy/MM/dd"
                            prefix-icon=""
                            :clearable="false"
                            ref="elDatePickControl2"
                            type="daterange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :picker-options="{
                                disabledDate: time => {
                                    return time.getTime() > Date.now()
                                }
                            }"
                        ></el-date-picker>
                    </div> -->
                    </div>

                    <div class="list-title">
                        <div style="flex: 2;">{{ titleActive ? '行业生态链' : '省份行业生态链' }}</div>
                        <div style="">平均值（秒）</div>
                        <div style="">点击（次）</div>
                    </div>

                    <div class="partner-right-list">
                        <div v-for="(item, index) in caseMainList" :key="index">
                            <div
                                :style="item.parent != null ? 'font-size:16px;font-weight:bold;flex:2;display:flex;align-items:center;justify-content:center;' : 'flex:2;display:flex;align-items:center;justify-content:center;'">
                                <div v-if="!titleActive && item.clickCount > 0 && item.parent == null"
                                    :class="item.mainType == 1 ? 'item-img-close' : 'item-img-open'"
                                    @click="caseMainOpen(index, item)"></div>
                                <div v-if="!titleActive && (item.clickCount == 0 || item.parent != null)"
                                    class="item-img-div"></div>
                                <div>{{ item.companyName }}</div>
                                <div v-if="item.parent != null">{{ item.sceneName }}</div>
                            </div>
                            <div :style="item.parent != null ? 'font-weight:bold;' : ''">{{ item.timeLength == null ?
                                '0.00' : item.timeLength.toFixed(2) }}</div>
                            <div :style="item.parent != null ? 'font-weight:bold;' : ''">{{ item.timeLength == null ?
                                '0' : item.clickCount }}</div>
                        </div>
                        <div v-if="caseMainList.length == 0">
                            <!-- <div style="text-align: left;padding-left: 10px;"></div> -->
                            <div style="">没有数据</div>
                            <!-- <div style="padding-left: 20px"></div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getindustrylist, getuserlist, getdetailuser, getregionuser,provincialSecondaryAdministrator } from '@/api/system/sum.js'
import { setScale } from '@/utils/setScale'
export default {
    name: 'Config',
    data() {
        return {
            titleActive: true,
            dayDate: [],
            list: [{ name: '冶金' }, { name: '电力' }, { name: '教育' }, { name: '工厂' }, { name: '矿山(井工矿)' }, { name: '矿山(露天矿矿)' }],
            selectItem: '',
            industryList: [],
            caseMainList: [],
            queryParams: {
                region: '总部'
            },
            isSecondaryAdministrator: false,
            region: '',
        }
    },

    created() {
        // const end = new Date()
        // const start = new Date()
        // start.setTime(start.getTime() - 3600 * 1000 * 24 * 60)
        // this.dayDate = [start, end]
        setScale()
        window.onresize = () => {
            setScale()
        }
        this.dayDate = this.$route.query.dayDate.split(',')
        this.getIndustryData()
        this.myProvincialSecondaryAdministrator();
    },
    methods: {
        myProvincialSecondaryAdministrator(){
            provincialSecondaryAdministrator().then(res=>{
            this.isSecondaryAdministrator = res.data.isSecondaryAdministrator;
            this.region = res.data.region;
            if(this.isSecondaryAdministrator && res.data.region){
                this.queryParams.region = res.data.region;
            }
        });
        },
        // 跳转传参数 开始 结束时间
        handleMoreClick(url) {
            this.$router.push("indexDetail/" + url + "?dayDate=" + this.dayDate)

            console.log(url)
        },
        //点击展开
        caseMainOpen(index, item) {
            let that = this
            if (item.mainType == null || item.mainType == 0) {
                item.mainType = 1
                // if (that.dayDate.length > 0) {
                //     that.queryParams.startTime = this.parseTime(this.dayDate[0], '{y}/{m}/{d}')
                //     that.queryParams.endTime = this.parseTime(this.dayDate[1], '{y}/{m}/{d}')
                // }
                that.queryParams.region = item.companyName
                getdetailuser(that.queryParams).then(res => {
                    for (let i = 0; i < res.data.length; i++) {
                        res.data[i].parent = item.companyName
                        that.caseMainList.splice(index + i + 1, 0, res.data[i])
                    }
                })
            } else {
                item.mainType = 0
                for (let i = index + 1; i < that.caseMainList.length; i++) {
                    let row = that.caseMainList[i]
                    if (row.parent === item.companyName) {
                        that.caseMainList.splice(i, 1)
                        i--
                    } else break
                }
            }
        },

        titleActiveClick() {
            this.caseMainList = []
            this.titleActive = !this.titleActive
            if (this.titleActive) {
                this.queryParams.region = '总部'
            }
            if(this.isSecondaryAdministrator && this.region){
                this.queryParams.region = this.region;
            }
            this.getCaseMainList()
        },
        selectItemClick(code) {
            this.queryParams.industryCode = code
            this.getCaseMainList()
        },

        getCaseMainList() {
            let that = this
            if (that.dayDate.length > 0) {
                that.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                that.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            if(this.isSecondaryAdministrator && this.region){
                this.queryParams.region = this.region;
            }
            if (this.titleActive) {
                getuserlist(that.queryParams).then(res => {
                    this.caseMainList = res.data
                })
            } else {
                getregionuser(that.queryParams).then(res => {
                    this.caseMainList = res.data
                })
            }
        },

        getIndustryData() {
            let queryParams = {}
            queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
            queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            getindustrylist().then(res => {
                this.industryList = res.data
                if (this.industryList.length > 0) {
                    this.queryParams.industryCode = this.industryList[0].industryCode
                    this.getCaseMainList()
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    width: 100%;
    font-family: '思源黑体';
    background: #f0f2f5;
    min-height: calc(100vh - 84px);

    .app-conter-box {
        width: 1200px;
        margin: auto;
        overflow: hidden;
    }

    .classftion-right-big-tltle {
        margin: 30px 0;
        font-size: 24px;
        font-weight: 900;
        color: #0066e4;
        display: flex;
        align-items: center;
        height: 60px;
        border-radius: 10px;
        justify-content: space-between;
        padding: 0 24px;
        box-sizing: border-box;
        background: #ffffff;

        .date {
            // flex: 1;
            display: flex;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
            position: relative;
            padding-right: 10px;
            align-items: center;
            cursor: pointer;

            .onlowdate {
                width: 72px;
                height: 36px;
                border-radius: 10px;
                opacity: 1;
                border: 1px solid #3384e9;
                text-align: center;
                line-height: 36px;
                background: #ffffff;
                margin-right: 20px;
            }
        }
    }

    .partner {
        padding: 16px;
        height: 700px;
        border-radius: 10px;
        opacity: 1;
        background: #ffffff;
        display: flex;
        box-sizing: border-box;

        .partner-left {
            width: 160px;
            margin-right: 20px;

            .partner-left-title {
                font-size: 20px;
                font-weight: 700;
                text-align: center;
                margin-bottom: 20px;
                // width: 176px;
                // height: 35px;
                // background: url(../../../assets/images/click/partner-left-title.png) no-repeat center center;
                // background-size: 100%;
                // text-align: center;
                // margin-bottom: 20px;
                // margin-top: 25px;
            }

            .partner-left-list {
                overflow-y: auto;
                overflow-x: hidden;
                height: 620px;
                padding-right: 10px;
                box-sizing: border-box;
            }

            .partner-left-item {
                cursor: pointer;
                width: 148px;
                height: 40px;
                border-radius: 4px;
                opacity: 1;
                font-size: 18px;
                font-weight: 700;
                // border: 1px solid #acc1d0;
                background: #eaeff8;
                color: #005cdc;
                text-align: center;
                line-height: 40px;
                margin-bottom: 10px;
            }

            .acitve {
                background: linear-gradient(180deg, #68befa 0%, #003eff 100%);
                color: #fff;
            }
        }

        .partner-right {
            text-align: center;
            flex: 1;

            .title {
                height: 48px;
                display: flex;
                margin: 0;

                // width: 1437px;
                .btn {
                    width: 161px;
                    height: 48px;
                    line-height: 48px;
                    border-radius: 9px 9px 0 0;
                    font-size: 20px;
                    font-weight: 800;
                    color: #222222;
                    cursor: pointer;
                    border-bottom: 1px solid #cad4e6;
                }

                .active {
                    // background: linear-gradient(180deg, #31d6a9 0%, #0f75c3 100%);
                    color: #005cdc;
                    border: 1px solid #cad4e6;
                    border-bottom: none;
                }

                .btn1 {
                    flex: 1;
                }

                .date {}
            }

            .top {
                // width: 1437px;
                height: 7px;
                float: left;
                background: linear-gradient(90deg, #31d6a9 0%, #0f75c3 100%);
            }

            .list-title {
                border-left: 1px solid #cad4e6;
                border-right: 1px solid #cad4e6;
                height: 42px;
                // width: 487px;
                box-sizing: border-box;
                // padding: 0 3.5%;
                margin: auto;
                display: flex;

                >div {
                    flex: 1;
                    text-align: center;
                    font-size: 16px;
                    color: #666666;
                    font-weight: 400;
                    line-height: 42px;
                }
            }

            // .list-line {
            //     width: 100%;
            //     height: 2px;
            //     box-sizing: border-box;
            //     padding: 0px 3.5%;
            //     .img {
            //         width: 100%;
            //         height: 2px;
            //         background: url(../../../assets/images/click/maincase-line.png) no-repeat center center;
            //         background-size: 100%;
            //     }
            // }

            .partner-right-list {
                float: left;
                height: 576px;
                overflow-y: auto;
                width: 100%;
                // border: 1px solid #cad4e6;
                border-left: 1px solid #cad4e6;
                border-right: 1px solid #cad4e6;
                border-bottom: 1px solid #cad4e6;
                border-radius: 0 0 10px 10px;

                >div {
                    // height: 80px;
                    width: 100%;
                    // border-bottom: 1px solid #e6eff9;
                    margin: auto;
                    display: flex;

                    >div {
                        flex: 1;
                        text-align: center;
                        font-size: 16px;
                        color: #222;
                        font-weight: 800;
                        line-height: 42px;

                        .item-img-open {
                            height: 20px;
                            width: 20px;
                            margin-right: 10px;
                            background: url(../../../assets/images/index/maincase-open.png) no-repeat center center;
                            background-size: 100%;
                            cursor: pointer;
                        }

                        .item-img-div {
                            height: 20px;
                            width: 20px;
                            float: left;
                            margin-top: 30px;
                            margin-right: 10px;
                        }

                        .item-img-close {
                            height: 20px;
                            width: 20px;
                            margin-right: 10px;
                            background: url(../../../assets/images/index/maincase-close.png) no-repeat center center;
                            background-size: 100%;
                            cursor: pointer;
                        }
                    }
                }
            }
        }
    }
}

// ::v-deep .el-input__inner {
//     border: 0;
// }

// ::v-deep .el-range-editor--medium .el-range__icon,
// .el-range-editor--medium .el-range__close-icon {
//     display: none;
// }

// ::v-deep .el-date-editor .el-range-separator {
//     padding: 0;
// }

// ::v-deep .el-date-editor .el-range__close-icon {
//     display: none;
// }
// ::v-deep .el-date-editor .el-range-input {
//     width: 48%;
//     font-size: 20px;
//     height: 38px;
//     line-height: 38px;
//     font-weight: 800;
//     color: #1366bf;
//     font-family: MiSans-Heavy, MiSans;
// }

// ::v-deep .el-range-separator {
//     font-size: 20px;
//     height: 38px;
//     line-height: 38px;
//     font-weight: 800;
//     color: #1366bf;
//     font-family: MiSans-Heavy, MiSans;
// }

// ::v-deep .el-date-editor > input:-moz-placeholder {
//     color: #1366bf;
// }

// ::v-deep .el-date-editor > input:-ms-input-placeholder {
//     color: #1366bf;
// }

// ::v-deep .el-date-editor > input::-webkit-input-placeholder {
//     color: #1366bf;
// }

/*修改滚动条样式*/
div::-webkit-scrollbar {
    width: 6px;
    height: 10px;
    /**/
}

div::-webkit-scrollbar-track {
    background: rgb(239, 239, 239);
    border-radius: 2px;
}

div::-webkit-scrollbar-thumb {
    background: #2269de;
    border-radius: 10px;
}

div::-webkit-scrollbar-thumb:hover {
    background: #2269de;
}

div::-webkit-scrollbar-corner {
    background: #2269de;
}
</style>
