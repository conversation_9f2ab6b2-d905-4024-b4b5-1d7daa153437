<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '95vw'
        },
        height: {
            type: String,
            default: '350px'
        },
        autoResize: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            chart: null
        }
    },
    mounted() {},
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart(nameData, valueData, timeData) {
            this.chart = echarts.init(this.$el, 'macarons')
            this.chart.setOption({
                xAxis: {
                    data: nameData,
                    boundaryGap: false,
                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#333333'
                        }
                    }
                },
                grid: {
                    left: 50,
                    right: 150,
                    bottom: 20,
                    top: 30,
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                    formatter: function(params) {
                        var relVal = params[0].name
                        for (var i = 0, l = params.length; i < l; i++) {
                            if (i == 0) relVal += '<br/>' + params[i].marker + '点击次数:' + params[i].value + '  次数'
                            else relVal += '<br/>' + params[i].marker + '停留时长:' + params[i].value + '  秒'
                        }
                        return relVal
                    }
                },
                yAxis: {
                    minInterval: 1, //不允许出现小数位

                    axisTick: {
                        show: false
                    },
                    axisLine: {
                        show: false
                    },
                    axisLabel: {
                        color: '#D5D7D9'
                    }
                },
                legend: {
                    data: ['停留时长', '点击次数']
                },
                series: [
                    {
                        name: '点击次数',
                        itemStyle: {
                            normal: {
                                color: '#FF005A',
                                lineStyle: {
                                    color: '#FF005A',
                                    width: 2
                                }
                            }
                        },
                        smooth: true,
                        type: 'line',
                        data: valueData,
                        animationDuration: 2800,
                        animationEasing: 'cubicInOut',
                        label: {
                            show: true,
                            position: 'top',
                            color: '#333'
                        }
                    },
                    {
                        name: '停留时长',
                        smooth: true,
                        type: 'line',
                        itemStyle: {
                            normal: {
                                color: '#3888fa',
                                lineStyle: {
                                    color: '#3888fa',
                                    width: 2
                                },
                                areaStyle: {
                                    color: '#f3f8ff'
                                }
                            }
                        },
                        data: timeData,
                        animationDuration: 2800,
                        animationEasing: 'quadraticOut',
                        label: {
                            show: true,
                            position: 'top',
                            color: '#333'
                        }
                    }
                ]
            })
        }
    }
}
</script>
