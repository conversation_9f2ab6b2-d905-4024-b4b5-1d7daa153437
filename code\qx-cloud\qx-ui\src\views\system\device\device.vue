<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业场景" prop="sceneId">
        <el-cascader
          v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear"> <!-- 监听清除事件 -->
        </el-cascader>
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input v-model="queryParams.deviceName" placeholder="请输入设备名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:device:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:device:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:device:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:device:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="id" align="center" prop="id" /> -->
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备实际图片" align="center" prop="deviceUrl">
        <template slot-scope="scope">
          <img style="width:50px;height:50px;" :src="scope.row.deviceUrl" />
        </template>
      </el-table-column>
      <el-table-column label="设备显示图片" align="center" prop="deviceShowurl">
        <template slot-scope="scope">
          <img style="width:50px;height:50px;" :src="scope.row.deviceShowurl" />
        </template>
      </el-table-column>
      <el-table-column label="设备单位" align="center" prop="deviceUnit" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:device:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:device:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" :close-on-click-modal="false" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" maxlength="50" show-word-limit placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备图片" prop="deviceUrl">
          <ImageUpload @input="setInput" :uploadStyle="'width:70px;height:70px;'" :value="form.deviceUrl" />
        </el-form-item>
        <el-form-item label="设备图片" prop="deviceShowurl">
          <ImageUpload @input="setShowInput" :uploadStyle="'width:70px;height:70px;'" :value="form.deviceShowurl" />
        </el-form-item>
        <el-form-item label="设备单位" prop="deviceUnit">
          <el-input v-model="form.deviceUnit" maxlength="20" show-word-limit placeholder="请输入设备单位" />
        </el-form-item>
        <el-form-item label="场景" prop="sceneId">
          <el-cascader v-model="form.sceneIds" :options="options" @change="handleChange"></el-cascader>
        </el-form-item>

        <!-- <div v-show="selectItem.sceneUrl&&form.deviceUrl" style="margin-bottom:5px;margin-left:80px;"> (高/宽/上边距/左边距,单位为实际图片宽度大小的百分比)</div>
        <el-form-item label="位置" v-for="(item,index) in devcieLocationList" :key="item.id" v-show="selectItem.sceneUrl&&form.deviceUrl">
          <el-input-number :min="0" :max="100" :controls="false" placeholder="高度" v-model="item.deviceHeight" style="width:60px;float:left;" />
          <el-input-number :min="0" :max="100" :controls="false" placeholder="宽度" v-model="item.deviceWidth" style="width:60px;float:left;margin-left:10px;" />
          <el-input-number :min="0" :max="100" :controls="false" placeholder="上边距" v-model="item.deviceTop" style="width:80px;float:left;margin-left:10px;" />
          <el-input-number :min="0" :max="100" :controls="false" placeholder="左边距" v-model="item.deviceLeft" style="width:80px;float:left;margin-left:10px;" />
           <el-input-number :min="0" :max="100" :controls="false" placeholder="字体上边距" v-model="item.fontTop" style="width:80px;float:left;margin-left:10px;" />
          <el-input-number :min="0" :max="100" :controls="false" placeholder="字体左边距" v-model="item.fontLeft" style="width:80px;float:left;margin-left:10px;" />
          <el-button style="margin-left:10px;float:left;" type="danger" v-show="index!=0" @click="deleteItemRow(index)">删除</el-button>
        </el-form-item>
        <el-button style="margin-bottom:5px;margin-left:250px;" type="primary" @click="addItemRow">添加</el-button>
        <el-form-item label-width="35px">
          <div style="width:30vw;height:17.1156vw; float:left;" v-show="selectItem.sceneUrl">
            <img v-for="(item) in devcieLocationList" :key="item.id" v-show="item.deviceHeight&&item.deviceWidth&&item.deviceTop&&item.deviceLeft" :style="'position: absolute;z-index:998;width:'+item.deviceWidth*0.3+'vw;height:'+item.deviceHeight*0.3+'vw;margin-top:'+item.deviceTop*0.3+'vw;margin-left:'+item.deviceLeft*0.3+'vw;'" :src="form.deviceUrl" />
            <div v-for="(item,index) in devcieLocationList" :key="index" v-show="item.fontTop&&item.fontLeft"
            :style="'position: absolute;z-index:999;-webkit-transform : scale(0.3,0.3);transform-origin: 0 0;font-size:0.9vw;padding-left:0.8vw;padding-right:0.8vw;background-color:rgba(0,0,0,0.8);color:#fff;height:2vw;line-height:2vw;'+'margin-top:'+item.fontTop*0.3+'vw;margin-left:'+item.fontLeft*0.3+'vw;'">
            {{form.deviceName}}</div>
            <div style="width:30vw;height:17.1156vw; position: absolute;z-index:997;background-color:rgba(0,0,0,0.5);"></div>
            <img style="width:30vw;height:17.1156vw; position: absolute;z-index:996;" :src="selectItem.sceneUrl" />
          </div>
        </el-form-item> -->
      </el-form>
      <!-- <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
      </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDevice, getDevice, delDevice, addDevice, updateDevice, exportDevice } from "@/api/system/device";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";
import ImageUpload from "../../../components/ImageUpload";
// import { getCodeImg } from "@/api/login";

export default {
  name: "Device",
  components: {
    ImageUpload
  },
  data() {
    return {
      devcieLocationList: [{}],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceName: null,
        deviceUrl: null,
        deviceUnit: null,
        sceneId: null
      },
      // 表单参数
      form: {
        code: "",
        uuid: ""
      },
      // 表单校验
      rules: {
        deviceName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
          // { validator: this.validateDeviceName, trigger: 'blur' }
        ],
        deviceUrl: [
          { required: true, message: "设备图片不能为空" }
        ],
        deviceUnit: [
          { required: true, message: "设备单位不能为空", trigger: "blur" },
          // { validator: this.validateDeviceUnit, trigger: 'blur' }
        ],
        sceneId: [
          { required: true, message: "场景不能为空 ", trigger: "change" }
        ],
      },
      //行业列表
      industryList: [],
      //场景列表
      sceneList: [],
      options: [],
      selectItem: {},//选中的item
    };
  },
  created() {
    this.getList();
    listIndustry().then(response => {
      this.industryList = response.rows;
      listScene().then(response => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });
  },
  methods: {
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    getStringLength(str) {
      let length = 0;
      for (let i = 0; i < str.length; i++) {
        const charCode = str.charCodeAt(i);
        if (charCode >= 0x4e00 && charCode <= 0x9fa5) {
          // 中文字符范围
          length += 2;
        } else {
          length += 1;
        }
      }
      return length;
    },

    // 验证设备名称
    validateDeviceName(rule, value, callback) {
      const length = this.getStringLength(value);
      if (length > 50) {
        callback(new Error('设备名称不能超过50个字符'));
      } else {
        callback();
      }
    },

    // 验证设备单位
    validateDeviceUnit(rule, value, callback) {
      const length = this.getStringLength(value);
      if (length > 20) {
        callback(new Error('设备单位不能超过20个字符'));
      } else {
        callback();
      }
    },
    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.queryParams.industryId = value[0];
      };

      if (value != null || value.length > 1) {
        this.queryParams.sceneId = value[1];
      };

    },
    //添加行
    addItemRow() {
      this.devcieLocationList.push({});
    },
    //删除行
    deleteItemRow(index) {
      this.devcieLocationList.splice(index, 1);
    },
    //选中
    handleChange(value) {
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach(row => {
        if (row.id === value[1]) {
          this.selectItem = row;
        }
      });
    },
    setInput(value) {
      this.$set(this.form, "deviceUrl", value);
    },
    setShowInput(value) {
      this.$set(this.form, "deviceShowurl", value);
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach(element => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: []
        }
        this.sceneList.forEach(row => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName
            });
          }
        });
        this.options.push(item);
      });
      console.log(this.options);
    },
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deviceName: null,
        deviceUrl: null,
        deviceUnit: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null,
        sceneId: null,
        sceneIds: null,
        qxDeviceLocationList: [{}]
      };
      this.devcieLocationList = [{}];
      this.selectItem = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.sceneIds = [];
      this.queryParams.industryId = null;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备";
      // this.getCode();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let that = this;
      this.reset();
      const id = row.id || this.ids
      getDevice(id).then(response => {
        that.form = response.data;
        that.devcieLocationList = that.form.qxDeviceLocationList;
        that.sceneList.forEach(row => {
          if (row.id === that.form.sceneId) {
            that.selectItem = row;
            that.form.sceneIds = [row.industryId, row.id];
            console.log(that.form.sceneIds);
          }
        });
        this.open = true;
        this.title = "修改设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.form.qxDeviceLocationList = this.devcieLocationList;

      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDevice(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.deviceList
        .filter(item => ids.includes(item.id))
        .map(item => item.deviceName)
        .join(', ');
      this.$confirm('是否确认删除设备名称为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delDevice(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的设备数据';
      }else {
        msg = '是否确认导出所有的设备数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/device/export', exportParams, `device_${new Date().getTime()}.xlsx`)
      }).catch({});
    },
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
        this.loginForm.publicKey = res.publicKey;
      });
    },
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload--picture-card {
  width: 80px !important;
  height: 80px !important;
  line-height: 80px;
}
::v-deep .image-slot {
  box-sizing: border-box;
  padding-top: 5px;
}
</style>
