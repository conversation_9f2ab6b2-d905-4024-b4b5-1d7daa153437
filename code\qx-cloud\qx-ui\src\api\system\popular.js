import request from '@/utils/request'

// 查询热门词列表
export function listPopular(query) {
  return request({
    url: '/system/home/<USER>/manage/list',
    method: 'get',
    params: query
  })
}

// 查询热门词详细
export function getPopular(id) {
  return request({
    url: '/system/home/<USER>/manage/' + id,
    method: 'get'
  })
}

// 新增热门词
export function addPopular(data) {
  return request({
    url: '/system/home/<USER>/manage/add',
    method: 'post',
    data: data
  })
}

// 修改热门词
export function updatePopular(data) {
  return request({
    url: '/system/home/<USER>/manage/upd',
    method: 'post',
    data: data
  })
}

// 删除热门词
export function delPopular(ids) {
  return request({
    url: '/system/home/<USER>/manage/del',
    method: 'post',
    data: { ids: ids }
  })
}
