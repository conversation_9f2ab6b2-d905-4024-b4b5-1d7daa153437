import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/auth/adminlogin',
    method: 'post',
    data: data,
    timeout: 1000*60*2
  })
}

//使用token登录
export function tokenlogin(token) {
  return request({
    url: '/auth/tokenlogin',
    method: 'post',
    data: token,
    timeout: 1000*60*2
  })
}

export function verifycode(username, code) {
  const data = {
    username,
    code
  }
  return request({
    url: '/auth/verifycode',
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

// 获取用户详细信息
export function isUpdPwd() {
  return request({
    url: '/system/isupdpwd',
    method: 'get'
  })
}

// 获取用户详细信息
export function updatePwd(data) {
  return request({
    url: '/system/updatepwd',
    method: 'post',
    data: data
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'delete'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/code',
    method: 'get'
  })
}
export function getCodeImgTo() {
  return request({
    url: '/captchaImageTo',
    method: 'get'
  })
}

// 获取验证码
export function getPwdCode(data) {
  return request({
    url: '/system/getpwdcode',
    method: 'post',
    data: data
  })
}


// 找回密码修改密码
export function updPwd(data) {
  return request({
    url: '/system/updpwd',
    method: 'post',
    data: data
  })
}
