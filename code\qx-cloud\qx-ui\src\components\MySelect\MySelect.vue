<template>
  <el-cascader :value="selectValue" :options="options" :props="props" @input="$emit('input',$event)" :placeholder="placeholder" @change="refreshStatus()" collapse-tags clearable></el-cascader>
</template>
<script>
export default {
  name: "MySelect",
  props: {

    options: {
      type: Array,
      required: true
    },
    // 绑定value是为了外面也可以传值改变到里面的值双向绑定
    value: {
      type: Array
    },
    placeholder: {
      type: String,
      default: "请选择"
    },
  },
  watch: {
    value: {
      handler(newName) {
        this.selectValue = newName;
      },
      deep: true,
    },
    selectValue: {
      handler(newName, oldName) {

        if (newName.length != oldName.length) {
          //全选勾选了
          if ((newName.length > 0 && newName[0][0] == -1) && (oldName.length == 0 || (oldName.length > 0 && !(oldName[0][0] == -1)))) {
            let all = [];
            this.options.forEach(province => {
              if (!province.children)
                all.push([province.value]);
              else {
                province.children.forEach(city => {
                  all.push([province.value, city.value]);
                });
              }
            });
            this.$emit("input", all);
          }
          //全选变为取消全选
          if (oldName.length == 341) {
            if (newName.length > 0 && !(newName[0][0] == -1)) {
              this.$emit("input", []);
            }
            if (newName.length > 0 && newName[0][0] == -1) {
              newName.splice(0, 1);
              let all = [];
              let num = 0;
              newName.forEach(row => {
                if (num != 0)
                  all.push([row[0], row[1]]);
                num++;
              })
              let row = newName[0]
              all.push([row[0], row[1]]);
              this.$emit("input", all);
            }
          }
          //所有城市选择了，全国未选，还是设置全选
          if (newName.length == 340 && !(newName[0][0] == -1) && (oldName.length == 0 || oldName.length > 0 && !(oldName[0][0] == -1))) {
            let all = [];
            this.options.forEach(province => {
              if (!province.children)
                all.push([province.value]);
              else {
                province.children.forEach(city => {
                  all.push([province.value, city.value]);
                });
              }
            });
            this.$emit("input", all);
          }

        }
        this.$nextTick(() => {
          this.$parent.$forceUpdate();
        });
      },
    }
  },
  data() {
    return {
      selectValue: [], props: { multiple: true }
    };
  },
  methods: {
    refreshStatus() {
      this.$parent.$forceUpdate();
    },
  }
};
</script>
<style lang="scss" scoped>
</style>