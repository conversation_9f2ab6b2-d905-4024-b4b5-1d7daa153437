const IframeMessage = {
    /**
     *  向主页面传递参数
     * @param {String} message
     */
    emitParentMessage(message) {
      window.parent.postMessage({ message:message }, '*')
    },

    /**
     *  向子页面传递参数
     * @param {String} iframeId  iframe id
     * @param {String} message   消息
     * @param {String} src       子页面请求
     */
    emitChildrenMessage(iframeId="iframe", message, src) {
       document.querySelector(iframeId).contentWindow.postMessage(message, src);
    },
    /**
     *  监听获取参数
     * @returns {callback}
     */
    onMessage() {
      return new Promise(resolve => {
        window.addEventListener(
          'message',
          function(e) {
            if (e.data) {
              resolve(e.data)
            }
          },
          false
        )
      })
    },

    /**
    * 移除
    * @param {iframeId}
    *
    **/
    removeEventListener(iframeId){
        document.querySelector(iframeId).removeEventListener('message',IframeMessage.onMessage);
    },
    /**
     *  判断 是否被 ifame 嵌套
     *  true 表示未被嵌套， false 表示已被嵌套
     * @returns {boolean}
     */
    isWithinOrOuter() {
      return window.self === window.top
    },
    /**
     *  获取token
     * @returns {string}
     */
     async getToken() {
       IframeMessage.emitParentMessage("收到消息")
       const token = await IframeMessage.onMessage().then(res => {
         return res
       })
       return token
     }

}

export default IframeMessage;
