import request from '@/utils/request'

// 查询定制开发内容列表
export function listService(query) {
  return request({
    url: '/system/service/list',
    method: 'get',
    params: query
  })
}

// 查询定制开发内容详细
export function getService(id) {
  return request({
    url: '/system/service/' + id,
    method: 'get'
  })
}

// 新增定制开发内容
export function addService(data) {
  return request({
    url: '/system/service',
    method: 'post',
    data: data
  })
}

// 修改定制开发内容
export function updateService(data) {
  return request({
    url: '/system/service',
    method: 'put',
    data: data
  })
}

// 删除定制开发内容
export function delService(id) {
  return request({
    url: '/system/service/' + id,
    method: 'delete'
  })
}

// 导出定制开发内容
export function exportService(query) {
  return request({
    url: '/system/service/export',
    method: 'get',
    params: query
  })
}