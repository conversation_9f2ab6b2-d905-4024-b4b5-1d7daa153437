<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业名称" prop="industryName">
        <el-input v-model="queryParams.industryName" placeholder="请输入行业名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="行业编码" prop="industryCode">
        <el-input v-model="queryParams.industryCode" placeholder="请输入行业编码" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="管理邮箱" prop="industryCode">
        <el-input v-model="queryParams.industryEmail" placeholder="请输入管理邮箱" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:industry:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:industry:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:industry:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:industry:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="industryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column label="id" align="center" prop="id" />-->
<!--      <el-table-column label="板块" align="center" prop="plateName"/>-->
      <el-table-column label="行业名称" align="center" prop="industryName" />
      <el-table-column label="行业编码" align="center" prop="industryCode" />
      <el-table-column label="行业说明" align="center" prop="industryExplain">
        <template #default="scope">
          <el-tooltip :content="scope.row.industryExplain" raw-content placement="top-start" v-if="scope.row.industryExplain">
            <span v-if="scope.row.industryExplain && scope.row.industryExplain.length <= 30">
              {{ scope.row.industryExplain }}
            </span>
            <span v-if="scope.row.industryExplain && scope.row.industryExplain.length > 30">
              {{ scope.row.industryExplain.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.industryExplain== null"> </span>
        </template>
      </el-table-column>
<!--      <el-table-column label="上线时间" align="center" prop="industryTime" />-->
<!--      <el-table-column label="上线天数" align="center" prop="timeNumber" />-->

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:industry:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:industry:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改行业对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="板块" prop="plate">
          <el-select style="width:380px" v-model="form.plate" placeholder="请选择板块">
            <el-option v-for="item in plateList"
                       :key="item.dictValue"
                       :label="item.dictLabel"
                       :value="item.dictValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="行业名称" prop="industryName">
          <el-input v-model="form.industryName" placeholder="请输入行业名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="行业编码" prop="industryCode">
          <el-input v-model="form.industryCode" placeholder="请输入行业编码" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="行业说明" prop="industryExplain">
          <el-input type="textarea" :rows="3" v-model="form.industryExplain" placeholder="请输入行业说明" maxlength="200" show-word-limit />
        </el-form-item>
<!--        <el-form-item label="上线时间" prop="industryTime">-->
<!--          <el-date-picker v-model="form.industryTime" type="date" style="width:100%" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="上线天数" prop="timeNumber">-->
<!--          <el-input v-model="form.timeNumber" placeholder="请输入上线天数" maxlength="5" show-word-limit />-->
<!--        </el-form-item>-->
        <el-form-item label="EOS路径" prop="eosPath">
          <el-input v-model="form.eosPath" placeholder="请输入EOS路径" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="行业生态链一级审批" prop="topExamine" label-width="150px">
          <el-select style="width:410px" v-model="form.topExamine" collapse-tags multiple placeholder="请选择一级审批" @change="setUserList()">
            <el-option v-for="item in userList" :key="item.id" :label="item.nickname + '(' + item.account +')'" :value="item.id" :disabled="item.topDisabled">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="行业生态链二级审批" prop="twoExamine" label-width="150px">
          <el-select style="width:410px" v-model="form.twoExamine" collapse-tags multiple placeholder="请选择二级审批" @change="setUserList()">
            <el-option v-for="item in userList" :key="item.id" :label="item.nickname + '(' + item.account +')'" :value="item.id" :disabled="item.twoDisabled">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listIndustry,
  listIndustryV2,
  getIndustry,
  delIndustry,
  addIndustry,
  updateIndustry,
  exportIndustry,
  updateUserIndustry,
} from "@/api/system/industry";
import { examinelistUser,getDict} from "@/api/system/qxuser"
import Template from "@/views/system/template";
export default {
  name: "Industry",
  components: {
    Template
  },
  data() {
    // 添加自定义校验方法
    const validateTopExamine = (rule, value, callback) => {
      if (!this.form.topExamine || this.form.topExamine.length === 0) {
        callback(new Error('一级审批人不能为空'));
      } else {
        callback();
      }
    };

    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 行业表格数据
      industryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industryName: null,
        industryExplain: null,
        industryCode: null,
        industryEmail: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industryName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
          { max: 50, message: "名称不能超过50个字符", trigger: "blur" },
        ],
        industryCode: [
          { required: true, message: "编码不能为空", trigger: "blur" },
          { max: 50, message: "编码不能超过50个字符", trigger: "blur" },
        ],
        industryExplain: [
          { max: 200, message: "行业说明不能超过200个字符", trigger: "blur" },
        ],
        plate: [
          { required: true, message: "板块未必填", trigger: "blur" },
        ],
        topExamine: [
          { required: true, validator: validateTopExamine, trigger: 'change' }
        ],
      },
      userList: [], //用户列表
    };
  },
  created() {
    this.getList();
    examinelistUser({ accountType: 1 }).then(response => {
      this.userList = response.data;
      console.log(this.userList);
    })
    getDict( {
      pageNum : 1,
      pageSize : 999,
      dictType : 'plate_type'
    }).then(response => {
      this.plateList = response.rows;
    })
  },
  methods: {
    setUserList() {
      let _this = this;
      _this.userList.forEach((item) => {
        item.twoDisabled = false;
        _this.form.topExamine.forEach((top) => {
          if (item.id == top) {
            item.twoDisabled = true;
          }
        });

        item.topDisabled = false;
        _this.form.twoExamine.forEach((top) => {
          if (item.id == top) {
            item.topDisabled = true;
          }
        });
      });

      // 手动触发验证
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.validateField('topExamine');
      });

      this.$forceUpdate();
    },
    /** 查询行业列表 */
    getList() {
      this.loading = true;
      listIndustryV2(this.queryParams).then((response) => {
        this.industryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        industryName: null,
        industryCode: null,
        industryExplain: null,
        industryEmail: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        password: null,
        plate: null,
        topExamine: [],
        twoExamine: [],
        qxIndustryUserVoList: [],
      };
      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加行业";
      examinelistUser({ accountType: 1 }).then((response) => {
        this.userList = response.data;
      });
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      examinelistUser({ accountType: 1 }).then((response) => {
        this.userList = response.data;
        getIndustry(id).then((response) => {
          this.form = response.data;
          this.open = true;
          this.title = "修改行业";
          this.form.topExamine = [];
          this.form.twoExamine = [];
          this.form.qxIndustryUserVoList.forEach((item) => {
            if (item.examineType == 1) {
              this.form.topExamine.push(item.userId);
            } else this.form.twoExamine.push(item.userId);
          });
          this.setUserList();
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (
        this.form.timeNumber != "" &&
        this.form.timeNumber != null &&
        !/^[1-9]\d*$/.test(this.form.timeNumber)
      ) {
        this.msgError("上线天数只能为正整数");
        return;
      }

     //this.$refs.form.validateField("topExamine");
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 额外验证一级审批
          if (!this.form.topExamine || this.form.topExamine.length === 0) {
            this.$message.error("一级审批人不能为空");
            return;
          }

          this.form.qxIndustryUserVoList = [];
          this.form.topExamine.forEach((element) => {
            this.form.qxIndustryUserVoList.push({
              userId: element,
              industryId: this.form.id,
              examineType: 1,
            });
          });
          this.form.twoExamine.forEach((element) => {
            this.form.qxIndustryUserVoList.push({
              userId: element,
              industryId: this.form.id,
              examineType: 2,
            });
          });
          if (this.form.id != null) {
            updateIndustry(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIndustry(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.industryList
        .filter((item) => ids.includes(item.id))
        .map((item) => item.industryCode)
        .join(", ");

      this.$confirm('是否确认删除行业编号为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delIndustry(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },

    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null, // Add ids if any are selected
      };
      let msg = "";
      if (that.ids.length > 0) {
        msg = "是否确认导出所筛选或选中的行业数据";
      } else {
        msg = "是否确认导出所有的行业数据";
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          that.download(
            "system/industry/export",
            exportParams,
            `industry_${new Date().getTime()}.xlsx`
          );
        })
        .catch({});
    },
  },
};
</script>
