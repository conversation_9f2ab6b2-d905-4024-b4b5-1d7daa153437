import request from '@/utils/request'

// 查询ai基础数据列表
export function listCase(query) {
  return request({
    url: '/system/case/list',
    method: 'get',
    params: query
  })
}

// 查询ai基础数据详细
export function getCase(id) {
  return request({
    url: '/system/case/' + id,
    method: 'get'
  })
}

// 新增ai基础数据
export function addCase(data) {
  return request({
    url: '/system/case',
    method: 'post',
    data: data
  })
}

// 修改ai基础数据
export function updateCase(data) {
  return request({
    url: '/system/case',
    method: 'put',
    data: data
  })
}

// 删除ai基础数据
export function delCase(id) {
  return request({
    url: '/system/case/' + id,
    method: 'delete'
  })
}
