<template>
  <div class="forget-index">
    <div class="forget-label" @click=" forgetPwd()">忘记密码 </div>
    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="pwdtitle" :visible.sync="open" width="550px" @close="cancel" append-to-body :close-on-click-modal="false">
      <el-form ref="codeform" v-show="!isGetCode" :model="codeform" :rules="coderules" label-width="120px">
        <el-form-item label="邮箱" prop="username">
          <el-input v-model="codeform.username" placeholder="请输入邮箱" :disabled="isGetCode" maxlength="200" />
        </el-form-item>
        <el-form-item label="姓名" prop="nikename">
          <el-input v-model="codeform.nikename" placeholder="请输入姓名" :disabled="isGetCode" maxlength="200" />
        </el-form-item>
        <el-form-item label="图形验证码" prop="imgcode">
          <el-input v-model="codeform.imgcode" auto-complete="off" placeholder="图形验证码" style="width: 63%">
          </el-input>
          <div class="login-code">
            <img :src="codeUrl" @click="getImgCode" class="login-code-img" />
          </div>
        </el-form-item>
      </el-form>

      <el-form v-show="isGetCode" ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="邮箱">
          <span>{{form.username}}</span>
        </el-form-item>
        <el-form-item label="验证码" prop="code">
          <el-input v-model="form.code" style="width: 63%" placeholder="请输入验证码" maxlength="6" />
          <!-- <el-button style="margin-left:15px" type="primary" :disabled="disabled" @click="getCode">{{btnStr}}</el-button> -->
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" maxlength="50" />
        </el-form-item>
        <el-form-item label="再次输入密码" prop="towPassword">
          <el-input v-model="form.towPassword" placeholder="请再次输入密码" type="password" maxlength="50" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" v-show="!isGetCode" @click="getCode">获取邮箱验证码</el-button>
        <el-button type="primary" v-show="isGetCode" @click="submitForm">确定修改密码</el-button>
        <el-button type="primary" v-show="isGetCode" @click="topCode">上一步</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getCodeImg } from "@/api/login";
import { getPwdCode, updPwd } from "@/api/login";
import md5 from 'md5';
export default {
  name: "MySelect",
  data() {
    var checkCode = (rule, value, callback) => {
      if (this.isGetCode) {
        if (this.form.code == null || this.form.code == "")
          return callback(new Error("验证码不能为空"));
        else if (this.form.code.length < 6)
          return callback(new Error("验证码不正确"));
      } else {
        return callback(new Error("未获取验证码"));
      }
      return callback();
    };
    var checkPassword = (rule, value, callback) => {
      if (this.isGetCode) {
        var reg = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]{8,32}$/;
        if (this.form.password == null || this.form.password == "")
          return callback(new Error("密码不能为空"));
        else if (!reg.test(this.form.password))
          return callback(new Error("密码不符合复杂度要求。应至少包含大写字母、小写字母、数字或特殊字符的其中三种，密码长度8-32位"));
      }
      return callback();
    };

    var checkTowPassword = (rule, value, callback) => {
      if (this.isGetCode) {
        if (this.form.towPassword == null || this.form.towPassword == "")
          return callback(new Error("再次密码不能为空"));
        else if (this.form.password != this.form.towPassword)
          return callback(new Error("两次密码不一致！"));
      }
      return callback();
    };
    return {
      pwdtitle: "忘记密码-获取验证码",
      codeUrl: null,
      open: false,
      disabled: false,
      isGetCode: false,
      btnStr: "获取验证码",
      timer: null,
      timerNum: 120,
      codeform: {
        username: null,
        nikename: null,
        imgcode: null,
        uuid: null,
      },
      form: {
        username: null,
        nikename: null,
        code: null,
        imgcode: null,
        password: null,
        towPassword: null,
        uuid: null,
      },
      coderules: {
        username: [
          { required: true, message: "邮箱不能为空", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        nikename: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        imgcode: [
          { required: true, message: "图形验证码不能为空", trigger: "blur" }
        ]
      },
      rules: {
        code: [
          { required: true, validator: checkCode, trigger: "blur" },
        ],
        password: [
          { required: true, validator: checkPassword, trigger: "blur" },
        ],
        towPassword: [
          { required: true, validator: checkTowPassword, trigger: "blur" },
        ],
      }
    };
  },
  methods: {
    //上一步
    topCode() {
      this.isGetCode = false;
      this.getImgCode();
      this.codeform.imgcode="";
    },
    //获取图形验证码
    getImgCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.codeform.uuid = res.uuid;
      });
    },
    forgetPwd() {
      this.open = true;
      this.disabled = false;
      this.isGetCode = false;
      this.btnStr = "获取验证码";
      this.pwdtitle = "忘记密码-获取验证码";
      this.getImgCode();
    },
    getCode() {
      let that = this;
      this.$refs["codeform"].validate(valid => {
        if (valid) {
          that.disabled = true;
          getPwdCode(this.codeform).then(res => {
            if (res.code === 200) {
              that.$message.success("获取验证码成功");
              that.pwdtitle = "忘记密码-重置密码";
              that.form.username = that.codeform.username;
              that.isGetCode = true;
            } else {
              that.getImgCode();
              that.$message.error(res.msg);
            }
          }).catch(() => {
            that.getImgCode();
          });
        }
      })
    },
    submitForm() {
      let that = this;
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.password = md5(this.form.password)
          updPwd(this.form).then(res => {
            if (res.code === 200) {
              that.$message.success("修改密码成功！");
              that.reset();
            }
          }).catch(() => {
            that.form.password = that.form.towPassword;
          });
        }
      });

    },
    reset() {
      let that = this;
      that.$refs.form.resetFields();
      that.$refs.codeform.resetFields();
      that.open = false;
      that.disabled = false;
      that.isGetCode = false;
      that.btnStr = "获取验证码";
    },
    cancel() {
      this.reset();
      this.open = false;
    }
  },

}

</script>

<style lang="scss" scoped>
.forget-index {
  width: 8vw;
  height: 14px;
  float: right;
}
.forget-label {
  text-align: right;
  font-family: FZLTXHK--GBK1-0, FZLTXHK--GBK1;
  cursor: pointer;
  width: 8vw;
  float: right;
  height: 14px;
  font-size: 14px;
  font-weight: normal;
  color: #0052d9;
  line-height: 14px;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.login-code-img {
  height: 38px;
  width: 110px;
}
</style>