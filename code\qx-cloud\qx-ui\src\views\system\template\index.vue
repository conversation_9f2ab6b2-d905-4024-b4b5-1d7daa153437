<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板key" prop="templateKey">
        <el-input v-model="queryParams.templateKey" placeholder="请输入模板key" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="模板名称" prop="templateName">
        <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="显示隐藏" prop="templateState">
        <el-select v-model="queryParams.templateState" collapse-tags size="small" placeholder="请选择显示隐藏">
          <el-option key="0" label="显示" value="0"></el-option>
          <el-option key="1" label="隐藏" value="1"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="templateList">
      <el-table-column label="缩略图" align="center" prop="templateUrl">
        <template slot-scope="scope">
          <img style="width:80px;height:45px;" :src="scope.row.templateUrl" />
        </template>
      </el-table-column>
      <el-table-column label="模板key" align="center" prop="templateKey" />
      <el-table-column label="模板名称" align="center" prop="templateName" />

      <el-table-column label="状态" align="center" prop="templateState">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.templateState" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script>
import { listTemplate, updateTemplate } from "@/api/system/template";

export default {
  name: "Template",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // E编排模板表格数据
      templateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        templateUrl: null,
        templateState: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询E编排模板列表 */
    getList() {
      this.loading = true;
      listTemplate(this.queryParams).then(response => {
        this.templateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 表单重置
    reset() {
      this.form = {
        templateKey: null,
        templateName: null,
        templateUrl: null,
        templateState: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    //修改状态
    handleStatusChange(row) {
      updateTemplate({ "templateKey": row.templateKey, "templateState": row.templateState }).then(response => {
        this.msgSuccess((row.templateState === 1 ? '隐藏' : '显示') + "成功");
        this.getList();
      });
    }



  }
};
</script>