<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="行业场景" prop="sceneIds">
        <el-cascader v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear"> <!-- 监听清除事件 -->
        </el-cascader>
      </el-form-item>

      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="内容" prop="valueContent">
        <el-input v-model="queryParams.valueContent" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['scene:estimate:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['scene:estimate:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['scene:estimate:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['scene:estimate:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="estimateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="成本预估内容" align="center" prop="valueContent" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{s}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['scene:estimate:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['scene:estimate:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改成本预估对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" show-word-limit maxlength="100" />
        </el-form-item>
        <el-form-item label="行业场景" prop="sceneIds">
          <el-cascader v-model="form.sceneIds" :options="options" @change="handleChange"></el-cascader>
        </el-form-item>
        <el-form-item label="成本预估内容"  prop="valueContent">
          <el-input v-model="form.valueContent" maxlength="2000" show-word-limit type="textarea" rows="10" placeholder="请输入成本预估内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listEstimate,
  getEstimate,
  delEstimate,
  addEstimate,
  updateEstimate,
} from "@/api/scene/estimate";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";

export default {
  name: "Estimate",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 成本预估表格数据
      estimateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        sceneCode: null,
        industryCode: null,
        valueContent: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sceneIds: [
          { required: true, message: "场景不能为空", trigger: "change" }
        ],
        title: [
          { required: true, message: "标题不能为空", trigger: "change" }
        ],
        valueContent: [
          { required: true, message: "成本预估内容不能为空", trigger: "blur" }
        ]
      },
      //行业列表
      industryList: [],
      //场景列表
      sceneList: [],
      options: [],
    };
  },
  created() {
    listIndustry().then((response) => {
      this.industryList = response.rows;
      listScene().then((response) => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });
    this.getList();
  },
  methods: {
    //选中
    handleChange(value) {
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach((row) => {
        if (row.id === value[1]) {
          this.form.sceneCode = row.sceneCode;
        }
      });

      this.industryList.forEach((row) => {
        if (row.id === value[0]) {
          this.form.industryCode = row.industryCode;
        }
      });
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach((element) => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: [],
        };
        this.sceneList.forEach((row) => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName,
            });
          }
        });
        this.options.push(item);
      });
    },
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.industryList.forEach((row) => {
          if (row.id === value[0]) {
            this.queryParams.industryCode = row.industryCode;
          }
        });
      }

      if (value != null || value.length > 1) {
        this.sceneList.forEach((row) => {
          if (row.id === value[1]) {
            this.queryParams.sceneCode = row.sceneCode;
          }
        });
      }
    },
    /** 查询成本预估列表 */
    getList() {
      this.loading = true;
      listEstimate(this.queryParams).then((response) => {
        this.estimateList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        sceneCode: null,
        industryCode: null,
        valueContent: null,
        createBy: null,
        createTime: null,
        delFlag: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.industryCode="";
      this.queryParams.sceneCode="";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加成本预估";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getEstimate(id).then((response) => {

        let industryId = null;
        let sceneId = null;

        this.industryList.forEach((item) => {
          if (item.industryCode === response.data.industryCode) {
            industryId = item.id;
          }
        });

        // 在 sceneList 中查找 sceneId
        this.sceneList.forEach((item) => {
          if (item.sceneCode === response.data.sceneCode) {
            sceneId = item.id;
          }
        });

        this.form = {
          ...response.data,
          sceneIds: [industryId, sceneId]
        };
        this.open = true;
        this.title = "修改成本预估";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        console.log(this.form);
        if (valid) {
          if (this.form.id != null) {
            updateEstimate(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEstimate(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      console.log(ids);
      const names = this.estimateList
        .filter(item => ids.includes(item.id))
        .map(item => item.title)
        .join(', ');
      this.$confirm('是否确认删除成本预估标题为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delEstimate(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/estimate/export",
        {
          ...this.queryParams,
        },
        `estimate_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
