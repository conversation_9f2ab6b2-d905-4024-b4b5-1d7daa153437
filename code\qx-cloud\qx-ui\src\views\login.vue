<template>
  <div class="login-container" v-loading.fullscreen.lock="true" element-loading-text="正在登录中...">
    <img src="https://eos-beijing-2.cmecloud.cn/ishow-bucket-prod/H5WEBGL/panovr_m/industry_homepage/img/zhizao-b.88626624.png"/>
    <!-- 这个 div 只用于承载 loading 效果，没有其他内容 -->
  </div>
</template>

<script>
import IframeMessage from "./../utils/iframeMessageBus";
import JSEncrypt from "jsencrypt";
import { getCodeImg, tokenlogin } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import UpdatePwd from "../components/UpdatePwd/updatepwd.vue";
import md5 from "md5";
export default {
  components: {
    UpdatePwd,
  },
  name: "Lo<PERSON>",
  data() {
    return {
      openCode: false,
      codeform: { loginCode: "" },
      coderules: {
        loginCode: [
          { required: true, message: "验证码不能为空", trigger: "blur" },
        ],
      },

      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        passwordTo: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        passwordTo: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      loading: false,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        // this.redirect = route.query && route.query.redirect;
        this.redirect = "index";
      },
      immediate: true,
    },
  },
  mounted() {
    try {
      //登录预加载index页面
      import(/* webpackPrefetch: true */ "./index.vue")
        .then(() => console.log("首页组件预加载成功"))
        .catch((err) => console.warn("预加载失败:", err));

      let datatoken;
      IframeMessage.getToken().then((res) => {
        if (res.token) {
          console.log(res);
          datatoken = {
            token: res.token,
            isAdmin: 1,
          };
          this.$store
            .dispatch("LoginToken", datatoken)
            .then((data) => {
              try {
                console.log("window.nlapmPlugin", window.nlapmPlugin);
                if (window.nlapmPlugin !== undefined) {
                  window.nlapmPlugin.login({
                    loginName: md5(token),
                  }); // loginName即为你的登录名（用户唯一标识）
                }
              } catch (e) {
                console.log("nlapmPlugin login erro!");
              }
              console.log("this.$router.push");

              if (this.$route.path !== data && this.$route.path !== "/index") {
                this.$router.push("/index").catch((err) => {
                  console.log("跳转失败:", err);
                });
              } else {
                console.log("已经在目标页面，跳转被跳过");
              }
            })
            .catch((error) => {
              console.error("获取验证码时出错:", error);
            });
        }

        // console.log('已收到', res)
      });

      let token = this.$route.query.token;
      if (token != null && token != "") {
        datatoken = {
          token: token,
        };
        this.$store
          .dispatch("LoginToken", datatoken)
          .then((data) => {
            console.log(data);

            try {
              console.log("window.nlapmPlugin", window.nlapmPlugin);
              if (window.nlapmPlugin !== undefined) {
                window.nlapmPlugin.login({
                  loginName: md5(token),
                }); // loginName即为你的登录名（用户唯一标识）
              }
            } catch (e) {
              console.log("nlapmPlugin login erro!");
            }
            console.log("this.$router.push");

            if (this.$route.path !== data && this.$route.path !== "/index") {
              this.$router.push("/index").catch((err) => {
                console.log("跳转失败:", err);
              });
            } else {
              console.log("已经在目标页面，跳转被跳过");
            }
          })
          .catch((error) => {
            console.error("获取验证码时出错:", error);
          });
      }
    } catch {}
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    // submitCode() {
    //   this.$refs.codeform.validate(valid => {
    //     if (valid) {
    //       //this.loginForm.code = this.codeform.loginCode;
    //       this.$store.dispatch("VerifyCode", { username: this.loginForm.username, code: this.codeform.loginCode }).then((data) => {
    //         console.log(data);
    //         // this.$router.push({ path: data || "/index" }).catch(() => { });
    //       }).catch(() => {
    //         this.loading = false;
    //       });
    //     }
    //   });
    // },
    // submitCode(event) {
    //   // 阻止回车时页面的默认行为
    //   if (event) {
    //     event.preventDefault();
    //   }
    //
    //   this.$refs.codeform.validate(valid => {
    //     if (valid) {
    //       // 登录验证码验证逻辑
    //       this.$store.dispatch("VerifyCode", { username: this.loginForm.username, code: this.codeform.loginCode }).then((data) => {
    //         console.log(data);
    //         this.$router.push({ path: data || "/index" }).catch(() => { });
    //       }).catch(() => {
    //         this.loading = false;
    //       });
    //     }
    //   });
    // },
    submitCode(event) {
      if (event) {
        event.preventDefault(); // 阻止默认行为
      }

      this.$refs.codeform.validate((valid) => {
        if (valid) {
          // 设置 loading 为 true，显示加载状态
          this.loading = true;

          this.$store
            .dispatch("VerifyCode", {
              username: this.loginForm.username,
              code: this.codeform.loginCode,
            })
            .then((data) => {
              // 请求成功后关闭 loading
              this.loading = false;
              console.log(data);
              try {
                console.log("window.nlapmPlugin", window.nlapmPlugin);
                if (window.nlapmPlugin !== undefined) {
                  window.nlapmPlugin.login({
                    loginName: this.loginForm.username,
                  }); // loginName即为你的登录名（用户唯一标识）
                }
              } catch (e) {
                console.log("nlapmPlugin login erro!");
              }

              this.$router.push({ path: data || "/index" }).catch(() => {});
            })
            .catch(() => {
              // 请求失败后关闭 loading
              this.loading = false;
            });
        }
      });
    },
    cancelCode() {
      this.openCode = false;
      this.codeform.loginCode = "";
    },

    getCode() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
        this.loginForm.publicKey = res.publicKey;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      let that = this;
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          //this.loginForm.password = md5(this.loginForm.passwordTo);

          const encryptor = new JSEncrypt();
          encryptor.setPublicKey(this.loginForm.publicKey); // 设置公钥
          that.loginForm.password = encryptor.encrypt(
            md5(that.loginForm.passwordTo)
          ); // 对需要加密的数据进行加密

          this.$store
            .dispatch("Login", this.loginForm)
            .then((data) => {
              if (data == 0) {
                this.openCode = true;
                this.loading = false;
              } else if (data == 1) {
                this.msgError("登录4A失败，请联系管理员");
                this.loading = false;
              } else {
                console.log(data);
                try {
                  console.log("window.nlapmPlugin", window.nlapmPlugin);
                  if (window.nlapmPlugin !== undefined) {
                    window.nlapmPlugin.login({
                      loginName: this.loginForm.username,
                    }); // loginName即为你的登录名（用户唯一标识）
                  }
                } catch (e) {
                  console.log("nlapmPlugin login erro!");
                }

                this.$router.push({ path: data || "/index" }).catch(() => {});
              }
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.png");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
  font-size: 24px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
</style>
