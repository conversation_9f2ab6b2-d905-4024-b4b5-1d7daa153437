<template>
  <div class="app-container">
    <div class="main">

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addRow()">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="deleteRow()">删除</el-button>
        </el-col>
      </el-row>



      <el-table v-loading="loading" :data="developmentList">
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="名称" align="center" prop="userName" />
      <el-table-column label="落地案例数量" align="center" prop="achieveNumber" />
      <el-table-column label="可支撑人数" align="center" prop="number" />
      <el-table-column label="落地案例地址" align="center" prop="achieveAddress" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ setTruncatedAddress(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="state" align="center" label="审核进度" width="160">
        <template slot-scope="scope">
          <span>{{scope.row.state===0?'审核中':scope.row.state===4?'审核通过':scope.row.state===2?'审核不通过':'未审核'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="定制开发内容" align="center" prop="imgUrl">
        <template slot-scope="scope">
          <img v-show="scope.row.imgUrl!=null&&scope.row.imgUrl!=''" style="width:50px;height:50px;cursor: pointer;" :src="scope.row.imgUrl" @click.stop="dialogVisible = true;dialogImgUrl=scope.row.imgUrl" />
        </template>
      </el-table-column>
      <el-table-column label="驳回原因" align="center" prop="explain" v-if="queryParams.state === 2">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.explain"
            raw-content
            placement="top-start"
            v-if="scope.row.explain"
          >
            <span v-if="scope.row.explain && scope.row.explain.length <= 30">
               {{ scope.row.explain }}
          </span>
            <span v-if="scope.row.explain && scope.row.explain.length > 30">
               {{ scope.row.explain.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.explain== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <router-link :to="'/developmentDetail/development/data/' + scope.row.id" class="link-type">
            <span>查看详情</span>
          </router-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    <el-dialog :visible.sync="dialogVisible" title="预览" width="1200" append-to-body>
      <img :src="dialogImgUrl" style="display: block; max-width: 100%; margin: 0 auto;">
    </el-dialog>



    <el-dialog :title="title" :visible.sync="open" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业" prop="industryCode">
              <el-select v-model="form.industryCode" placeholder="请选择行业">
                <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行业场景" prop="sceneId">
              <el-cascader v-model="form.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" @change="queryParamsHandleChange"></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="名称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入名称"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="落地案例数量" prop="achieveNumber">
              <el-input-number :min="0" :max="99999999" :controls="false" placeholder="请输入数量" v-model="form.achieveNumber" style="text-align:center;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可支撑人数" prop="number">
              <!-- <el-input v-model="form.number" placeholder="请输入可支撑人数"/> -->
              <el-input-number :min="0" :max="9999999999" :controls="false" placeholder="支撑人数" v-model="form.number" style="text-align:center;" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="落地案例地址" prop="achieveAddress">
          <template slot-scope="scope">
              <!-- <my-select :options='optionsProvince' style="width:250px" v-model="scope.row.achieveAddress" :ref="'ref'+scope.row.id" :key="'id'+scope.row.id"></my-select> -->
              <my-select :options='optionsProvince' style="width:250px" v-model="form.achieveAddress"></my-select>
          </template>
        </el-form-item>

        <el-form-item label="审核进度" prop="state">
          <template slot-scope="scope">
              <p>{{scope.row.state===0?'审核中':scope.row.state===4?'审核通过':scope.row.state===2?'审核未通过':scope.row.state===3?'未审核':''}}</p>
            </template>
        </el-form-item>

        <el-form-item label="审核不通过说明" prop="explain">
          <el-input v-model="form.explain" type="textarea" :rows="3" placeholder="请输入审核不通过说明" maxlength="1000" show-word-limit />
        </el-form-item>
        <!-- <el-form-item label="应用场景及亮点" prop="mainScene">
          <el-input v-model="form.mainScene" type="textarea" :rows="3" placeholder="请输入应用场景及亮点" maxlength="1000" show-word-limit />
        </el-form-item>
        <el-form-item label="项目整体成本" prop="projectCost">
          <el-input v-model="form.projectCost" type="textarea" :rows="3" placeholder="请输入项目整体成本" maxlength="1000" show-word-limit />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <!-- <el-button type="primary" v-if="region !='总部'" @click="handleExamine">提交审核</el-button> -->
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    </div>
  </div>
</template>
<script>
import MySelect from "../../../../components/MySelect/MySelect.vue";
import { getProvince } from "../../../../utils/province";
import { deleteall, add, list, edit, industrylist } from "@/api/hzhb/api"
export default {
  components: {
    MySelect
  },
  data() {
    return {
      open: false,
      dialogVisible: false,
       // 表单校验
       rules: {
        industryCode: [
          { required: true, message: "行业不能为空", trigger: "blur" }
        ],

      },
      dialogImgUrl: '',
        // 总条数
        total: 0,
      // 选中数组
      ids: [],
       // 遮罩层
       loading: true,
        // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industryId: null,
        sceneId: null,
        achieveNumber: null,
        achieveAddress: null,
        number: null,
        state: null,
        imgUrl: null,
        userName: null,
      },
       // 表单参数
       form: {},
      isOther: true,
      // 非多个禁用
      multiple: true,
      props: { multiple: true },
      name: null,
      value: '',
      optionsProvince: [],
      developmentList: [{ id: 0 }],
      industryList: [],
      sceneList: [],
      nikeName: '',
    };
  },
  created() {
    this.setProvince();
    this.getIndustryList();
    this.nikeName = localStorage.getItem("nickName");
  },
  mounted() {

  },
  methods: {
        /** 提交按钮 */
        submitForm() {
      this.$refs["form"].validate(valid => {
        // if (valid) {
        //   if (this.form.mainCaseId != null) {
        //     updateMain(this.form).then(response => {
        //       this.msgSuccess("修改成功");
        //       this.open = false;
        //       this.getList();
        //     });
        //   } else {
        //     addMain(this.form).then(response => {
        //       this.msgSuccess("新增成功");
        //       this.open = false;
        //       this.getList();
        //     });
        //   }
        // }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    setTruncatedAddress(row) {
      const address = this.setProvince(row);
      return address.length > 30 ? address.substring(0, 30) + '...' : address;
    },

    cancelPwd() {
      this.openSetPwd = false;
      this.pwdform = {
        password: null
      }
      this.resetForm("pwdform");
    },

    sceneChange(row) {
      let boo = true;
      this.developmentList.forEach(element => {
        if (element != row && element.sceneId == row.sceneId && row.sceneId != -1) {
          this.$message.error("场景重复");
          this.$set(row, "sceneId", null);
        }
        if (element.sceneId === -1)
          boo = false;
      });
      if (row.sceneId === -1)
        row.sceneName = '';
      this.isOther = boo;
    },
    industryChange(value) {
      this.industryList.forEach(element => {
        if (element.id == value.industryId) {
          //this.sceneList = element.qxSceneVoList;
          this.$set(value, "sceneList", element.qxSceneVoList);
          this.$set(value, "sceneId", null);
        }
      });
    },
    addRow() {
      this.developmentList.push({ id: this.developmentList.length, sceneName: '' });
    },
    deleteRow() {
      const ids = this.ids;
      let that = this;
      const names = this.developmentList
        .filter(item => ids.includes(item.id))
        .map(item => item.sceneName)
        .join(', ');

      this.$confirm('是否确认删除场景名称为"'+names+'"数据项吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        for (let i = 0; i < ids.length; i++) {
          if (ids[i].toString().length != 16) {
            ids.splice(i, 1);
            i--;
          }
        }
        if (ids.length == 0) {
          that.getList();
        } else {
          deleteall(ids).then(res => {
            if (res.code === 200) {
              that.$message.success("删除成功");
              that.getList();
            }
          });
        }
      }).then(() => {
        this.msgSuccess("删除成功");
      })
    },
    updateRow(row) {
      if (row.id == null || row.id.toString().length != 16) {
        this.$message.error("请先保存数据");
      } else {
        let industryCode = null;
        this.industryList.forEach(item => {
          if (item.id === row.industryId) {
            industryCode = item.industryCode;
          }
        });

        //this.$router.push({ path: "/unit/detail/index/0" }).catch(() => { })

        this.$router.push({ path: "/develop/detail/index/", query: { id: row.id, achieveNumber: row.achieveNumber, number: row.number, industryCode: industryCode } });
      }
    },
    validateAchieveNumber(row) {
      const maxAchieveNumber = 99999999;
      if (row.achieveNumber > maxAchieveNumber) {
        this.$message.error(`落地案例数量的最大值为 ${maxAchieveNumber}`);
        row.achieveNumber = maxAchieveNumber;
      }
    },

    validateNumber(row) {
      const maxNumber = 9999999999;
      if (row.number > maxNumber) {
        this.$message.error(`可支撑人数的最大值为 ${maxNumber}`);
        row.number = maxNumber;
      }
    },
    saveRow(row) {
      let that = this;
      for (let i = 0; i < this.developmentList.length; i++) {
        let element = this.developmentList[i];
        if (element != row && row.sceneId == -1 && element.sceneName == row.sceneName) {
          this.$message.error("其它场景重复");
          return false;
        }
      }
      if (row.sceneId === -1 && (row.sceneName === "" || row.sceneName == null)) {
        that.$message.error("其它场景不能为空");
        return false;
      }
      if (row.industryId === "" || row.industryId == null) {
        that.$message.error("行业不能为空");
        return false;
      }
      if (row.sceneId === "" || row.sceneId == null) {
        that.$message.error("场景不能为空");
        return false;
      }
      if (row.achieveNumber === "" || row.achieveNumber == null) {
        that.$message.error("落地案例数量不能为空");
        return false;
      }else if (row.achieveNumber > 99999999 ){
        that.$message.error("可支撑人数最大值为99999999");
        return false;
      }
      if (row.selectAddress == null || row.selectAddress.length === 0) {
        that.$message.error("落地案例地址不能为空");
        return false;
      }
      if (row.number === "" || row.number == null) {
        that.$message.error("可支撑人数不能为空");
        return false;
      }else if ( row.number > 9999999999 ){
        that.$message.error("可支撑人数最大值为9999999999");
        return false;
      }
      let addressItem = [];
      row.selectAddress.forEach(item => {
        if (item != null && item.length > 1)
          addressItem.push({ provinceId: item[1], cityId: item[0] });
      });
      row.qxDevelopmentCityList = addressItem;
      if (row.id == null || row.id.toString().length != 16) {
        add(row).then(res => {
          if (res.code === 200) {
            that.$message.success("添加成功");
            that.getList();
          }
        });
      } else {
        edit(row).then(res => {
          if (res.code === 200) {
            that.$message.success("修改成功");
            that.getList();
          }
        });
      }
    },
    getIndustryList() {
      let that = this;
      industrylist({}).then(res => {
        if (res.code === 200) {
          that.industryList = res.data;
          this.getList();
        }
      });
    },
    getList() {
      let that = this;
      let params = {
      };
      list(params).then(res => {
        if (res.code === 200) {
          that.developmentList = res.data == null || res.data.length == 0 ? [{ id: 0, sceneId: null }] : res.data;
          let isOther = true;
          that.developmentList.forEach(item => {
            //场景选择
            let isOk = true;
            that.industryList.forEach(element => {
              if (element.id == item.industryId) {
                item.sceneList = element.qxSceneVoList;
                element.qxSceneVoList.forEach(scene => {
                  if (scene.id === item.sceneId) {
                    isOk = false;
                  }
                });
              }
            });
            if (isOk && item.id != 0) {
              item.sceneId = -1;
              isOther = false;
            }
            //省市选中
            item.selectAddress = [];
            if (item.qxDevelopmentCityList != null && item.qxDevelopmentCityList.length > 0) {
              item.qxDevelopmentCityList.forEach(row => {
                item.selectAddress.push([row.provinceId, row.cityId]);
              });
            }
            this.$nextTick(() => {
              that.$refs['ref' + item.id].selectValue = item.selectAddress;
            })
          });
          this.isOther = isOther;
        }
      });
    },
    setProvince() {
      this.optionsProvince = getProvince();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.multiple = !selection.length
    }
  }
};
</script>

<style lang="scss" scoped>
.index {
  width: 100%;
  height: 100vh;
  font-family: FZLTXHK--GBK1-0, FZLTXHK--GBK1;
  .head {
    float: left;
    width: 100%;
    background: #f0f6f9;
    text-align: center;
    height: 3.3vw;
    font-size: 1.3vw;
    font-family: FZLTZHK--GBK1-0, FZLTZHK--GBK1;
    font-weight: normal;
    color: #131415;
    line-height: 3.3vw;
  }
  .right {
    position: absolute;
    right: 0;
    top: 0;
    height: 3.3vw;
    width: 35vw;
    padding-right: 1.7vw;
    .myinfo {
      float: right;
      width: 15.3vw;
      font-size: 1vw;
      color: #131415;
      cursor: pointer;
      text-align: right;
      margin-right: 1.5vw;
    }
    .login-out {
      color: #ff5656;
      padding-left: 1.3vw;
      border-left: 1px solid #d8d8d8;
      float: right;
      width: 4.3vw;
      font-size: 1vw;
      height: 1vw;
      line-height: 1vw;
      margin-top: 1.15vw;
      cursor: pointer;
    }
    .update-pwd {
      color: #000;
      margin-right: 1.3vw;
      padding-left: 1.3vw;
      border-left: 1px solid #d8d8d8;
      float: right;
      width: 4.3vw;
      font-size: 1vw;
      height: 1vw;
      line-height: 1vw;
      margin-top: 1.15vw;
      cursor: pointer;
    }
  }
  .title {
    float: left;
    height: 5.8vw;
    width: 96.6vw;
    margin: 0 1.7vw;
    box-sizing: border-box;
    border-bottom: 2px solid #d1d1d1;

    .label {
      font-size: 1.7vw;
      font-weight: normal;
      color: #131415;
      line-height: 1.7vw;
      width: 100%;
      float: left;
      height: 5.8vw;
      line-height: 5.8vw;
      text-align: center;
    }
    .input {
      float: left;
      width: 8.3vw;
      height: 2.8vw;
      border-radius: 0.3vw;
      border: 1px solid #dcdcdc;
      font-size: 1vw;
      box-sizing: border-box;
      padding: 0 1.3vw;
      color: #1c1c1d;
      margin-top: 1.5vw;
      margin-left: 0.8vw;
    }
    .btn-upload {
      width: 8.3vw;
      height: 2.8vw;
      border-radius: 0.3vw;
      background: #0052d9;
      font-size: 0.8vw;
      text-align: center;
      line-height: 2.8vw;
      margin-left: 1vw;
      color: #fff;
      float: left;
      border: none;
      cursor: pointer;
      margin-top: 1.5vw;
    }
  }
  .content {
    float: left;
    margin-top: 1.8vw;
    padding-left: 5vw;
    width: 91.6vw;
    margin-bottom: 7.2vw;
    .grid-content {
      text-align: left;
      font-weight: normal;
      color: #131415;
      font-size: 1vw;
    }
    ::v-deep .el-table--border::after,
    .el-table--group::after,
    .el-table::before {
      background-color: #fff;
    }
    ::v-deep .el-table th.el-table__cell.is-leaf {
      border: none !important;
      border: 0px !important;
    }
    ::v-deep .el-table thead {
      color: #131415;
      font-size: 1vw;
      padding: 0;
    }
    ::v-deep .el-input-number .el-input__inner {
      text-align: left;
    }
  }
  .footer {
    z-index: 1000;
    background-color: #fff;
    opacity: 1;
    border-top: 1px solid #999;
    position: fixed;
    width: 100%;
    float: left;
    bottom: 0;
    left: 0;
    height: 7.2vw;
    .delete:disabled {
      background-color: #999;
      cursor: auto;
    }
    .delete {
      float: right;
      margin-right: 1.6vw;
      width: 16vw;
      height: 5vw;
      background: #0052d9;
      border-radius: 0.3vw;
      font-size: 1.2vw;
      line-height: 5vw;
      text-align: center;
      border: none;
      margin-top: 1.2vw;
      color: #fff;
      cursor: pointer;
      border-color: #0052d9;
    }
    .add {
      float: right;
      margin-right: 1.6vw;
      width: 16vw;
      height: 5vw;
      background: #fff;
      border-radius: 0.3vw;
      font-size: 1.2vw;
      line-height: 5vw;
      text-align: center;
      border: none;
      margin-top: 1.2vw;
      color: #fe5959;
      cursor: pointer;
      border: 1px solid #fe5959;
    }
  }
}
</style>
