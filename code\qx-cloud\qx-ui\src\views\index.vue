<template>
  <div style="width: 100%; height: calc(100vh - 90px); overflow: auto;background: #f0f2f5;">
    <div class="dashboard-editor-container">
      <div class="dashboard-editor-header">
        <img src="@/assets/images/index/biaoti.png" alt="" />
        平台总体数据
        <div class="date">
          <el-date-picker v-model="dayDate" @change="getCaseMainList()" style="width:280px" format="yyyy/MM/dd" prefix-icon="" :clearable="false" ref="elDatePickControl2" type="daterange" range-separator="-"
            start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                disabledDate: time => {
                  const today = new Date();
                  // 禁止选择今天之后的时间
                  if (time.getTime() > today.getTime()) {
                    return true;
                  }
                  // 禁止结束时间选择今天
                  if (this.dayDate && this.dayDate.length === 2) {
                    return time.getTime() === today.setHours(0, 0, 0, 0);
                  }
                  return false;
                }
              }"></el-date-picker>
          <div class="title-down" @click="downClick('elDatePickControl2')"></div>
        </div>
      </div>
      <overallData v-if="mounted" :Date="this.dayDate"></overallData>
      <div class="dashboard-editor-header mt20">
        <img src="@/assets/images/index/biaoti.png" alt="" />
        访问数据
      </div>

      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="访问次数" name="first">
          <panel-group v-if="mounted" :options="dataOptions" :dataOptionsImage="dataOptionsImage" :titleOptions="titleOptions" @handleSetLineChartData="handleSetLineChartData" style="margin: 0;" />
          <el-row v-show="selectIndex != 0" style="background:#fff; padding:16px 16px 0;margin-bottom:32px; overflow-x: auto;">
            <div :style="'width:'+(industryList.length*25)+'px;'">
              <bar-chart v-if="mounted" ref="barChartTo" :width="'2800px'" />
            </div>
          </el-row>
          <el-row v-if="selectIndex === 2" style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
            <bar-chart v-if="mounted" ref="barChartToA" :width="'1608px'" />
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="停留时长" name="second">
          <panel-time-group :options="titleTimeOptions" @handleSetLineChartData="handleSetTimeChartData" />
          <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;margin-top:20px;">
            <line-chart v-if="mounted" ref="lineChart" />
          </el-row>
        </el-tab-pane>
      </el-tabs>

      <div class="dashboard-editor-header mt20">
        <img src="@/assets/images/index/biaoti.png" alt="" />
        各功能板块使用时长
        <div class="more" @click="handleMoreClick('functional-classification')">查看更多</div>
      </div>
      <functionalClassification :dayDate="dayDate" v-if="mounted"/>

      <div class="dashboard-editor-header mt20" v-if="!isSecondaryAdministrator">
        <img src="@/assets/images/index/biaoti.png" alt="" />
        各省用户数据
        <div class="more" @click="handleMoreClick('user-data')">查看更多</div>
      </div>
      <userData :dayDate="dayDate" v-if="mounted && !isSecondaryAdministrator"/>
      <div style="display: flex;">
        <user-region v-if="mounted" :dayDate="dayDate"></user-region>
        <two-casemain v-if="mounted" :dayDate="dayDate"></two-casemain>
      </div>

      <div>
        <user-support :dayDate="dayDate"  v-if="mounted" />
      </div>

      <div style="display: flex;">
        <PartnerClassification :dayDate="dayDate" v-if="mounted"></PartnerClassification>
        <user-brisk  v-if="mounted"/>
      </div>

    </div>
  </div>
</template>

<script>



// import { isUpdPwd, updatePwd } from "@/api/login";

import {
  listClickRecords,
  listItemClickRecords,
} from "@/api/system/clickrecords";
import { listIndustry } from "@/api/system/industry";
import { provincialSecondaryAdministrator } from '@/api/system/sum.js'
import md5 from "md5";

// import { setScale } from "@/utils/setScale";
const lineChartData = {
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145],
  },
};

export default {
  name: "Index",
  components: {
    PanelGroup: () => ({
      component: import('@/views/dashboard/PanelGroup.vue')
        .then(comp => {
          console.log('PanelGroup loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('PanelGroup load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    FunctionalClassification: () => ({
      component: import('@/views/dashboard/functional-classification.vue')
        .then(comp => {
          console.log('FunctionalClassification loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('FunctionalClassification load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    PartnerClassification: () => ({
      component: import('@/views/dashboard/partner-classification.vue')
        .then(comp => {
          console.log('PartnerClassification loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('PartnerClassification load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    UserBrisk: () => ({
      component: import('@/views/dashboard/user-brisk.vue')
        .then(comp => {
          console.log('UserBrisk loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('UserBrisk load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    UserData: () => ({
      component: import('@/views/dashboard/user-data.vue')
        .then(comp => {
          console.log('UserData loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('UserData load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    BarChart: () => ({
      component: import('@/views/dashboard/BarChart.vue')
        .then(comp => {
          console.log('BarChart loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('BarChart load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    LineChart: () => ({
      component: import('@/views/dashboard/LineChart.vue')
        .then(comp => {
          console.log('LineChart loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('LineChart load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    OverallData: () => ({
      component: import('@/views/dashboard/overall-data.vue')
        .then(comp => {
          console.log('OverallData loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('OverallData load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    UserRegion: () => ({
      component: import('@/views/dashboard/user-region.vue')
        .then(comp => {
          console.log('UserRegion loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('UserRegion load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    TwoCasemain: () => ({
      component: import('@/views/dashboard/two-casemain.vue')
        .then(comp => {
          console.log('TwoCasemain loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('TwoCasemain load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    UserSupport: () => ({
      component: import('@/views/dashboard/user-support.vue')
        .then(comp => {
          console.log('UserSupport loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('UserSupport load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    }),
    PanelTimeGroup: () => ({
      component: import('@/views/dashboard/PanelTimeGroup.vue')
        .then(comp => {
          console.log('PanelTimeGroup loaded successfully');
          return comp;
        })
        .catch(err => {
          console.error('PanelTimeGroup load failed:', err);
          return import('@/components/ErrorComponent.vue');
        }),
      loading: {
        template: '<div style="padding: 20px; text-align: center;">加载中...</div>'
      },
      delay: 200,
      timeout: 3000
    })
  },

  created() {
    const end = new Date();
    end.setDate(end.getDate() - 1);
    end.setHours(0, 0, 0, 0);
    // 设置为当前时间的前一天
    end.setHours(0, 0, 0, 0);
    const start = new Date(end.getFullYear(), 0, 1); // 当年的1月1号
    this.dayDate = [start, end];
    console.log(this.dayDate);
    this.queryParams.startDate = this.parseTime(this.dayDate[0], "{y}-{m}-{d}");
    this.queryParams.endDate = this.parseTime(this.dayDate[1], "{y}-{m}-{d}");
    let that = this;


    // setScale();
    // window.onresize = () => {
    //   setScale();
    // };
    listIndustry().then((response) => {
      this.industryList = response.rows;
      this.getList();
    });
    this.myProvincialSecondaryAdministrator();
  },
  data() {
    return {
      mounted:false,
      dayDate: [],

      activeName: "first",
      //titleOptions: ['ishow首页访客', '行业访客', '场景访客', '', '网络方案访客', '商业价值访客', '落地案例访客', 'VR看现场访客', '集成报价访客', '行业生态链访客'],
      titleOptions: [
        "ishow首页访客",
        "行业访客",
        "场景访客",
        "",
        "网络方案访客",
        "商业价值访客",
        "落地案例访客",
        "VR看现场访客",
        "集成报价访客",
        "行业生态链访客",
        "视频讲解访客",
        "合作伙伴访客",
        "产品介绍访客",
        "基础能力访客",
      ],
      titleTimeOptions: [
        { id: 10, name: "视频讲解", value: 0, time: 0 },
        { id: 11, name: "合作伙伴", value: 0, time: 0 },
      ],
      dataOptions: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      dataOptionsImage: ["../../assets/images/index/icon_0.png"],
      lineChartData: lineChartData.newVisitis,
      queryParams: {},
      sumList: [],
      //行业列表
      industryList: [],
      nameOptions: [],
      valueOptions: [],
      selectIndex: 10,

      nameTowOptions: [],
      valueTowOptions: [],
      lengthOptions: [],
      isSecondaryAdministrator: false,
    };
  },
  mounted() {

    this.loading = false; // 当页面加载完成时
    this.mounted = true;
    this.$nextTick(() => {
      this.$forceUpdate();
    });
  },
  methods: {
    myProvincialSecondaryAdministrator(){
      provincialSecondaryAdministrator().then(res=>{
        this.isSecondaryAdministrator = res.data.isSecondaryAdministrator;
      });
    },
    downClick(name) {
      let that = this;
      that.$refs[name].focus();
    },
    // 跳转传参数 开始 结束时间
    handleMoreClick(url) {
      console.log(this.dayDate);

      this.$router.push("indexDetail/" + url + "?dayDate=" + this.dayDate);
    },
    getCaseMainList() {
      let that = this;
      if (that.dayDate.length > 0) {
        that.queryParams.startDate = this.parseTime(
          this.dayDate[0],
          "{y}-{m}-{d}"
        );
        that.queryParams.endDate = this.parseTime(
          this.dayDate[1],
          "{y}-{m}-{d}"
        );
      }
      this.getList();
    },
    handleClick() {
      if (this.activeName == "second") {
      } else {
      }
    },
    setTimeLength(type) {
      //访问总数
      this.nameTowOptions = [];
      this.valueTowOptions = [];
      this.lengthOptions = [];

      this.industryList.forEach((item) => {
        item.number = 0;
        item.sum = 0;
        this.sumList.forEach((row) => {
          if (row.clickType == type && item.industryCode == row.clickValue) {
            item.number += parseInt(row.clickNumber);
            item.sum += parseInt(row.timeLength);
          }
        });
      });
      this.industryList.forEach((item) => {
        this.nameTowOptions.push(item.industryName);
        this.valueTowOptions.push(item.number);
        this.lengthOptions.push(item.sum);
      });
    },
    handleSetTimeChartData(type) {
      this.setTimeLength(type);
      this.$forceUpdate();
      if (this.$refs.lineChart != null) {
        this.$refs.lineChart.initChart(
          this.nameTowOptions,
          this.valueTowOptions,
          this.lengthOptions
        );
      }
    },

    setTypeData(type) {
      //访问总数
      this.nameOptions = [];
      this.valueOptions = [];
      if (type == -1) {
        this.industryList.forEach((item) => {
          let options = {
            nameOptions: [],
            valueOptions: [],
            value: 0,
          };
          this.titleOptions.forEach((title, index) => {
            let number = 0;
            if (index != 3) {
              this.sumList.forEach((row) => {
                if (
                  row.clickType == index &&
                  item.industryCode == row.clickValue
                ) {
                  number += parseInt(row.clickNumber);
                }
              });
              options.nameOptions.push(title);
              options.valueOptions.push(number);
              options.value += number;
            }
          });
          this.nameOptions.push(item.industryName);
          this.valueOptions.push(options);
        });
      } else if (type == 13) {
        this.industryList.forEach((item) => {
          let options = {
            nameOptions: [],
            valueOptions: [],
            value: 0,
          };
          this.titleOptions.forEach((title, index) => {
            let number = 0;
            if (index == 0 || index == 1 || index == 11 || index == 13) {
              this.sumList.forEach((row) => {
                if (
                  row.clickType == index &&
                  item.industryCode == row.clickValue
                ) {
                  number += parseInt(row.clickNumber);
                }
              });
              options.nameOptions.push(title);
              options.valueOptions.push(number);
              options.value += number;
            }
          });
          this.nameOptions.push(item.industryName);
          this.valueOptions.push(options);
        });
      } else {
        this.industryList.forEach((item) => {
          item.number = 0;
          this.sumList.forEach((row) => {
            if (row.clickType == type && item.industryCode == row.clickValue) {
              item.number += parseInt(row.clickNumber);
            }
          });
          // 计算柱状图
          this.nameOptions.push(item.industryName);
          this.valueOptions.push(item.number);
        });
      }
    },
    /** 查询场景默认项列表 */
    getList() {
      let that = this;

      // this.dataOptions = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      listClickRecords(this.queryParams).then((response) => {
        this.dataOptions = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        this.sumList = response.data;
        this.sumList.forEach((item) => {
          let clickType = parseInt(item.clickType);
          let clickNumber = parseInt(item.clickNumber);
          this.$set(
            this.dataOptions,
            clickType,
            this.dataOptions[clickType] + clickNumber
          );
        });
        this.setTypeData(-1);
        this.nameOptions.splice(0, 0, "ishow首页");
        this.valueOptions.splice(0, 0, this.dataOptions[0]);

        //行业首页，行业访客做处理
        // let avg = 0
        // let sum = that.dataOptions[0] + that.dataOptions[1]
        // if (sum > 0) avg = parseInt(sum / 8)

        // for (let i = 0; i < that.dataOptions.length; i++) {
        //     if (i != 0 && i != 1 && i != 3 && i < 11) {
        //         that.dataOptions[i] = that.dataOptions[i] + avg
        //         if (i == 2) that.dataOptions[2] = that.dataOptions[i] + (sum % 8)
        //     }
        // }
        this.titleTimeOptions = [
          { id: 10, name: "视频讲解", value: 0, time: 0 },
          { id: 11, name: "合作伙伴", value: 0, time: 0 },
        ];
        if (this.$refs.barChartTo != null) {
          this.$refs.barChartTo.initChart(
            "访问总数",
            this.nameOptions,
            this.valueOptions,
            false
          );
        }
        this.titleTimeOptions.forEach((option) => {
          this.sumList.forEach((row) => {
            if (row.clickType == option.id) {
              option.time += parseInt(row.timeLength);
              option.value += parseInt(row.clickNumber);
            }
          });
        });
        this.setTimeLength(10);
        if (this.$refs.lineChart != null) {
          this.$refs.lineChart.initChart(
            this.nameTowOptions,
            this.valueTowOptions,
            this.lengthOptions
          );
        }
      });
    },
    handleSetLineChartData(type) {
      this.selectIndex = type;
      this.title = this.titleOptions[type];
      this.setTypeData(type);
      if (type === -1 || type === 13) {
        this.nameOptions.splice(0, 0, "ishow首页");
        this.valueOptions.splice(0, 0, this.dataOptions[0]);
      }
      this.$refs.barChartTo.initChart(
        type == -1
          ? "访问总数"
          : type == 13
          ? "其它访客"
          : this.titleOptions[type],
        this.nameOptions,
        this.valueOptions,
        false
      );
      if (this.selectIndex === 2) {
        this.$nextTick(() => {
          if (this.industryList != null && this.industryList.length > 0)
            this.setImgData(this.industryList[0].industryName);
        });
      }
    },
    setImgData(code) {
      this.industryList.forEach((item) => {
        if (item.industryName == code) {
          let params = {
            clickValue: item.industryCode,
            clickType: 2,
            startDate: this.parseTime(this.dayDate[0], "{y}-{m}-{d}"),
            endDate: this.parseTime(this.dayDate[1], "{y}-{m}-{d}"),
          };
          let nameOptions = [];
          let valueOptions = [];

          listItemClickRecords(params).then((response) => {
            response.qxclickRecords.forEach((qxclickrecord) => {
              response.qxScenes.forEach((qxscene) => {
                if (qxscene.sceneCode == qxclickrecord.childType) {
                  nameOptions.push(
                    qxscene.sceneCode == "DL05"
                      ? "智能化巡检"
                      : qxscene.sceneName
                  );
                  valueOptions.push(qxclickrecord.clickNumber);
                }
              });
            });
            if (!(nameOptions.length === 0 || valueOptions.length === 0)) {
              if (this.$refs.barChartToA != null) {
                this.$refs.barChartToA.initChart(
                  item.industryName + "行业场景访客",
                  nameOptions,
                  valueOptions,
                  true
                );
              }
            }
          });
        }
      });
    },

    bigDepatClick(name) {
      if (this.selectIndex === 2) {
        this.industryList.forEach((item) => {
          if (item.industryName == name) {
            this.setImgData(name);
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
body {
  height: 100%;
}

.dashboard-editor-container {
  font-family: "思源黑体";
  padding: 24px;
  background-color: rgb(240, 242, 245);
  position: relative;
  height: calc(100vh -88px);
  margin: auto;
  /* 减去对应的高度 */
  width: 1680px;
  /* 确保需要的内容宽度 */
  /* 确保可以横向滚动 */
  // overflow-y: hidden;
  /* 隐藏纵向滚动条 */

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

.dashboard-editor-header {
  font-size: 24px;
  font-weight: 900;
  color: #0066e4;
  display: flex;
  align-items: center;

  img {
    width: 18px;
    height: 24px;
    margin-right: 16px;
  }

  .more {
    flex: 1;
    text-align: right;
    font-size: 18px;
    font-weight: 500;
    position: relative;
    padding-right: 10px;
    cursor: pointer;
  }

  .more::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    border-top: 2px solid #0066e4;
    /* 修改颜色以适应你的设计 */
    border-right: 2px solid #0066e4;
    /* 修改颜色以适应你的设计 */
    transform: translateY(-50%) rotate(45deg);
  }
}

.mt20 {
  margin: 50px 0 20px 0;
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}

.date {
  flex: 1;
  text-align: right;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-left: 20px;
  // cursor: pointer;
}

.date {
  ::v-deep .el-input__inner {
    border: 0 !important;
    background: transparent;
  }
}

// ::v-deep .el-dialog {
//   .el-input__inner {
//     border: 1px solid #999 !important;
//     background-color: #FFFFFF;
//   }
// }

::v-deep .el-range-editor--medium .el-range__icon,
.el-range-editor--medium .el-range__close-icon {
  display: none;
}

::v-deep .el-date-editor .el-range-separator {
  padding: 0;
}

::v-deep .el-date-editor .el-range__close-icon {
  display: none;
}

::v-deep .el-date-editor .el-range-input {
  width: 48%;
  font-size: 20px;
  height: 38px;
  line-height: 38px;
  font-weight: 800;
  color: #1366bf;
  // font-family: MiSans-Heavy, MiSans;
  background: transparent;
}

::v-deep .el-range-separator {
  font-size: 20px;
  height: 38px;
  line-height: 38px;
  font-weight: 800;
  color: #1366bf;
  font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-date-editor > input:-moz-placeholder {
  color: #1366bf;
}

::v-deep .el-date-editor > input:-ms-input-placeholder {
  color: #1366bf;
}

::v-deep .el-date-editor > input::-webkit-input-placeholder {
  color: #1366bf;
}

.title-down {
  // float: right;
  // margin-top: 15px;
  height: 9px;
  width: 17px;
  background: url(../assets/images/click/brisk_down.png) no-repeat center center;
  background-size: 100%;
  cursor: pointer;
}

.dashboard-editor-container {
  .el-tabs__item {
    width: 140px;
    height: 60.06px;
    opacity: 1;
    background: #fff;
    font-size: 20px;
    line-height: 60.06px;
    font-weight: 700;
    color: #000;
    text-align: center;
    padding: 0;
  }

  .el-tabs {
    background: #fff;
    border-radius: 10px;
    padding: 0 20px;
  }

  .el-tabs__item.is-active {
    background: linear-gradient(180deg, #373eff33 0%, #1366bf00 100%);
  }

  .el-tabs__nav-wrap::after {
    background-color: #fff;
  }

  .el-tabs__active-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 2px;
    background-color: #1890ff;
    z-index: 1;
  }
}
</style>
