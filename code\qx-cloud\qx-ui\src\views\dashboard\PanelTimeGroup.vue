<template>
    <div>
        <el-row :gutter="40" class="panel-group" style="margin: 0;">
            <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 10 }">
                <div class="card-panel" @click="handleSetLineChartData(10)">
                    <div>
                        <img src="@/assets/images/index/icon_10.png" alt="" style="width: 56px;height: auto;" />
                    </div>

                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            {{ options[0].name }}
                        </div>
                        <count-to :start-val="0" :end-val="options[0].time" :duration="2600" class="card-panel-num" />
                    </div>
                </div>
            </el-col>
            <el-col :xs="12" :sm="12" :lg="4" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 11 }">
                <div class="card-panel" @click="handleSetLineChartData(11)">
                    <div>
                        <img src="@/assets/images/index/icon_11.png" alt="" style="width: 56px;height: auto;" />
                    </div>

                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            {{ options[1].name }}
                        </div>
                        <count-to :start-val="0" :end-val="options[1].time" :duration="3000" class="card-panel-num" />
                    </div>
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import CountTo from 'vue-count-to'

export default {
    components: {
        CountTo
    },

    props: {
        options: {
            type: Array
        }
    },
    data() {
        return {
            selectNum: 10
            //sumNum: 0
        }
    },
    mounted() {
        console.log(this.options, 'options-----------------------------------')
        //  this.sumNum =;
    },
    methods: {
        handleSetLineChartData(type) {
            this.selectNum = type
            this.$emit('handleSetLineChartData', type)
        }
    }
}
</script>

<style lang="scss" scoped>
.panel-group {
    display: flex;
    align-items: center;
    .card-panel-col {
        margin-bottom: 16px;
        margin-right: 2%;
        background: #ffffff;
        box-shadow: 0 2px 2px 0 #d5dde3;
        border-radius: 4px;
        border: 3px solid transparent;
        &:hover {
            border: 3px solid #175fff;
            box-shadow: 0 4px 6px 0 #d5dde3;
        }
    }
    .card-panel-col-hover {
        border: 3px solid #175fff;
        box-shadow: 0 4px 6px 0 #d5dde3;
    }
    .el-col-lg-4-8 {
        width: 35%;
    }

    .el-col-lg-5 {
        width: 17.888%;
    }
    .card-panel {
        height: 108px;
        cursor: pointer;
        font-size: 12px;
        position: relative;
        overflow: hidden;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: space-around;
        // background: #fff;
        // box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
        // border-color: rgba(0, 0, 0, 0.05);

        .card-panel-icon-wrapper {
            float: left;
            margin: 14px 0 0 14px;
            padding: 16px;
            transition: all 0.38s ease-out;
            border-radius: 6px;
        }

        .card-panel-icon {
            float: left;
            font-size: 48px;
        }

        .card-panel-description {
            font-weight: bold;
            text-align: right;
            .card-panel-text {
                line-height: 18px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 16px;
                margin-bottom: 12px;
            }

            .card-panel-num {
                font-size: 20px;
            }
        }
    }
}

@media (max-width: 550px) {
    .card-panel-description {
        display: none;
    }

    .card-panel-icon-wrapper {
        float: none !important;
        width: 100%;
        height: 100%;
        margin: 0 !important;

        .svg-icon {
            display: block;
            margin: 14px auto !important;
            float: none !important;
        }
    }
}
</style>
