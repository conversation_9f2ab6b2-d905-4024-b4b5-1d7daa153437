<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
       <el-form-item label="姓名" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入姓名" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="省份" prop="provinceName">
        <el-input v-model="queryParams.provinceName" placeholder="请输入省份" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="注册终端" prop="isMobile">
        <el-select v-model="queryParams.isMobile" collapse-tags placeholder="请选择注册终端" clearable size="small">
          <el-option v-for="dict in formOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="createTime"
          type="datetimerange"
          :picker-options="pickerOptions"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          align="right">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:customer:add']">新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:customer:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExportVo" v-hasPermi="['system:customer:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="customerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="序号" width="50" align="center">
        <template slot-scope="scope">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="邮箱" align="center" prop="account" />
      <el-table-column label="姓名" align="center" prop="nickname" />
      <el-table-column label="省份" align="center" prop="provinceName" />
      <el-table-column label="注册终端" align="center" prop="isAbout">
        <template slot-scope="scope">
          <span>{{scope.row.isMobile==0?'PC端':scope.row.isMobile==1?'移动端':scope.row.isMobile==2?'移动办公APP端':"网大"}}</span>
        </template>
      </el-table-column>
      <el-table-column label="登录次数" align="center" prop="loginCount">
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:customer:edit']">修改密码</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:customer:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改前端客户用户对话框 -->
    <el-dialog title="修改密码" :visible.sync="openSetPwd" width="500px" append-to-body>
      <el-form ref="pwdform" :model="pwdform" :rules="pwdrules" label-width="80px">
        <el-form-item label="密码" prop="password">
          <el-input v-model="pwdform.password" type="password" placeholder="请输入密码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPwdForm">确 定</el-button>
        <el-button @click="cancelPwd">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改前端客户用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="邮箱" prop="account">
          <el-input v-model="form.account" placeholder="请输入账号" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" placeholder="请输入密码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <TreasuryVerify ref="treasuryVerifyRef" functionCode="600001" operationCode="1001" />
  </div>
</template>

<script>
import { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer, exportCustomer } from "@/api/system/customer";
import TreasuryVerify from "../../../components/TreasuryVerify/TreasuryVerify.vue";
import { Loading } from 'element-ui'
import md5 from 'md5';
export default {
  name: "Customer",
  components: {TreasuryVerify
  },
  data() {
    return {
      formOptions:[],
      createTime: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 前端客户用户表格数据
      customerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openSetPwd: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        icon: null,
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      value2: '',
      // 表单参数
      form: {},
      pwdform: {},
      // 表单校验
      pwdrules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          { max: 32, message: "密码长度在8-32位字符", trigger: "blur" },
          { min: 8, message: "密码长度在8-32位字符", trigger: "blur" },
          {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+[\]{};':"\\|,.<>/?-]).{8,32}$/,
            message: "密码只能包含英文字母、数字和特殊符号",
            trigger: "blur"
          }
        ]
      },
      rules: {
        account: [
          { required: true, message: "用户账号邮箱不能为空", trigger: "blur" },
          {
            pattern: /^([a-zA-Z]|[0-9])(\w|-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/,
            message: "账号邮箱格式不正确",
            trigger: "blur"
          }
        ],
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          { max: 32, message: "密码长度在8-32位字符", trigger: "blur" },
          { min: 8, message: "密码长度在8-32位字符", trigger: "blur" },
          {
            pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+[\]{};':"\\|,.<>/?-]).{8,32}$/,
            message: "密码只能包含英文字母、数字和特殊符号",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("channel_type").then(response => {
      this.formOptions = response.data;
    });
    

  },
  methods: {
    /** 查询前端客户用户列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      console.log(this.createTime)
      if (this.createTime != null && this.createTime.length > 0) {
        this.queryParams.params.createTime = this.parseTime(this.createTime[0], '{y}-{m}-{d} {h}:{i}:{s}');
        this.queryParams.params.endTime = this.parseTime(this.createTime[1], '{y}-{m}-{d} {h}:{i}:{s}');
      }
      listCustomer(this.queryParams).then(response => {
        this.customerList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelPwd() {
      this.openSetPwd = false;
      this.pwdform = {
        id: null,
        password: null
      }
      this.resetForm("pwdform");
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        icon: null,
        createTime: null,
        delFlag: null
      };

      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.createTime = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加前端客户用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids;
      this.openSetPwd = true;
      this.pwdform = {
        id: id,
        password: null
      }
      this.resetForm("pwdform");
    },
    /** 提交按钮 */
    submitPwdForm() {
      this.$refs["pwdform"].validate(valid => {
        if (valid) {
          if (this.pwdform.id != null) {
            this.pwdform.password = md5(this.pwdform.password);
            updateCustomer(this.pwdform).then(response => {
              this.msgSuccess("修改成功");
              this.openSetPwd = false;
            });
          }
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.password = md5(this.form.password);
          addCustomer(this.form).then(response => {
            this.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          });

        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.customerList
        .filter(item => ids.includes(item.id))
        .map(item => item.nickname)
        .join(', ');


      this.$confirm('是否确认删除前端客户为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delCustomer(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },

handleExportVo() {
      const queryParams = this.queryParams;
      this.queryParams.accountType = this.activeName;
      exportCustomer(queryParams)
        .then(response => {
          if (response.msg !== "-1") {
            this.handleExport();
          } else {
            this.$refs.treasuryVerifyRef.getTreasuryApprover();
          }
        });
    },
       /** 导出按钮操作 */
    handleExport() {
      let that = this;
         const exportParams = {
           ...that.queryParams,
           exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
         };
      that.download('system/customer/exportvo', exportParams, `customer_${new Date().getTime()}.xlsx`)
    }

    // /** 导出按钮操作 */
    // handleExport() {
    //   let that = this;
    //   let downloadLoadingInstance = null;
    //   const queryParams = this.queryParams;
    //   this.$confirm('是否确认导出所有前端客户用户数据项?', "警告", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning"
    //   }).then(function () {
    //     downloadLoadingInstance = Loading.service({ text: "正在下载数据，请稍候", spinner: "el-icon-loading", background: "rgba(0, 0, 0, 0.7)", });
    //     return exportCustomer(queryParams);
    //   }).then(response => {
    //     if (response.msg != "-1") {
    //       that.download(response.msg);
    //     } else {
    //       that.$refs.treasuryVerifyRef.getTreasuryApprover();
    //     }
    //     if (downloadLoadingInstance != null)
    //       downloadLoadingInstance.close();
    //   }).catch((r) => {
    //     console.error(r)
    //     this.msgError('下载文件出现错误，请联系管理员！')
    //     if (downloadLoadingInstance != null)
    //       downloadLoadingInstance.close();
    //   })
    // }
  }
};
</script>
