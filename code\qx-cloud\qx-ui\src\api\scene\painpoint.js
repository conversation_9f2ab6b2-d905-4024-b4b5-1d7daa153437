import request from '@/utils/request'

// 查询痛点价值列表
export function listPainpoint(query) {
  return request({
    url: '/system/scene/painpoint/list',
    method: 'get',
    params: query
  })
}

// 查询痛点价值详细
export function getPainpoint(ID) {
  return request({
    url: '/system/scene/painpoint/' + ID,
    method: 'get'
  })
}

// 新增痛点价值
export function addPainpoint(data) {
  return request({
    url: '/system/scene/painpoint',
    method: 'post',
    data: data
  })
}

// 修改痛点价值
export function updatePainpoint(data) {
  return request({
    url: '/system/scene/painpoint',
    method: 'put',
    data: data
  })
}

// 删除痛点价值
export function delPainpoint(ID) {
  return request({
    url: '/system/scene/painpoint/' + ID,
    method: 'delete'
  })
}
