<template>
    <div class="error-component">
      <div class="error-icon">⚠️</div>
      <p>组件加载失败</p>
      <button @click="retry">重试</button>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ErrorComponent',
    methods: {
      retry() {
        this.$emit('retry');
      }
    }
  }
  </script>
  
  <style scoped>
  .error-component {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #e74c3c;
  }
  
  .error-icon {
    font-size: 40px;
    margin-bottom: 10px;
  }
  
  button {
    margin-top: 10px;
    padding: 5px 15px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  </style>