<template>
  <div class="component-upload-image">
    <el-upload class="upload-demo" ref="upload" :action="action" :headers="headers" :on-error="handleError" :on-success="handleUploadSuccess" :on-preview="handlePreview" :on-remove="handleRemove" multiple :limit="maxCount" :on-exceed="handleExceed" :before-upload="beforeUpload" :file-list="fileList" :accept="accept">
      <el-button size="small" type="primary">点击上传</el-button>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "multiUpload",
  props: {
    //图片属性数组
    value: Array,
    //最大上传图片数量
    maxCount: {
      type: Number,
      default: 5
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.doc,.docx,.ppt,.pptx,.txt,.mp4,.pdf'
    }
  },
  watch: {
    value: {
      handler(newName, oldName) {
        this.fileList.splice(0, this.fileList.length);
        newName.forEach(element => {
          this.fileList.push({ name: element.name, url: element.url });
        });
        console.log(this.fileList)
      }, deep: true,
      immediate: true,
    }
  },
  data() {
    return {
      //accept: '.bmp,.gif,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.html,.htm,.txt,.rar,.zip,.gz,.bz2,.mp4,.avi,.rmvb,.pdf',
      //accept: '.jpg,.jpeg,.png,.doc,.docx,.ppt,.pptx,.txt,.mp4,.pdf',
      isFirst: true,
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      action: process.env.VUE_APP_BASE_API + "/system/common/upload",
      //action: "http://***********:9098/common/upload",
    };
  },
  created() {


  },
  computed: {
    fileList: {
      get: function () {
        let fileList = [];
        if (this.length == null || this.length == 0) return [];
        for (let i = 0; i < this.value.length; i++) {
          fileList.push({ url: this.value[i].url, name: this.value[i].name });
        }

        //console.log(fileList);
        return fileList;
      }, set: function (list) {
        return list;
      }
    }
  },
  methods: {
    emitInput(fileList) {
      let value = [];
      for (let i = 0; i < fileList.length; i++) {
        if (fileList[i].url == null) console.log("11111");
        value.push(fileList[i]);
      }
      //console.log(fileList, value);
      this.$emit("input", value);
    },
    handleRemove(file, fileList) {
      this.emitInput(fileList);
    },
    handleError(err, file, fileList) {
      this.$message({
        message: '文件上传出错,请联系管理员' + err,
        type: 'error',
        duration: 3000,
      });
    },
    handlePreview(file) {
      this.dialogVisible = true;
      this.dialogImageUrl = file.url;
    },
    beforeUpload(file) {
      let that = this;
      let fileName = file.name.substring(file.name.lastIndexOf('.'))
      if (that.accept.lastIndexOf(fileName) < 0) {
        this.$message({
          message: '上传文件只能上传' + that.accept + '格式!',
          type: 'error',
          duration: 3000,
        });
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 50;
      if (!isLt2M) {
        this.$message({
          message: "上传文件大小不能超过 50MB!",
          type: "error",
          duration: 3000
        });
        return false;
      }
    },
    handleUploadSuccess(res, file) {
      if (res.code == 200) {
        this.fileList.push({ name: file.name, url: res.data });
        this.emitInput(this.fileList);
      } else {
        this.$message({
          message: res.msg,
          type: "error",
          duration: 3000
        });
      }
    },
    handleExceed(files, fileList) {
      this.$message({
        message: "最多只能上传" + this.maxCount + "个文件",
        type: "warning",
        duration: 1000
      });
    },
    clear() {
      this.$refs.upload.clearFiles()
    }
  }
};
</script>