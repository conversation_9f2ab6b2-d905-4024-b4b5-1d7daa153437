<template>
  <div id="appitem" class="appitem" v-cloak>
    <div class="back" @click="gotoback"></div>

    <div v-if="step == 1 " class="content-box" style="background: rgba(0,0,0,0.9);">

      <div class="title">{{ sendName}}</div>
      <div>
        <div class="content222">
          <div class="left-box">
            <img src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/case.png" alt="" @click="imgShow = true;step = 0" />
            <img src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/sever.png" alt="" @click="isSever = true;step = 0" />
            <img v-if="solutions.qxCaseVoList.length > 0" src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/anli.png" alt="" @click="isAnli = true;step = 0" />
          </div>
          <div class="right-box">
            <div class="right-contet">
              <div class="right-contet-nengli">
                <div class="nengli-box">支撑能力</div>
                <div class="nengli-contet">
                  <div>
                    <!-- border: 15px solid #1c9efd; -->
                    <div class="nengli-contet-title">可支撑人数</div>
                    <el-progress type="circle" :percentage="solutions.number" :width="100" :stroke-width="15" color="#1c9efd" text-color="#fff" :format="formatPercentage"></el-progress>
                  </div>
                  <div>
                    <div class="nengli-contet-title">全国案例数</div>
                    <el-progress type="circle" :percentage="solutions.achieveNumber" :width="100" :stroke-width="15" color="#5493ff" text-color="#fff" :format="formatPercentage"></el-progress>
                  </div>
                </div>
                <div class="nengli-citys">可支撑地市</div>
                <div class="citylist">
                  <div class="cityitem" v-for="(item, index) in solutions.qxDevelopmentCityList" :key="index">
                    <img src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/city-icon.png" alt="" style="width: 61.02px;
height: 36.95px; margin: 0 10px" />
                    <div style="text-align: center;font-size: 18px;">{{ item.label }}</div>
                  </div>
                </div>

                <div class="rigth-btns" v-if=" solutions.qxLinkList.length">
                  <div>友情链接</div>
                  <div>{{ solutions.qxLinkList[0].linkUrl }}</div>
                </div>
                <div class="rigth-btns">
                  <div>联系生态</div>
                  <img src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/phone.png" alt="" style="width: 61px; margin: 0 0 0 56px" />
                  <div style="text-align: left">{{ solutions.phone }}</div>
                </div>
              </div>
              <div class="right-contet-canpin">
                <div class="canpin-box">相关产品</div>
                <div class="canpin-list">
                  <div class="canpin-item" v-for="(item, index) in solutions.demoVos" :key="index">
                    <img :src="item.url" alt="" style="width: 182px; height: 84px; border-radius: 14px; margin: 8px auto 8px auto" />
                    <div>{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="imgbox" v-if="imgShow">
      <img style="width: 100%;height: 100%;" :src="imgUrl" alt="">
    </div>

    <div class="imgbox bg_server_img" v-if="isSever">
      <div class="sever">
        <el-table :data="solutions.qxServiceList" border style="width: 100%; background: transparent">
          <el-table-column prop="serviceType" label="类别">
            <template slot-scope="scope">
              <div style="text-align: center;">
                {{
                                scope.row.serviceType === 0 ? '硬件' :
                                scope.row.serviceType === 1 ? '软件' :
                                scope.row.serviceType === 2 ? '集成服务' : '其他'
                            }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="serviceName" label="名称"></el-table-column>
          <el-table-column prop="serviceExplain" label="功能介绍"></el-table-column>
          <el-table-column prop="serviceNumber" label="数量"></el-table-column>
          <el-table-column prop="serviceUnit" label="单位"></el-table-column>
          <el-table-column prop="servicePrice" label="单价"></el-table-column>
        </el-table>
      </div>
    </div>

    <div class="imgbox bg_server_img" v-if="isAnli">
      <div class="sever1212 sever">
        <div>

          <img v-if="solutions.qxCaseVoList.length > 0" src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/pren.png" alt=""
            style="width: 45.5px; margin: 58px 21.5px 0; height: 39.5px; cursor: pointer;" @click="prenCase()" />
          <div class="content111">
            <div class="content111-box">
              <div class="title111">{{ solutions.qxCaseVoList[caseIndex].caseName }}</div>
              <div class="imgs111" style="position: relative;">
                <div style="float: left;" @mouseover="handleMouseOver($event)" @mouseleave="handleMouseLeave($event)">
                  <img :src="solutions.qxCaseVoList[caseIndex].img" alt="" style="width: 715px; height: 310px" class="adfsdsdfsd" />
                  <div class="anli-hovers" v-show="solutions.qxCaseVoList[caseIndex].show" @click="gotoUrl(solutions.qxCaseVoList[caseIndex].linkUrl)">
                    <img src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/ebianpai.png" alt="" style="width: 64.12px;
height: 70.47px;">
                  </div>
                </div>
                <div class="anli-desc">
                  <div>案例描述</div>
                  <div>
                    {{solutions.qxCaseVoList[caseIndex].caseExplain}}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <img v-if="solutions.qxCaseVoList.length > 1" src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/next.png" alt=""
            style="width: 45.5px; margin: 58px 21.5px 0; height: 39.5px; cursor: pointer;" @click="nextCase()" />
        </div>
        <div class="download-box" v-if="solutions.qxCaseVoList[caseIndex].pdtArr.length">
          <img src="https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/download.png" alt="" style="width: 51px; height: 55px; margin-right: 50px; cursor: pointer" />
          <div class="download" @click="downloadPdt(solutions.qxCaseVoList[caseIndex].pdtArr)">下载</div>
        </div>
      </div>

      <div class="imgbox" v-if="anliiframeShow" style="background: #000;">
        <iframe style="width: 1650px;float: left;
  height: 890px;border: 0;" :src="anliiframeUrl + '?type=1'" frameborder="0"></iframe>
      </div>
    </div>
    <div class="imgbox" v-if="iframeShow" style="background: #000;">
      <iframe style="width: 1650px;float: left;
  height: 890px;border: 0;" :src="iframeUrl + '?type=1'" frameborder="0"></iframe>
    </div>

    <div class="cart-modoul" v-if="ative">
      <img :src="modoulObj.src" alt="">
      <div :style="{'color': modoulObj.color}">{{modoulObj.text}}</div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      previewPage: false,
      loading: false,
      loadingText: "加载中",
      detalis: {},
      province: {},
      qxServiceList: [],
      qxDevelopmentCityList: [],
      qxLinkList: [],
      baseurl: "",
      step: 1,
      sceneId: null,
      industryId: null,
      sceneList: [],
      sceneList1: [],
      imgUrl: "",
      caseIndex: 0,
      qxCaseVoList: [],
      number: "", //全国案例数
      achieveNumber: "", //可支撑人数,
      isshow: false,
      sceneName: "",
      token: localStorage.getItem("token"),
      isAnli: false,
      tilte: "",
      userId: "",
      ative: false,
      modoulObj: {
        src: "",
        text: "",
        color: "",
      },
      cartId: "",
      myheader: "",
      imgShow: false,
      developmentId: "",
      hometips: 0,
      sendtips: 0,
      iframeUrl: "",
      iframeShow: false,
      isSolution: false,
      sendName: "",
      isImg: false,
      solutions: {
        number: 0, //全国案例数
        achieveNumber: 0, //可支撑人数,
        qxDevelopmentCityList: [],
        qxLinkList: [],
        demoVos: [],
        qxCaseVoList: [],
      },
      isSever: false,
      anliiframeShow: false,
      anliiframeUrl: "",
    };
  },
  mounted() {
    const query = {};
    if (query.myheader) this.myheader = query.myheader;
    console.log(query, "7100000000000000000000000000000000");
    this.industryId = query.industryId;
    this.sceneId = query.sceneId;
    this.userId = query.userId;
    this.sceneName = query.sceneName;
    if (this.userId != undefined && this.sceneName != undefined) {
      this.step = 2;
    }
    // else {
    //   this.getsceneList();
    // }
    this.tilte = query.tilte;
    let that = this;
    // that.setScale();
    // window.onresize = function windowResize() {
    //   that.setScale();
    // };
  },
  computed: {},
  methods: {
    gotoUrl(url) {
      if (!url) return;
      this.anliiframeShow = true;
      this.anliiframeUrl = url;
    },

    handleMouseOver(event) {
      event.stopPropagation(); // 阻止事件传播
      if (this.solutions.qxCaseVoList[this.caseIndex].linkUrl) {
        this.solutions.qxCaseVoList[this.caseIndex].show = true;
      }

      this.$forceUpdate();
      console.log(this.solutions.qxCaseVoList[this.caseIndex].show);
    },
    handleMouseLeave(event) {
      event.stopPropagation(); // 阻止事件传播
      this.solutions.qxCaseVoList[this.caseIndex].show = false;
      this.$forceUpdate();
      console.log(this.solutions.qxCaseVoList[this.caseIndex].show);
    },
    gotojicheng() {
      window.location.replace(
        `integratedquotation.html?myheader=${this.myheader}`
      );
    },
    log() {
      console.log(this.qxCaseVoList, this.caseIndex);
    },
    prenCase() {
      if (this.caseIndex > 0) {
        this.caseIndex--;
      }
    },
    nextCase() {
      if (this.caseIndex < this.solutions.qxCaseVoList.length - 1) {
        this.caseIndex++;
      }
    },
    downloadPdt(pptx) {
      pptx.forEach((i) => {
        window.open(i, "_blank");
      });
    },
    formatPercentage(percentage) {
      return `${percentage}`;
    },
    chekiframeUrl(item) {
      if (item.linkUrl) {
        this.iframeUrl = item.linkUrl;
        this.iframeShow = true;
      }
    },

    download(item) {
      item.pdtArr.forEach((i) => {
        window.open(i, "_blank");
        // window.open(item.img,'_blank')
      });

      // top.location.href = item.url
      //
      // console.log(item)
    },
    linkUrl(url) {
      window.open(url, "_blank");
      // top.location.href = url
    },

    gotoback() {
      if (this.anliiframeShow) {
        this.isAnli = true;
        this.step = 0;
        this.anliiframeShow=false;
      } else if (this.step == 0) {
        this.step = 1;
        this.imgShow = false;
        this.isAnli = false;
        this.isSever = false;
      } else {
        this.$emit("change-preview"); // 触发父组件的自定义事件
      }
    },

    setScale() {
      var html = document.getElementsByTagName("html")[0];
      //屏幕的宽度（兼容处理）
      var w = document.documentElement.clientWidth || document.body.clientWidth;
      console.log(w);
      //750这个数字是根据你的设计图的实际大小来的，所以值具体根据设计图的大小
      html.style.fontSize = w / 1920 + "px";
      console.log(html.style.fontSize);
    },
  },
};
</script>


<style lang="scss" scoped>
/* 强制覆盖表头背景色（红色） */
::v-deep .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background-color: #1c9efd !important;
  color: white !important;
}

/* 强制覆盖内容区背景色（蓝色） */
::v-deep .el-table tr {
  background-color: transparent !important;
  color: white !important;
}

/* 移除行悬停效果 */
::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: transparent !important;
}

.appitem {
  float: left;
  margin: 10px;
  width: 1650px;
  height: 890px;
  background: transparent;
  font-size: 28px;
  color: #fff;
}

/* 定义滚动条样式 */
::-webkit-scrollbar {
  width: 5px !important;
  height: 5px;
  background-color: rgba(0, 0, 0, 0);
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  border-radius: 5px;
  background-color: rgba(240, 240, 240, 0.5);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  width: 5px !important;
  height: 5px !important;
  background-color: #fff;
}

.back {
  width: 58px;
  height: 58px;
  position: relative;
  top: 80px;
  left: 2%;
  background: url(https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/imgs/back.png)
    no-repeat center center;
  background-size: 100%;
  z-index: 999999;
  cursor: pointer;
}

.my-header {
  width: 100%;
  height: 50px;
  background: rgba(0, 0, 0, 0.55);
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  padding: 0 2.68%;
  color: #fff;
  box-sizing: border-box;
}

.tltie {
  font-size: 35px;
  color: #fff;
  font-weight: bold;
}

.nengli {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.content {
  margin-top: 56px;
  font-size: 22px;
  color: #fff;
  height: 214px;
  overflow-y: auto;
}

::-webkit-scrollbar {
  width: 6px;
  /* height: 6px; */
  background-color: rgba(0, 0, 0, 0);
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  border-radius: 10px;
  background-color: rgba(240, 240, 240, 0.5);
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 0px rgba(240, 240, 240, 0.5);
  width: 6px;
  height: 6px;
  background-color: #fff;
}

.nengli > div {
  color: #1693ee;
  padding: 5px 10px;
  border-radius: 30px;
  font-size: 18px;
  border: 1px solid #1693ee;
  margin-right: 10px;
}

#app {
  position: relative;
  background-image: url(https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/imgs/index/home_bg.jpg);

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  zoom: 1;
  background-repeat: no-repeat;
  background-size: cover;
  background-size: 100%;
  -webkit-background-size: cover;
  -o-background-size: cover;
  background-position: center 0;
  font-size: 28px;
  color: #fff;
  /* overflow: hidden; */
  /* overflow-y: scroll; */
}

.content-box {
  background-color: rgba(0, 0, 0, 0.75);
  width: 1650px;
  height: 890px;
  float: left;
  position: absolute;
  top: 10px;
  overflow: hidden;
}

.bg_server_img {
  width: 1920px;
  height: 1080px;
  transform-origin: 0 0;
  /* background-color: rgba(0, 0, 0, 0); */
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  background: url(https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/sever-bg.png)
    no-repeat center center;
  background-size: 100%;
}

.title {
  width: 1650px;
  height: 30px;
  line-height: 30px;
  margin-top: 45px;
  color: #0094ff;
  font-weight: bold;
  font-family: "FZLanTingHeiS-B-GB";
  font-size: 30px;
  text-align: center;
}

.content222 {
  width: 1650px;
  height: 800px;
  margin-top: 30px;
  float: left;
}

.left-box {
  width: 525px;
  height: 730px;
  float: left;
  margin-left: 103px;
  border-radius: 24.06px;
  background: linear-gradient(180deg, #ffffff1a 0%, #ffffff21 100%);
  border: 3.44px solid #ffffff29;
  box-shadow: 0 2.58px 5.16px #00000002;
}

.right-box {
  width: 807px;
  height: 730px;
  border-radius: 15px;
  margin-left: 100px;
  background: #1c1f37;
  border: 3.44px solid #ffffff33;
  box-shadow: 0 2.58px 12.89px #00000029;
  box-sizing: border-box;
  padding: 36px;
  float: left;
}

.right-contet {
  width: 728px;
  height: 730px;
  float: left;
}

.right-contet-nengli {
  width: 460.63px;
  float: left;
}

.right-contet-canpin {
  width: 198px;
  float: left;
  margin-left: 60px;
}

.nengli-box {
  width: 100%;
  text-align: center;
  border-bottom: 2px solid #6a6a6a;
  line-height: 40px;
  color: #1c9efd;
  font-size: 18px;
  font-weight: 400;
}

.nengli-contet {
  display: flex;
}

.nengli-contet > div {
  width: 50%;
  text-align: center;
}

.nengli-contet-title {
  margin: 30px 0 20px 0;
  font-size: 20px;
}

.nengli-citys {
  margin: 35px 0;
  text-align: center;
  font-weight: 400;
  font-size: 18px;
  color: #fff;
}

.citylist {
  width: 95%;
  margin: 0 auto 60px auto;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-content: flex-start;
}

.cityitem {
  text-align: center;
  margin: auto 4px;
  white-space: nowrap;
}

.rigth-btns {
  height: 47px;
  border-radius: 12px;
  background: linear-gradient(180deg, #ffffff1f 0%, #ffffff17 100%);
  border: 2px solid #ffffff1f;
  box-shadow: 0 3px 6px #00000029;
  width: 100%;
  margin-bottom: 26px;
  display: flex;
  align-items: center;
  font-size: 18px;
  /*  */
}

.rigth-btns > div:first-child {
  width: 136px;
  height: 48px;
  border-radius: 12px;
  background: #1c9efd;
  box-shadow: 0 3px 6px #00000029;
  text-align: center;
  line-height: 48px;
  /* 弹性盒子不被挤压 */
  flex-shrink: 0;
}

.rigth-btns > div:last-child {
  flex: 1;
  text-align: center;
  line-height: 48px;
  cursor: pointer;
  /* 最对显示2行文字 */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  width: 329px;
  overflow: hidden;
}

.canpin-box {
  width: 100%;
  text-align: center;
  border-bottom: 2px solid #6a6a6a;
  line-height: 40px;
  color: #1c9efd;
  font-size: 20px;
}

.canpin-list {
  height: 560px;
  overflow-y: auto;
  overflow-x: hidden;
  margin-top: 30px;
}

.canpin-list::-webkit-scrollbar {
  display: none;
}

.canpin-item {
  width: 198px;
  height: 126px;
  border-radius: 16px;
  background: linear-gradient(180deg, #ffffff29 0%, #ffffff21 100%);
  border: 1px solid #ffffff1a;
  margin-bottom: 13px;
  text-align: center;
  font-size: 12px;
}

.right-btns {
  display: flex;
  margin-top: 22px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.right-btns > div {
  width: 443px;
  height: 55px;
  border-radius: 13px;
  background: #1c9efd;
  box-shadow: 0 3px 6px #00000029;
  text-align: center;
  line-height: 55px;
  font-size: 20px;
  cursor: pointer;
}

.left-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.left-box img {
  width: 436.56px;
  height: 181.33px;
  float: left;
  cursor: pointer;
}

[v-cloak] {
  display: none !important;
}

.imgbox {
  width: 1650px;
  height: 890px;
  position: absolute;
  margin: 10px;
  top: 0;
  left: 0;
  z-index: 9999;
  overflow: hidden;
  background-color: #0f1225;
}

.sever {
  width: 1518px;
  height: 723px;
  border-radius: 29px;
  background: linear-gradient(180deg, #ffffff1c 0%, #ffffff1c 100%);
  border: 4px solid #ffffff33;
  box-shadow: 0 3px 15px #00000029;
  margin: 156px auto 0;
  padding: 66px;
  box-sizing: border-box;
  overflow-y: auto;
}

.sever1212 > div {
  display: flex;
  align-items: center;
}

.sever1212 {
  padding: 0;
}

.download-box {
  margin-top: 50px;
  justify-content: center;
  display: flex;
}

.content111 {
  margin-top: 80px;
  width: 100%;
}

.content111-box {
  width: 1350px;
  height: 505px;
  border-radius: 28px;

  background: linear-gradient(180deg, #ffffff1c 0%, #ffffff1c 100%);
  border: 3.44px solid #ffffff33;
  opacity: 0.52;
  box-shadow: 0 2.58px 12.89px #00000029;
  padding: 41px 35px;
  box-sizing: border-box;
  float: left;
  margin-left: 10px;
}

.title111 {
  width: 1265px;
  height: 48px;
  background: #1c9efd;
  text-align: center;
  line-height: 48px;
  font-size: 18px;
  font-weight: bold;
}

.imgs111 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 61px;
}

.anli-desc {
  width: 540px;
  font-size: 15.5px;
  margin-left: 100px;
  float: left;
  color: #fff;
}

.anli-desc > div:first-child {
  color: #1c9efd;
}

.anli-desc > div:last-child {
  height: 320px;
  overflow-y: auto;
}

.download {
  width: 236px;
  height: 48px;
  line-height: 48px;
  text-align: center;
  background: #1c9efd;
  border-radius: 8px;
  cursor: pointer;
}

.el-table tr {
  background: transparent !important;
  color: #fff !important;
}

.has-gutter tr th {
  background: #1c9efd !important;
  color: #fff !important;
  text-align: center !important;
}

.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #000 !important;
  /* 修改为需要的背景颜色 */
  color: #fff !important;
  /* 如果需要修改文字颜色 */
}

.el-progress path:first-child {
  stroke: #606577 !important;
}

.el-progress__text {
  font-size: 40px !important;
}

.el-carousel__container {
  height: 100% !important;
}

.el-icon-arrow-left {
  background: url(https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/pren.png)
    no-repeat center center !important;
  width: 46px !important;
  height: 46px !important;
  background-size: 100% !important;
}

.el-icon-arrow-right {
  background: url(https://ishow-bucket-to.eos-beijing-2.cmecloud.cn/H5WEBGL/panovr/11yejin/images/send/next.png)
    no-repeat center center !important;
  width: 46px !important;
  height: 46px !important;
  background-size: 100% !important;
}

.el-icon-arrow-left::before,
.el-icon-arrow-right::before {
  content: "" !important;
}

.cart-modoul {
  position: fixed;
  top: 36%;
  left: 36%;
  width: 600px;
  height: 280px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 5px solid #447de0;

  text-align: center;
}

.cart-modoul img {
  width: 72px;
  height: 72px;

  padding-top: 76px;
  padding-bottom: 24px;
  text-align: center;
}

.adfsdsdfsd {
  cursor: pointer;
}

.adfsdsdfsd + .anli-hovers {
  position: absolute;
  top: 0;
  left: 0;
  width: 715px;
  height: 330px;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  /* border-radius: 20px; */
}

.img_icon + span {
  display: none;
}

.img_icon {
  cursor: pointer;
}

.img_icon:hover + span {
  display: block;
}
</style>