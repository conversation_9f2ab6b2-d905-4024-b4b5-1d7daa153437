import request from '@/utils/request'

// 查询场景默认项列表
export function listDefault(query) {
  return request({
    url: '/system/default/list',
    method: 'get',
    params: query
  })
}

// 查询场景默认项详细
export function getDefault(id) {
  return request({
    url: '/system/default/' + id,
    method: 'get'
  })
}

// 新增场景默认项
export function addDefault(data) {
  return request({
    url: '/system/default',
    method: 'post',
    data: data
  })
}

// 修改场景默认项
export function updateDefault(data) {
  return request({
    url: '/system/default',
    method: 'put',
    data: data
  })
}

// 删除场景默认项
export function delDefault(id) {
  return request({
    url: '/system/default/' + id,
    method: 'delete'
  })
}

// 导出场景默认项
export function exportDefault(query) {
  return request({
    url: '/system/default/export',
    method: 'get',
    params: query
  })
}