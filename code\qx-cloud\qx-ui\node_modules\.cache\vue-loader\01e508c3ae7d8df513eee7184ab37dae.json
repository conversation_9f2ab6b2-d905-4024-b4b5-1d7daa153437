{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\popular\\index.vue?vue&type=template&id=71a09f56", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\popular\\index.vue", "mtime": 1755845996588}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1747036193477}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747036191888}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}