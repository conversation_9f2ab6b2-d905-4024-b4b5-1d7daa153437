import request from '@/utils/request'

// 查询内容审核列表
export function listContentexamine(query) {
  return request({
    url: '/system/examine/contentexamine/list',
    method: 'get',
    params: query
  })
}

// 查询内容审核详细
export function getContentexamine(id) {
  return request({
    url: '/system/examine/contentexamine/' + id,
    method: 'get'
  })
}

// 新增内容审核
export function addContentexamine(data) {
  return request({
    url: '/system/examine/contentexamine',
    method: 'post',
    data: data
  })
}

export function getExamine(data) {
  return request({
    url: '/system/examine/contentexamine/getexamine',
    method: 'post',
    data: data,
    timeout: 1000*60*10
    
  })
}



// 修改内容审核
export function updateContentexamine(data) {
  return request({
    url: '/system/examine/contentexamine',
    method: 'put',
    data: data
  })
}

// 删除内容审核
export function delContentexamine(id) {
  return request({
    url: '/system/examine/contentexamine/' + id,
    method: 'delete'
  })
}
