import request from '@/utils/request'

// 金库取消导出
export function cencelExport() {
    return request({
      url: '/system/treasury/cencelexport',
      method: 'post',
      timeout: 1000 * 60 * 3,
    })
}


// 金库取消导出
export function getApprover(data) {
  return request({
    url: '/system/treasury/getapprover',
    method: 'post',
    data: data,
    timeout: 1000 * 60 * 3,
  })
}

export function getTreasuryCode(data) {
  return request({
    url: '/system/treasury/getcode',
    method: 'post',
    data: data,
    timeout: 1000 * 60 * 3,
  })
}

export function sendTreasuryCode(data) {
  return request({
    url: '/system/treasury/checkcode',
    method: 'post',
    data: data,
    timeout: 1000 * 60 * 3,
  })
}

export function cancelSmsCode() {
  return request({
    url: '/system/treasury/cancel',
    method: 'post',
    timeout: 1000 * 60 * 3,
  })
}
