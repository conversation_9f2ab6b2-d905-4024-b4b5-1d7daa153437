import request from '@/utils/request'

// 查询用户案例列表
export function listMain(query) {
  return request({
    url: '/system/main/list',
    method: 'get',
    params: query
  })
}

// 查询用户案例详细
export function getMain(mainCaseId) {
  return request({
    url: '/system/main/' + mainCaseId,
    method: 'get'
  })
}

// 新增用户案例
export function addMain(data) {
  return request({
    url: '/system/main',
    method: 'post',
    data: data
  })
}

// 修改用户案例
export function updateMain(data) {
  return request({
    url: '/system/main',
    method: 'put',
    data: data
  })
}

// 删除用户案例
export function delMain(mainCaseId) {
  return request({
    url: '/system/main/' + mainCaseId,
    method: 'delete'
  })
}

// 导出用户案例
export function exportMain(query) {
  return request({
    url: '/system/main/export',
    method: 'get',
    params: query
  })
}