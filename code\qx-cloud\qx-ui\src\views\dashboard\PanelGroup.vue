<template>
    <div>
        <el-row :gutter="40" class="panel-group" style="margin: 0;">
            <!-- 左侧的访问总数部分 -->
            <el-col :xs="4" :sm="4" :lg="4" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == -1 }" style="margin-left: 3px;padding: 0;">
                <div class="card-panel" @click="handleSetLineChartData(-1)">
                    <div>
                        <img src="@/assets/images/index/icon_0.png" alt="" style="width: 66px;height: auto;" />
                    </div>
                    <div class="card-panel-description">
                        <div class="card-panel-text">
                            访问总数
                        </div>
                        <count-to :start-val="0" :end-val="options[0] + options[1] + options[2] + options[4] + options[5] + options[6] + options[7] + options[8] + options[9] + options[10] + options[11]+ options[12]+ options[13]" :duration="2600" class="card-panel-num" />
                    </div>
                </div>
            </el-col>

            <!-- 右侧的部分分成两排 -->
            <el-col :xs="24" :sm="24" :lg="20">
                <el-row :gutter="40">
                    <!-- 第一排 -->
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 2 }">
                        <div class="card-panel" @click="handleSetLineChartData(2)">
                            <!-- <div :style="selectNum == 2 ? 'background:#0082fc;color: #fff;' : ''" class="card-panel-icon-wrapper icon-message">
                                <svg-icon icon-class="peoples" class-name="card-panel-icon" />
                            </div> -->
                            <div>
                                <img src="@/assets/images/index/icon_2.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[2] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[2]" :duration="3000" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 4 }">
                        <div class="card-panel" @click="handleSetLineChartData(4)">
                            <div>
                                <img src="@/assets/images/index/icon_4.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[4] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[4]" :duration="3200" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 5 }">
                        <div class="card-panel" @click="handleSetLineChartData(5)">
                            <div>
                                <img src="@/assets/images/index/icon_5.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[5] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[5]" :duration="3600" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 6 }">
                        <div class="card-panel" @click="handleSetLineChartData(6)">
                            <div>
                                <img src="@/assets/images/index/icon_6.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[6] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[6]" :duration="2600" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 7 }">
                        <div class="card-panel" @click="handleSetLineChartData(7)">
                            <div>
                                <img src="@/assets/images/index/icon_7.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[7] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[7]" :duration="3000" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                </el-row>
                <el-row :gutter="40">
                    <!-- 第二排 -->
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 8 }">
                        <div class="card-panel" @click="handleSetLineChartData(8)">
                            <div>
                                <img src="@/assets/images/index/icon_8.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[8] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[8]" :duration="3200" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 9 }">
                        <div class="card-panel" @click="handleSetLineChartData(9)">
                            <div>
                                <img src="@/assets/images/index/icon_9.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[9] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[9]" :duration="3600" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 10 }">
                        <div class="card-panel" @click="handleSetLineChartData(10)">
                            <div>
                                <img src="@/assets/images/index/icon_10.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[10] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[10]" :duration="2600" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 12 }">
                        <div class="card-panel" @click="handleSetLineChartData(12)">
                            <div>
                                <img src="@/assets/images/index/icon_12.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    {{ titleOptions[12] }}
                                </div>
                                <count-to :start-val="0" :end-val="options[12]" :duration="2600" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                    <el-col :xs="12" :sm="12" :lg="5" class="card-panel-col" :class="{ 'card-panel-col-hover': selectNum == 13 }">
                        <div class="card-panel"  @click="handleSetLineChartData(13)">
                            <div>
                                <img src="@/assets/images/index/icon_13.png" alt="" style="width: 56px;height: auto;" />
                            </div>
                            <div class="card-panel-description">
                                <div class="card-panel-text">
                                    <!-- {{ titleOptions[13] }} -->
                                      其它访客
                                </div>
                                <count-to :start-val="0" :end-val="options[0]+options[1]+options[11]+options[13]" :duration="2600" class="card-panel-num" />
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import CountTo from 'vue-count-to'

export default {
    components: {
        CountTo
    },

    props: {
        titleOptions: {
            type: Array,
            default: () => []
        },
        options: {
            type: Array,
            default: () => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        dataOptionsImage: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            selectNum: -1,
            sumNum: 0,
            avg: 0
        }
    },
    mounted() {
        // console.log(this.dataOptionsImage)
        // this.options.forEach(element => {
        //   this.sumNum += element;
        // });
        //  this.sumNum =;
    },
    created() {
        this.$nextTick(() => {
            this.options.forEach(element => {
                this.sumNum += element
            })
        })
    },
    methods: {
        handleSetLineChartData(type) {
            this.selectNum = type
            this.$emit('handleSetLineChartData', type)
        }
    }
}
</script>

<style lang="scss" scoped>
.panel-group {
    display: flex;
    align-items: center;
    .card-panel-col {
        margin-bottom: 16px;
        margin-right: 2%;
        background: #ffffff;
        box-shadow: 0 2px 2px 0 #d5dde3;
        border-radius: 4px;
        border: 3px solid transparent;
        &:hover {
            border: 3px solid #175fff;
            box-shadow: 0 4px 6px 0 #d5dde3;
        }
    }
    .card-panel-col-hover {
        border: 3px solid #175fff;
        box-shadow: 0 4px 6px 0 #d5dde3;
    }
    .el-col-lg-4-8 {
        width: 35%;
    }

    .el-col-lg-5 {
        width: 17.888%;
    }
    .card-panel {
        height: 108px;
        cursor: pointer;
        font-size: 12px;
        position: relative;
        overflow: hidden;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: space-around;
        // background: #fff;
        // box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
        // border-color: rgba(0, 0, 0, 0.05);

        .card-panel-icon-wrapper {
            float: left;
            margin: 14px 0 0 14px;
            padding: 16px;
            transition: all 0.38s ease-out;
            border-radius: 6px;
        }

        .card-panel-icon {
            float: left;
            font-size: 48px;
        }

        .card-panel-description {
            font-weight: bold;
            text-align: right;
            .card-panel-text {
                line-height: 18px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 16px;
                margin-bottom: 12px;
            }

            .card-panel-num {
                font-size: 20px;
            }
        }
    }
}

@media (max-width: 550px) {
    .card-panel-description {
        display: none;
    }

    .card-panel-icon-wrapper {
        float: none !important;
        width: 100%;
        height: 100%;
        margin: 0 !important;

        .svg-icon {
            display: block;
            margin: 14px auto !important;
            float: none !important;
        }
    }
}
</style>
