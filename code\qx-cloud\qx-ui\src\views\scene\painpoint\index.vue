<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业场景" prop="sceneIds">
        <el-cascader v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear">
        </el-cascader>
      </el-form-item>

      <el-form-item label="标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="内容" prop="valueContent">
        <el-input v-model="queryParams.valueContent" placeholder="请输入标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['scene:painpoint:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['scene:painpoint:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['scene:painpoint:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['scene:painpoint:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="painpointList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="成本预估内容" align="center" prop="valueContentTo">
        <template slot-scope="scope">
          <div v-for="(item, index) in scope.row.valueContentTo" :key="index" style="text-align: left;">
            {{ item }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{s}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['scene:painpoint:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['scene:painpoint:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改痛点价值对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题" show-word-limit maxlength="100" />
        </el-form-item>
        <el-form-item label="行业场景" prop="sceneIds">
          <el-cascader v-model="form.sceneIds" :options="options" @change="handleChange"></el-cascader>
        </el-form-item>
        <el-form-item label="成本预估内容" prop="valueContentTo">

          <div class="dynamic-input-container">
            <el-button type="primary" icon="el-icon-plus" @click="addInput" size="mini" class="add-button">
              添加内容项
            </el-button>
            <label>（字数总数不能超过1000并且不能超过10项）</label>
            <div v-for="(item, index) in form.valueContentTo" :key="index" class="input-item">
              <el-input v-model="form.valueContentTo[index]" placeholder="请输入成本预估内容" :rows="2" class="dynamic-input" show-word-limit maxlength="500"></el-input>
              <el-button type="danger" icon="el-icon-delete" circle @click="removeInput(index)" size="mini" class="delete-button"></el-button>
            </div>
          </div>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import timeSelect from "../../../assets/images/add.png";

import {
  listPainpoint,
  getPainpoint,
  delPainpoint,
  addPainpoint,
  updatePainpoint,
} from "@/api/scene/painpoint";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";

export default {
  name: "Painpoint",
  data() {
    return {
      timeSelect,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 痛点价值表格数据
      painpointList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        sceneCode: null,
        industryCode: null,
        valueContent: null,
        valueContentTo: [""],
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sceneIds: [
          { required: true, message: "场景不能为空", trigger: "change" },
        ],
        title: [{ required: true, message: "标题不能为空", trigger: "change" }],
        valueContentTo: [
          {
            validator: (rule, value, callback) => {
              // 检查是否为空
              if (!value || value.length === 0) {
                return callback(new Error("至少需要一项成本预估内容"));
              }

              // 检查每项是否为空
              if (value.some((item) => !item || item.trim() === "")) {
                return callback(new Error("成本预估内容不能为空"));
              }

              // 计算总字数
              const totalChars = value.reduce(
                (sum, item) => sum + item.length,
                0
              );

              // 检查总字数
              if (totalChars > 1000) {
                return callback(new Error("成本预估内容总字数不能超过1000字"));
              }

              // 所有验证通过
              callback();
            },
            trigger: "blur",
          },
        ],
      },
      //行业列表
      industryList: [],
      //场景列表
      sceneList: [],
      options: [],
    };
  },
  created() {
    listIndustry().then((response) => {
      this.industryList = response.rows;
      listScene().then((response) => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });
    this.getList();
  },
  methods: {
    addInput() {
      if (
        this.form.valueContentTo != null &&
        this.form.valueContentTo.length < 11
      ) {
        this.form.valueContentTo.push("");
      }
    },
    removeInput(index) {
      this.$set(this.form, "valueContentTo", [
        ...this.form.valueContentTo.slice(0, index),
        ...this.form.valueContentTo.slice(index + 1),
      ]);
    },
    //选中
    handleChange(value) {
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach((row) => {
        if (row.id === value[1]) {
          this.form.sceneCode = row.sceneCode;
        }
      });

      this.industryList.forEach((row) => {
        if (row.id === value[0]) {
          this.form.industryCode = row.industryCode;
        }
      });
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach((element) => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: [],
        };
        this.sceneList.forEach((row) => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName,
            });
          }
        });
        this.options.push(item);
      });
    },
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.industryList.forEach((row) => {
          if (row.id === value[0]) {
            this.queryParams.industryCode = row.industryCode;
          }
        });
      }

      if (value != null || value.length > 1) {
        this.sceneList.forEach((row) => {
          if (row.id === value[1]) {
            this.queryParams.sceneCode = row.sceneCode;
          }
        });
      }
    },
    /** 查询痛点价值列表 */
    getList() {
      this.loading = true;
      listPainpoint(this.queryParams).then((response) => {
        this.painpointList = response.rows;
        this.painpointList = response.rows.map((item) => {
          return {
            ...item,
            valueContentTo: item.valueContent
              ? JSON.parse(item.valueContent)
              : [],
          };
        });
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        sceneCode: null,
        industryCode: null,
        valueContent: null,
        valueContentTo: [""],
        createBy: null,
        createTime: null,
        delFlag: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.industryCode="";
      this.queryParams.sceneCode="";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加痛点价值";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPainpoint(id).then((response) => {
        // 在 industryList 中查找 industryId
        let industryId = null;
        let sceneId = null;

        this.industryList.forEach((item) => {
          if (item.industryCode === response.data.industryCode) {
            industryId = item.id;
          }
        });

        // 在 sceneList 中查找 sceneId
        this.sceneList.forEach((item) => {
          if (item.sceneCode === response.data.sceneCode) {
            sceneId = item.id;
          }
        });

        this.form = {
          ...response.data,
          sceneIds: [industryId, sceneId],
          valueContentTo: response.data.valueContent
            ? JSON.parse(response.data.valueContent)
            : [],
        };
        this.open = true;
        this.title = "修改痛点价值";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.valueContent = JSON.stringify(this.form.valueContentTo);
          if (this.form.id != null) {
            updatePainpoint(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPainpoint(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      console.log(ids);
      const names = this.painpointList
        .filter((item) => ids.includes(item.id))
        .map((item) => item.title)
        .join(", ");
      this.$confirm(
        '是否确认删除痛点价值标题为"' + names + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delPainpoint(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/painpoint/export",
        {
          ...this.queryParams,
        },
        `painpoint_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped>
.dynamic-input-container {
  width: 100%;
}

.add-button {
  margin-bottom: 10px;
}

.input-item {
  display: flex;
  align-items: flex-start; /* 顶部对齐 */
  margin-bottom: 10px;
}

.dynamic-input {
  flex: 1;
  margin-right: 10px;
}

.delete-button {
  flex-shrink: 0;
  margin-top: 4px; /* 微调按钮位置 */
}
</style>