<template>
    <div class="app-container">
        <div class="classftion-right-big-tltle">
            <img src="@/assets/images/index/biaoti.png" alt="" />
            <div>用户活跃度</div>
            <div class="more" @click="handleMoreClick('user-brisk')">查看更多</div>
        </div>
        <div class="classftion-right-small-box">
            <div class="classftion-right-small-tltle">
                <div class="classftion-right-small-tltle-sub">
                    <div class="classftion-right-small-tltle-sub-left">日活跃度</div>
                    <div class="classftion-right-small-tltle-sub-left1">(7天)</div>
                    <div class="classftion-right-small-tltle-sub-left2">人次</div>
                    <div class="classftion-right-small-tltle-sub-left3">人数</div>
                </div>

                <div class="classftion-right-small-list2222" v-if="dayDateList.length">
                    <div v-for="(item, index) in dayDateList" :key="index">
                        <div>{{ item.dateStr }}</div>
                        <div class="custom-progress ">
                            <div class="progress-bar first-bar"
                                :style="{ width: calculateWidth(item.personOrder, item.personNumber + item.personOrder) + '%', backgroundColor: '#bedbff' }">
                                {{ item.personOrder }}</div>
                            <div class="progress-bar second-bar" :style="{ width: calculateWidth(item.personNumber, item.personNumber + item.personOrder) + '%', backgroundColor: '#3470ff' }">{{ item.personNumber }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="classftion-right-small-tltle">
                <div class="classftion-right-small-tltle-sub">
                    <div class="classftion-right-small-tltle-sub-left">月活跃度</div>
                    <div class="classftion-right-small-tltle-sub-left1">6个月</div>
                    <div class="classftion-right-small-tltle-sub-left2">人次</div>
                    <div class="classftion-right-small-tltle-sub-left3">人数</div>
                </div>

                <div class="classftion-right-small-list2222" v-if="dayDateMonthList.length">
                    <div v-for="(item, index) in dayDateMonthList" :key="index">
                        <div>{{ item.dateStr }}</div>
                        <div class="custom-progress ">
                            <div class="progress-bar first-bar"
                                :style="{ width: calculateWidth(item.personOrder, item.personNumber + item.personOrder) + '%', backgroundColor: '#bedbff' }">
                                {{ item.personOrder }}</div>
                            <div class="progress-bar second-bar" :style="{ width: calculateWidth(item.personNumber, item.personNumber + item.personOrder) + '%', backgroundColor: '#3470ff' }">{{ item.personNumber }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
// import LineBriskChart from '../../dashboard/LineBriskChart'
import { datesum, monthsum } from '@/api/system/sum.js'

export default {
    name: 'userBrisk',
    components: {
        // LineBriskChart
    },
    data() {
        return {
            isShow: true,
            echartMapYData: [33, 34, 32, 36, 31, 38, 39, 40],
            echartNumYData: [8, 9, 7, 6.5, 11, 8, 10, 11],
            echartMapXData: ['2020/05/05', '2020/09/05', '2020/05/05', '2020/04/05', '2020/03/05', '2020/02/05', '2020/01/05', '2020/06/05'],

            queryParams: {},

            dayDate: [],
            dayDateList: [],

            dayDateMonth: [],
            dayDateMonthList: [],

            list1: []
        }
    },
    // props: {
    //     dayDate: {
    //         type: Array,
    //         default: []
    //     }
    // },
    // watch: {
    //     // 监听 Date 数组的变化
    //     dayDate: {
    //         handler() {
    //             this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
    //             this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
    //             this.getIndustryData()
    //         },
    //         // immediate: true, // 初始化时立即执行一次
    //         deep: true // 深度监听
    //     }
    // },
    created() {
        const end = new Date()
        const start = new Date()
        end.setTime(end.getTime() - 3600 * 1000 * 24)
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        this.dayDate = [start, end]

        const m_end = new Date()
        const m_start = new Date()
        m_start.setMonth(m_start.getMonth() - 6)
        this.dayDateMonth = [m_start, m_end]

        setScale()
        window.onresize = () => {
            setScale()
        }
        this.getDaySum()
        this.getMonthSum()
    },

    methods: {
        handleMoreClick(url) {
            this.$router.push('indexDetail/' + url)
        },
        calculateWidth(value, total) {
            if (total === 0) return 0
            return ((value / total) * 100).toFixed(2)
        },
        //切换图表列表
        titleChange() {
            this.isShow = !this.isShow
        },
        MobileChange() {
            this.getDaySum()
            this.getMonthSum()
        },
        downClick(name) {
            let that = this
            that.$refs[name].focus()
        },
        getDaySum() {
            let that = this
            this.queryParams.params = {}
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            datesum(this.queryParams).then(res => {
                if (res.code == 200) {
                    that.dayDateList = res.data.reverse()
                    console.log(that.dayDateList, 'dayDateListdayDateListdayDateListdayDateListdayDateList')
                    // that.echartMapYData = []
                    // that.echartNumYData = []
                    // that.echartMapXData = []
                    // that.dayDateList.forEach(item => {
                    //     that.echartMapYData.push(item.personOrder)
                    //     that.echartNumYData.push(item.personNumber)
                    //     that.echartMapXData.push(item.dateStr)
                    // })
                    // that.$refs['LineBriskChartDays'].initChart(that.echartMapXData, that.echartMapYData, that.echartNumYData)
                }
            })
        },
        getMonthSum() {
            let that = this
            this.queryParams.params = {}
            if (this.dayDateMonth.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDateMonth[0], '{y}-{m}')
                this.queryParams.endDate = this.parseTime(this.dayDateMonth[1], '{y}-{m}')
            }

            monthsum(this.queryParams).then(res => {
                if (res.code == 200) {
                    that.dayDateMonthList = res.data.reverse()
                    console.log(that.dayDateMonthList, 'dayDateMonthListdayDateMonthListdayDateMonthListdayDateMonthList')
                    // let echartMapYData = []
                    // let echartNumYData = []
                    // let echartMapXData = []
                    // that.dayDateMonthList.forEach(item => {
                    //     echartMapYData.push(item.personOrder)
                    //     echartNumYData.push(item.personNumber)
                    //     echartMapXData.push(item.dateStr)
                    // })
                    // that.$refs['LineBriskChartMonth'].initChart(echartMapXData, echartMapYData, echartNumYData)
                }
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    margin-left: 46px;

    .classftion-right-big-tltle {
        font-size: 24px;
        font-weight: 900;
        color: #0066e4;
        display: flex;
        align-items: center;
        margin: 20px 0;

        img {
            width: 18px;
            height: 24px;
            margin-right: 16px;
        }

        .more {
            flex: 1;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
            position: relative;
            padding-right: 10px;
            cursor: pointer;
        }

        .more::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            border-top: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            border-right: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            transform: translateY(-50%) rotate(45deg);
        }
    }

    .classftion-right-small-box {
        display: flex;

        >div:first-child {
            margin-right: 20px;
        }

        .classftion-right-small-tltle {
            width: 445px;
            height: 460px;
            border-radius: 10px;
            opacity: 1;
            padding: 22px;
            box-sizing: border-box;
            background: #ffffff;

            &-sub {
                display: flex;
                font-size: 12px;
                align-items: center;

                &-left {
                    font-size: 20px;
                }

                &-left1 {
                    margin-right: 30%;
                }

                &-left2 {
                    padding-left: 20px;
                    position: relative;
                }

                &-left2::after {
                    content: '';
                    position: absolute;
                    top: 30%;
                    left: 0;
                    width: 10px;
                    height: 10px;
                    background: #bedbff;
                    border-radius: 50%;
                }

                &-left3 {
                    padding-left: 20px;
                    position: relative;
                    margin-left: 20px;
                }

                &-left3::after {
                    content: '';
                    position: absolute;
                    top: 30%;
                    left: 0;
                    width: 10px;
                    height: 10px;
                    background: #3470ff;
                    border-radius: 50%;
                }
            }
        }
    }
}

.custom-progress {
    position: relative;
    width: 70%;
    height: 30px;
    /* 进度条的高度 */
    background-color: #e5e5e5;
    /* 进度条的背景色 */
    border-radius: 30px;
    line-height: 30px;
    display: flex;
    overflow: hidden;
}

// .progress-bar {
//     position: absolute;
//     top: 0;
//     left: 0;
//     height: 100%;
//     text-align: center;
//     border-radius: 30px;
// }
.progress-bar {
    height: 100%;
    text-align: center;
    line-height: 30px;
    color: white;
    font-weight: bold;
    // border-radius: 30px;
}

.classftion-right-small-list2222>div {
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: space-between;
    font-size: 15px !important;
}

.classftion-right-small-list2222>div {
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: space-between;
}

.custom-progress {
    position: relative;
    width: 80%;
    height: 22px;
    background-color: #f0f0f0;
    border-radius: 22px;
    overflow: hidden;
    display: flex;
}

.progress-bar {
    height: 100%;
    text-align: center;
    line-height: 22px;
    color: white;
    font-weight: bold;
    position: absolute;
    top: 0;
    left: 0;
}

.first-bar {
    border-top-left-radius: 22px;
    border-bottom-left-radius: 22px;
    text-align: right;
    color: #2269de;
}

.second-bar {
    border-top-right-radius: 22px;
    border-bottom-right-radius: 22px;
    // text-align: left;
}
</style>
