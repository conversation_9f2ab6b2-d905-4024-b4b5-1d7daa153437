<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键词"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点击量" prop="clickCount">
        <el-input
          v-model="queryParams.clickCount"
          placeholder="请输入点击量"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="终端" prop="terminal">
        <el-select v-model="queryParams.terminal" placeholder="请选择终端" clearable size="small">
          <el-option label="全部" :value="0" />
          <el-option label="PC端" :value="1" />
          <el-option label="智库APP" :value="2" />
          <el-option label="移动端办公APP" :value="3" />
          <el-option label="网大" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="searchLogList">
      <el-table-column label="关键词" align="center" prop="keyword" />
      <el-table-column label="点击量" align="center" prop="clickCount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
          >详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="点击详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-form :model="detailQueryParams" ref="detailQueryForm" :inline="true" label-width="80px">
        <el-form-item label="关键词">
          <span>{{ currentKeyword }}</span>
        </el-form-item>
        <el-form-item label="终端" prop="terminal">
          <el-select v-model="detailQueryParams.terminal" placeholder="请选择终端" size="small" @change="getDetailList">
            <el-option label="全部" :value="0" />
            <el-option label="PC端" :value="1" />
            <el-option label="智库APP" :value="2" />
            <el-option label="移动端办公APP" :value="3" />
            <el-option label="网大" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            v-model="detailDateRange"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="getDetailList"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      
      <el-table v-loading="detailLoading" :data="detailList" max-height="400">
        <el-table-column label="序号" align="center" width="80" type="index" :index="getIndex" />
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column label="点击时间" align="center" prop="clickTime" width="180" />
      </el-table>
      
      <pagination
        v-show="detailTotal>0"
        :total="detailTotal"
        :page.sync="detailQueryParams.pageNo"
        :limit.sync="detailQueryParams.pageSize"
        @pagination="getDetailList"
        style="margin-top: 20px;"
      />
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSearchLog, getSearchDetail } from "@/api/system/searchlog";
import { addDateRange } from "@/utils/qx";

export default {
  name: "SearchLog",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 热门搜索记录表格数据
      searchLogList: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: null,
        clickCount: null,
        terminal: null
      },
      // 详情对话框
      detailOpen: false,
      detailLoading: false,
      detailTotal: 0,
      detailList: [],
      currentKeyword: '',
      detailDateRange: [],
      detailQueryParams: {
        keyword: '',
        terminal: 0,
        pageNo: 1,
        pageSize: 20
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询热门搜索记录列表 */
    getList() {
      this.loading = true;
      const params = addDateRange(this.queryParams, this.dateRange);
      listSearchLog(params).then(response => {
        this.searchLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.currentKeyword = row.keyword;
      this.detailQueryParams.keyword = row.keyword;
      this.detailQueryParams.terminal = 0;
      this.detailQueryParams.pageNo = 1;
      this.detailDateRange = [];
      this.detailOpen = true;
      this.getDetailList();
    },
    /** 查询详情列表 */
    getDetailList() {
      this.detailLoading = true;
      let params = { ...this.detailQueryParams };

      // 处理时间范围
      if (this.detailDateRange && this.detailDateRange.length === 2) {
        params.startTime = this.detailDateRange[0];
        params.endTime = this.detailDateRange[1];
      }

      getSearchDetail(params).then(response => {
        this.detailList = response.list;
        this.detailTotal = response.total;
        this.detailLoading = false;
      });
    },
    /** 计算序号 */
    getIndex(index) {
      return (this.detailQueryParams.pageNo - 1) * this.detailQueryParams.pageSize + index + 1;
    }
  }
};
</script>
