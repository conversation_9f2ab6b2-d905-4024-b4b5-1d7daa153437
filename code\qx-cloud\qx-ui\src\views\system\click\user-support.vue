<template>
    <div class="app-container">
        <div class="app-conter-box">
            <div class="classftion-right-big-tltle">
                <!-- <img src="@/assets/images/index/biaoti.png" alt="" /> -->
                <div>用户点赞数</div>
                <div class="date">
                    <el-date-picker v-model="dayDate" @change="getDaySum()" style="width:300px" format="yyyy/MM/dd"
                        prefix-icon="" :clearable="false" ref="elDatePickControl" type="daterange" range-separator="-"
                        start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                            disabledDate: time => {
                                const today = new Date();
                                // 禁止选择今天之后的时间
                                if (time.getTime() > today.getTime()) {
                                    return true;
                                }
                                // 禁止结束时间选择今天
                                if (this.dayDate && this.dayDate.length === 2) {
                                    return time.getTime() === today.setHours(0, 0, 0, 0);
                                }
                                return false;
                            }
                        }"></el-date-picker>
                </div>
                <!-- <div class="more" @click="handleMoreClick('usersupport')">查看更多</div> -->
                <!-- <div class="title-change" @click="titleChange()"></div> -->
            </div>

            <div class="support-chart" v-if="dayDateList.length">
                <div class="tlite">各行业点赞</div>
                <div :style="'height: 340px;width:1200px;overflow-x: auto;overflow-y: hidden;'">
                <barChart ref="barChart" :linxAxisData="linxAxisData" :linchartData="linchartData" :height="'380px'"
                    :width="'3000px'" class="lineclickchart" :types="1" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
import { listAll } from '@/api/system/support'
import barChart from '../../dashboard/lineclickchart.vue'
import { listIndustry } from '@/api/system/industry'

export default {
    name: 'userBrisk',
    components: {
        barChart
    },
    data() {
        return {
            isShow: false,
            queryParams: {},
            dayDate: [],
            dayDateList: [],

            list1: [],
            //行业列表
            industryList: [],
            linxAxisData: [],
            linchartData: []
        }
    },

    created() {
        // const end = new Date()
        // const start = new Date()
        // start.setTime(start.getTime() - 3600 * 1000 * 24 * 10)
        // this.dayDate = [start, end]

        setScale()
        window.onresize = () => {
            setScale()
        }
        this.dayDate = this.$route.query.dayDate.split(',')
        this.getlistIndustry()
    },
    methods: {
        getlistIndustry() {
            listIndustry().then(response => {
                this.industryList = response.rows
                this.getDaySum()
            })
        },
        // 跳转传参数 开始 结束时间
        handleMoreClick(url) {
            this.$router.push("indexDetail/" + url + "?dayDate=" + this.dayDate)

            console.log(url)
        },
        //切换图表列表
        titleChange() {
            this.isShow = !this.isShow
        },

        downClick(name) {
            let that = this
            that.$refs[name].focus()
        },
        getDaySum() {
            let that = this
            // this.queryParams.params = {}
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            let echartMapXData = []
            let echartNumYData = []
            listAll(this.queryParams).then(response => {
                this.dayDateList = []
                this.dayDateList = response.data

                that.industryList.forEach(row => {
                    let isAdd = true
                    that.dayDateList.forEach(item => {
                        if (item.industryCode == row.industryCode) isAdd = false
                    })
                    if (isAdd) {
                        that.dayDateList.push({ supportCount: 0, industryName: row.industryName, industryCode: row.industryCode })
                    }
                })
                that.dayDateList.forEach(item => {
                    echartNumYData.push(item.supportCount)
                    echartMapXData.push(item.industryName)
                })
                console.log(echartMapXData, echartNumYData)
                this.linchartData = echartNumYData
                this.linxAxisData = echartMapXData
                that.$refs['barChart'].initChart(echartMapXData, echartNumYData)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    width: 100%;
    font-family: '思源黑体';
    background: #f0f2f5;
    min-height: calc(100vh - 84px);

    .app-conter-box {
        width: 1200px;
        margin: auto;
        overflow: hidden;
    }

    .classftion-right-big-tltle {
        margin: 30px 0;
        font-size: 24px;
        font-weight: 900;
        color: #0066e4;
        display: flex;
        align-items: center;
        height: 60px;
        border-radius: 10px;
        justify-content: space-between;
        padding: 0 24px;
        box-sizing: border-box;
        background: #ffffff;

        .date {
            // flex: 1;
            display: flex;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
            position: relative;
            padding-right: 10px;
            align-items: center;
            cursor: pointer;

            .onlowdate {
                width: 72px;
                height: 36px;
                border-radius: 10px;
                opacity: 1;
                border: 1px solid #3384e9;
                text-align: center;
                line-height: 36px;
                background: #ffffff;
                margin-right: 20px;
            }
        }
    }

    /* justify-content: space-between; */
}

.support-chart {
    width: 1634px;
    height: 440px;
    border-radius: 10px;
    opacity: 1;
    
    background: #ffffff;

    .tlite {
        padding-left: 25px;
        padding-top: 16px;
        font-size: 20px;
        font-weight: 700;
    }
}
</style>
