<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '1838px'
    },
    height: {
      type: String,
      default: '620px'
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {

  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart(echartXData, echartData) {
      let that = this;
      this.chart = echarts.init(this.$el)
      this.chart.setOption({
        title: { show: false, },
        backgroundColor: 'rgba(255, 255, 255, 1)',
        xAxis: {
          boundaryGap: true, // 显示间距设置为false
          type: 'category',
          data: echartXData,
          axisLabel: {//x轴文字的配置
            show: true,
            fontWeight: 800,
            textStyle: {
              color: "#1366BF",
            }
          },
          axisTick: {
            show: false //不显示坐标轴刻度线
          },
          axisLine: {
            lineStyle: {
              color: "#DBE8FF"//x轴轴线颜色
            }
          },
          splitLine: {     //网格线
            "show": false
          }
        },
        grid: {
          top: 10,
          bottom: 40,
          left: '3%',
          right: '4%',
        },
        yAxis: {
          type: 'value',
          minInterval: 1,
          max: function (value) {
            return value.max + 5;
          },
          axisTick: {
            show: false //不显示坐标轴刻度线
          },
          axisLine: {
            lineStyle: {
              color: "#DBE8FF"//x轴轴线颜色
            }
          },
          splitLine: {     //网格线
            "show": false
          },
          axisLabel: {//x轴文字的配置
            show: true,
            fontWeight: 800,
            textStyle: {
              color: "#1366BF",
            }
          }
        },
        series: [
          {
            data: echartData,
            type: 'bar',
            barWidth: '60',
            itemStyle: {
              color: '#30D4A9'
            },
            label: {
              // 柱图头部显示值
              show: true,
              position: "top",
              color: "#30D4A9",
              fontSize: "18rem",
              fontWeight:800,
              formatter: (params) => {
                return params.value[params.encode.x[0]];
              },
            },
          }
        ]

      })
    }
  }
}
</script>
