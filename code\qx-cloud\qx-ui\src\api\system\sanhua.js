import request from '@/utils/request'

// 查询ishow平台行业场景与三化解决方案ID基础列表
export function listSanhua(query) {
  return request({
    url: '/system/sanhua/list',
    method: 'get',
    params: query
  })
}

// 查询ishow平台行业场景与三化解决方案ID基础详细
export function getSanhua(id) {
  return request({
    url: '/system/sanhua/' + id,
    method: 'get'
  })
}

// 新增ishow平台行业场景与三化解决方案ID基础
export function addSanhua(data) {
  return request({
    url: '/system/sanhua',
    method: 'post',
    data: data
  })
}

// 修改ishow平台行业场景与三化解决方案ID基础
export function updateSanhua(data) {
  return request({
    url: '/system/sanhua',
    method: 'put',
    data: data
  })
}

// 删除ishow平台行业场景与三化解决方案ID基础
export function delSanhua(id) {
  return request({
    url: '/system/sanhua/' + id,
    method: 'delete'
  })
}


export function listTypeData() {
  return request({
    url: '/system/sanhua/typeDataCase',
    method: 'get'
  })
}