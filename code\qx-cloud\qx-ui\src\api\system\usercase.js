import request from '@/utils/request'

// 查询添加案例的用户列表
export function listUsercase(query) {
  return request({
    url: '/system/usercase/list',
    method: 'get',
    params: query
  })
}

// 查询添加案例的用户详细
export function getUsercase(id) {
  return request({
    url: '/system/usercase/' + id,
    method: 'get'
  })
}

// 新增添加案例的用户
export function addUsercase(data) {
  return request({
    url: '/system/usercase',
    method: 'post',
    data: data
  })
}

// 修改添加案例的用户
export function updateUsercase(data) {
  return request({
    url: '/system/usercase',
    method: 'put',
    data: data
  })
}

// 删除添加案例的用户
export function delUsercase(id) {
  return request({
    url: '/system/usercase/' + id,
    method: 'delete'
  })
}

// 导出添加案例的用户
export function exportUsercase(query) {
  return request({
    url: '/system/usercase/export',
    method: 'get',
    params: query
  })
}