<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="组件分类" prop="assemblyType">
        <el-select v-model="queryParams.assemblyType" collapse-tags size="small" placeholder="请选择组件分类" @change="optionChange">
          <el-option key="0" label="图片" value="0"></el-option>
          <el-option key="1" label="视频" value="1"></el-option>
          <el-option key="2" label="PPT" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="组件类型" prop="assemblyModel">
        <el-select v-model="queryParams.assemblyModel" placeholder="组件类型" clearable size="small">
          <el-option v-for="dict in formOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:assembly:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:assembly:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:assembly:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:assembly:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="assemblyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="组件主键" align="center" prop="assemblyId" /> -->
      <el-table-column label="组件分类" align="center" prop="assemblyType">
        <template slot-scope="scope">
          <span>{{scope.row.assemblyType===0?'图片':scope.row.assemblyType===1?'视频':'PPT'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="组件类型" align="center" prop="assemblyModel">
        <template slot-scope="scope">
          <span>{{setOptionsName(scope.row.assemblyModel)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="组件名称" align="center" prop="assemblyName">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.assemblyName"
            raw-content
            placement="top-start"
            v-if="scope.row.assemblyName"
          >
            <span v-if="scope.row.assemblyName && scope.row.assemblyName.length <= 30">
               {{ scope.row.assemblyName }}
          </span>
            <span v-if="scope.row.assemblyName && scope.row.assemblyName.length > 30">
               {{ scope.row.assemblyName.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.assemblyName== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="组件图片" align="center" prop="assemblyUrl">
        <template slot-scope="scope">
          <img v-show="scope.row.assemblyType==0" style="width:50px;height:50px;" :src="scope.row.assemblyUrl" />
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:assembly:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:assembly:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改E编排组件对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="组件分类" prop="assemblyType">
          <el-select v-model="form.assemblyType" collapse-tags placeholder="请选择组件分类">
            <el-option :key="0" label="图片" :value="0"></el-option>
            <el-option :key="1" label="视频" :value="1"></el-option>
            <el-option :key="2" label="PPT" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组件类型" prop="assemblyModel" v-if="form.assemblyType === 0 || form.assemblyType === 1">
          <el-select v-model="form.assemblyModel" placeholder="请选择组件类型" clearable>
            <el-option v-for="dict in formOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="组件名称" prop="assemblyName">
          <el-input v-model="form.assemblyName" placeholder="请输入组件名称" maxlength="100" show-word-limit/>
        </el-form-item>
        <el-form-item label="组件url" prop="assemblyUrl">
          <FileUpload v-model="form.assemblyUrl" :fileSize="fileSize" :fileType="fileType" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAssembly, getAssembly, delAssembly, addAssembly, updateAssembly, exportAssembly } from "@/api/system/assembly";
import FileUpload from "../../../components/FileUpload";
export default {
  name: "Assembly",
  components: {
    FileUpload
  },
  data() {
    return {
      topType: null,
      filetemp: {},
      fileSize: 2,
      fileType: ["png", "jpg"],
      selectOptions: [],
      formOptions: [],
      options: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // E编排组件表格数据
      assemblyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        assemblyType: null,
        assemblyModel: null,
        assemblyUrl: null,
        userId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        assemblyType: [
          { required: true, message: "组件分类不能为空", trigger: "blur" }
        ],
        assemblyUrl: [
          { required: true, message: "组件文件不能为空", trigger: "blur" }
        ],
        assemblyName: [
          { required: true, message: "组件名称不能为空", trigger: "blur" }
        ]

      }
    };
  },
  created() {
    this.getList();
    this.getDicts("qx_e_arrange_type").then(response => {
      this.formOptions = response.data;
    });
  },
  watch: {
    'form.assemblyType': function(newVal, oldVal) {
      this.formOptionChange();
    }
  },
  methods: {
    setOptionsName(value) {
      let name = '';
      this.formOptions.forEach(element => {
        if (value == element.dictValue) {
          name = element.dictLabel;
        }
      });
      return name;
    },
    formOptionChange() {
      let that = this;
      // that.formOptions = [];

      if (that.form.assemblyType == 0) {
        that.fileSize = 2;  // 设置图片类型文件大小限制（单位：MB）
        that.fileType = ["png", "jpg"];  // 只允许上传 png 和 jpg
      } else if (that.form.assemblyType == 2) {
        that.fileSize = 20;  // 设置 PPT 文件大小限制（单位：MB）
        that.fileType = ["ppt", "pptx"];  // 只允许上传 ppt 和 pptx
      } else if (that.form.assemblyType == 1) {
        that.fileSize = 200;  // 设置 PPT 文件大小限制（单位：MB）
        that.fileType = ["mp4"];  // 只允许上传 ppt 和 pptx
      }

      this.filetemp[this.topType] = that.form.assemblyUrl;
      that.$set(that.form, 'assemblyUrl', this.topType == null ? '' : this.filetemp[that.form.assemblyType]);
      that.$set(that.form, 'assemblyModel', null);

      // that.options.forEach(element => {
      //   if ((parseInt(that.form.assemblyType) + 1) === parseInt(element.dictValue.substr(0, 1))) {
      //     that.formOptions.push(element);
      //   }
      // });

      this.topType = that.form.assemblyType;
    },
    optionChange() {
      let that = this;
      that.selectOptions = [];
      that.$set(that.queryParams, 'assemblyModel', null);
      that.options.forEach(element => {
        if ((parseInt(that.queryParams.assemblyType) + 1) === parseInt(element.dictValue.substr(0, 1))) {
          that.selectOptions.push(element);
        }
      });
    },
    /** 查询E编排组件列表 */
    getList() {
      this.loading = true;
      listAssembly(this.queryParams).then(response => {
        this.assemblyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        assemblyId: null,
        assemblyType: null,
        assemblyModel: null,
        assemblyUrl: null,
        userId: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.assemblyId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.form.assemblyType = 0;
      this.title = "添加E编排组件";
      this.formOptionChange();  // 调用文件类型更新方法
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      var that = this;
      this.reset();
      const assemblyId = row.assemblyId || this.ids
      getAssembly(assemblyId).then(response => {
        response.data.assemblyModel = response.data.assemblyModel !== null ? response.data.assemblyModel + "" : undefined;
        this.form = response.data;
        this.open = true;
        this.title = "修改E编排组件";
        this.topType = this.form.assemblyType;
        this.formOptionChange();  // 调用文件类型更新方法
        this.$forceUpdate();
      });

    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.assemblyType === 2){
        this.form.assemblyModel = ''
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.assemblyId != null) {
            updateAssembly(this.form).then(response => {
              this.msgSuccess(response.msg);
              this.open = false;
              this.getList();
            });
          } else {
            addAssembly(this.form).then(response => {
              this.msgSuccess(response.msg);
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      //const assemblyIds = row.assemblyId || this.ids;
      const assemblyIds = row.assemblyId == null ? this.ids : [row.assemblyId];
      const names = this.assemblyList
        .filter(item => assemblyIds.includes(item.assemblyId))
        .map(item => item.assemblyName)
        .join(', ');

      this.$confirm('是否确认删除E编排组件名称为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delAssembly(assemblyIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的组件数据';
      }else {
        msg = '是否确认导出所有的组件数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/assemblyto/export', exportParams, `assembly_${new Date().getTime()}.xlsx`)
      }).catch({});
    }


  }
};
</script>
