<template>
  <div class="app-container" style="padding:20px;">

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px" style="text-align:left;">
      <el-form-item label="行业" prop="industryCode">
        <el-select v-model="queryParams.industryCode" placeholder="请选择行业" clearable @clear="handleIndustryClear">
          <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="mainCaseTitle">
        <el-input v-model="queryParams.mainCaseTitle" placeholder="请输入案例标题" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="审核状态" prop="mainCaseState">
        <el-select size="small" v-model="queryParams.mainCaseState" placeholder="请选择审核状态">
          <el-option :key="0" label="审核中" :value="0"></el-option>
          <el-option :key="4" label="审核通过" :value="4"></el-option>
          <el-option :key="2" label="审核未通过" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain size="mini" :disabled="multiple" @click="agree">一键同意</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain size="mini" :disabled="multiple" @click="reject">一键驳回</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="mainList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="mainCaseId" /> -->
      <el-table-column label="案例标题" align="center" prop="mainCaseTitle" />
      <el-table-column label="案例封面" align="center" prop="mainCaseCover">
        <template slot-scope="scope">
          <el-image style="width:100px;height:50px; margin: 0 auto;cursor:pointer;" :src="scope.row.mainCaseCover" :z-index="9999" :preview-src-list="[scope.row.mainCaseCover]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="是否对外" align="center" prop="isAbout">
        <template slot-scope="scope">
          <span>{{scope.row.isAbout==0?'否':'是'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="行业名称" align="center" prop="industryCode">
        <template slot-scope="scope">
          <p>{{setIndustryCode(scope.row.industryCode)}}</p>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="mainCaseState">
        <template slot-scope="scope">
          <p>{{scope.row.mainCaseState===0?'审核中':scope.row.mainCaseState===4?'审核通过':scope.row.mainCaseState===2?'审核未通过':scope.row.mainCaseState===3?'未审核':''}}</p>
        </template>
      </el-table-column>
      <el-table-column label="审核未通过说明" align="center" prop="rejectExplain">
        <template #default="scope">
          <el-tooltip :content="scope.row.rejectExplain" raw-content placement="top-start" v-if="scope.row.rejectExplain">
            <span v-if="scope.row.rejectExplain && scope.row.rejectExplain.length <= 30">
              {{ scope.row.rejectExplain }}
            </span>
            <span v-if="scope.row.rejectExplain && scope.row.rejectExplain.length > 30">
              {{ scope.row.rejectExplain.substr(0, 30) + "..." }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.rejectExplain== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="管理员显示隐藏" align="center" prop="showHide">
        <template slot-scope="scope">
          <p>{{scope.row.showHide===0?'显示':'隐藏'}}</p>
        </template>
      </el-table-column>
      <el-table-column label="隐藏说明" align="center" prop="hideExplain" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="text" icon="el-icon-s-promotion" v-show="scope.row.mainCaseState===0" size="small" @click="agree(scope.row)">同意</el-button>
          <el-button type="text" icon="el-icon-s-promotion" v-show="scope.row.mainCaseState===0" size="small" @click="reject(scope.row)">驳回</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改用户案例对话框 -->
    <el-dialog :title="title" :visible.sync="open" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业:" prop="industryCode">
              <div>{{setIndustryCode(form.industryCode)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否对外:" prop="isAbout">
              <div>{{form.isAbout==0?'否':'是'}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例标题:" prop="mainCaseTitle">
          <div>{{form.mainCaseTitle}}</div>
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="案例封面:" prop="mainCaseCover">
              <el-image style="width:150px;height:150px; margin: 0 auto;cursor:pointer;" :src="form.mainCaseCover" :z-index="9999" :preview-src-list="[form.mainCaseCover]">
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="案例内容" prop="mainCaseContent">
              <video v-if="fileName=='.mp4'" width="300px" height="150px" controls>
                <source :src="form.mainCaseContent" type="video/mp4">
              </video>
              <el-image v-else style="width:150px;height:150px; margin: 0 auto;cursor:pointer;" :src="form.mainCaseContent" :z-index="9999" :preview-src-list="[form.mainCaseContent]">
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例详情编辑" prop="explainTitle">
          <el-image v-if="form.mainScene != null && form.mainScene != '' && form.mainScene.indexOf('http') > -1" style="width: 160px;height: 90px;margin-left:30px;cursor:pointer;" :src="form.mainScene" :z-index="9999"
            :preview-src-list="[form.mainScene]">
          </el-image>

        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import ImageUpload from "../../../../components/ImageUpload";
import {
  reject,
  agree,
  examinelist,
  detail,
  industrylist,
} from "@/api/hzhb/case";
export default {
  name: "caseexamine",
  components: {
    ImageUpload,
  },
  data() {
    return {
      fileName: null,
      divSFYX: null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户案例表格数据
      mainList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mainCaseTitle: null,
        mainCaseCover: null,
        mainCaseContent: null,
        mainCaseExplain: null,
        userId: null,
        region: null,
        orderByColumn: "main_case_state, create_time",
        isAsc: "desc",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industryCode: [
          { required: true, message: "行业不能为空", trigger: "blur" },
        ],
        isAbout: [
          { required: true, message: "对内对外不能为空", trigger: "change" },
        ],
        mainCaseTitle: [
          { required: true, message: "案例标题不能为空", trigger: "blur" },
        ],
        mainCaseCover: [
          { required: true, message: "案例封面不能为空", trigger: "change" },
        ],
        mainCaseContent: [
          { required: true, message: "案例内容不能为空", trigger: "change" },
        ],
      },
      industryList: [],
      accountType: null,
    };
  },
  created() {
    this.getIndustryList();
    this.accountType = localStorage.getItem("accountType");
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const mainCaseId = row.mainCaseId || this.ids;

      detail(mainCaseId).then((response) => {
        this.form = response.data;
        this.fileName = this.form.mainCaseContent.substring(
          this.form.mainCaseContent.lastIndexOf(".")
        );
        this.open = true;
        this.title = "查看落地案例";
      });
    },
    reject(row) {
      let that = this;
      const ids =
        row == null || row.mainCaseId == null ? this.ids : [row.mainCaseId];
      this.$prompt("请输入驳回理由", "驳回信息", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "驳回理由不能为空",
      })
        .then(({ value }) => {
          let select = [];
          ids.forEach((id) => {
            that.mainList.forEach((item) => {
              if (id == item.mainCaseId) {
                select.push({ id: id });
              }
            });
          });

          let row = {
            qxDevelopmentExamineList: select,
            explain: value,
          };
          return reject(row).then((res) => {
            if (res.code === 200) {
              that.$message.success("审批成功");
              that.getList();
            }
          });
        })
        .catch(() => {});
    },
    agree(row) {
      let that = this;
      const ids =
        row == null || row.mainCaseId == null ? this.ids : [row.mainCaseId];
      let select = [];
      ids.forEach((id) => {
        that.mainList.forEach((item) => {
          if (id == item.mainCaseId) {
            select.push({ id: id });
          }
        });
      });

      agree({ qxDevelopmentExamineList: select }).then((res) => {
        if (res.code === 200) {
          that.$message.success("审批成功");
          that.getList();
        }
      });
    },
    setIndustryCode(code) {
      let name = "";
      this.industryList.forEach((item) => {
        if (item.industryCode == code) {
          name = item.industryName;
        }
      });
      return name;
    },
    getIndustryList() {
      let that = this;
      industrylist({}).then((res) => {
        if (res.code === 200) {
          that.industryList = res.data;
          this.getList();
        }
      });
    },
    setMainCaseContent(value) {
      this.$set(this.form, "mainCaseContent", value);
    },
    setInput(value) {
      this.$set(this.form, "mainCaseCover", value);
    },
    /** 查询用户案例列表 */
    getList() {
      this.loading = true;
      examinelist(this.queryParams).then((response) => {
        this.mainList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mainCaseId: null,
        mainCaseTitle: null,
        mainCaseCover: null,
        mainCaseContent: null,
        createTime: null,
        userId: null,
        region: null,
        industryCode: null,
        isAbout: 0,
        explainTitle: null,
        projectExplain: null,
        mainScene: null,
        projectCost: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.mainCaseId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.mb8 {
  margin-bottom: 8px;
}
.panorama-ifram {
  width: 100vw;
  overflow: hidden;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1001;
}
.panorama-ifram-item {
  width: 100%;
  padding: 0;
  border: 0;
  margin: 0;
  height: 100%;
  overflow: hidden;
}

::v-deep .el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 40px;
      font-size: 13px;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*="el-icon-"] + span {
      margin-left: 1px;
    }
  }
}
</style>
