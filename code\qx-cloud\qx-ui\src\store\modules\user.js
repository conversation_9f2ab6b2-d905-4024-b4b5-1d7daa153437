import { login, logout, getInfo, verifycode,tokenlogin } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    email: '',
    accountType: null,
    region: '',
    roles: [],
    permissions: [],
    defaultpath:''
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_EMAIL: (state, email) => {
      state.email = email
    },
    SET_AccountType: (state, accountType) => {
      state.accountType = accountType
    },
    SET_region: (state, region) => {
      state.region = region
    },
    SET_defaultpath: (state, defaultpath) => {
      state.defaultpath = defaultpath
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 登录
    LoginToken({ commit }, token) {
      return new Promise((resolve, reject) => {
        tokenlogin(token).then(res => {
          if (res.token == 0 || res.token == 1) {
            resolve(res.token)
          } else {
            setToken(res.token)
            commit('SET_TOKEN', res.token)
            localStorage.setItem('defaultpath', res.url);
            console.log('33333',res.url);
            resolve(res.url)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          if (res.token == 0 || res.token == 1) {
            resolve(res.token)
          } else {
            setToken(res.token)
            commit('SET_TOKEN', res.token)
            localStorage.setItem('defaultpath', res.url);
            console.log(res.url);
            resolve(res.url)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    VerifyCode({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const code = userInfo.code
      return new Promise((resolve, reject) => {
        verifycode(username, code).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve(res.url)
        }).catch(error => {
          reject(error)
        })
      })
    },




    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token).then(res => {
          const user = res.user
          const avatar = user.avatar == "" || user.avatar == null ? require("@/assets/images/head.png") : user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', avatar)
          commit('SET_EMAIL', user.email)
          commit('SET_AccountType', user.accountType)
          commit('SET_region', user.region)

          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
