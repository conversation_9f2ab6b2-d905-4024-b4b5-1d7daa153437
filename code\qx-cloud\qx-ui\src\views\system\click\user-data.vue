<template>
    <div class="app-container">
        <div class="app-conter-box">
            <div class="dashboard-editor-header mt20">
                各功能板块使用时长

                <div class="date">
                    <div class="onlowdate" @click="handleExport">导出</div>
                    <el-date-picker v-model="dayDate" @change="getclicksumusercount()" style="width:300px"
                        format="yyyy/MM/dd" prefix-icon="" :clearable="false" ref="elDatePickControl" type="daterange"
                        range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                            disabledDate: time => {
                                const today = new Date();
                                // 禁止选择今天之后的时间
                                if (time.getTime() > today.getTime()) {
                                    return true;
                                }
                                // 禁止结束时间选择今天
                                if (this.dayDate && this.dayDate.length === 2) {
                                    return time.getTime() === today.setHours(0, 0, 0, 0);
                                }
                                return false;
                            }
                        }"></el-date-picker>
                </div>
            </div>
            <div class="classftion">
                <div class="classftion-box">
                    <div class="headquarters">
                        <div class="classftion-left-box-tlite">
                            <img src="@/assets/images/index/biaoti.png" alt="" />
                            {{ isSecondaryAdministrator? this.region:'总部' }}数据
                        </div>

                        <div class="headquarters-box">
                            <img style="width: 74px;" src="@/assets/images/index/headquarters.png" alt="" />
                            <div>
                                <div class="headquarters-tltie" style="margin-top: 0;text-align: center;">{{ isSecondaryAdministrator? this.region:'总部' }}</div>
                                <div class="headquarters-num">{{ headquarters }}</div>
                            </div>
                        </div>
                    </div>
                    <div style="margin: 0 56px 0 22px;">
                        <div class="classftion-left-box-tlite">
                            <img src="@/assets/images/index/biaoti.png" alt="" />
                            政企专业公司
                        </div>
                        <!-- <div class="headquarters-tltie ">政企专业公司</div> -->
                        <myLine v-if="list2.length" :width="'861px'" :height="'214px'" :List="list2"></myLine>
                    </div>
                </div>
                <div>
                    <div class="classftion-left-box-tlite">
                        <img src="@/assets/images/index/biaoti.png" alt="" />
                        其他专业公司
                    </div>
                    <!-- <div class="headquarters-tltie ">其他专业公司</div> -->
                    <myLine v-if="list3.length" :width="'1151px'" :height="'214px'" :List="list3"></myLine>
                </div>
                <div>
                    <div class="classftion-left-box-tlite">
                        <img src="@/assets/images/index/biaoti.png" alt="" />
                        省份列表
                    </div>
                    <myLine v-if="list1.length" :width="'1151px'" :height="'204px'" :List="list1"></myLine>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
import { clicksumusercount, clickSumusercountExport,provincialSecondaryAdministrator } from '@/api/system/sum.js'
import myLine from '../../dashboard/myLine.vue'
import { Loading } from 'element-ui'

// clicksumusercount
export default {
    name: 'Config',
    components: {
        myLine
    },
    data() {
        return {
            // dayDate: [],
            list1: [],
            list2: [],
            list3: [],
            headquarters: 0,
            queryParams: {},
            dayDate: [],
            isSecondaryAdministrator: false,
            region: '',
        }
    },

    created() {
        setScale()
        window.onresize = () => {
            setScale()
        }
        this.dayDate = this.$route.query.dayDate.split(',')
        this.getclicksumusercount()
        this.myProvincialSecondaryAdministrator();
    },
    methods: {
        myProvincialSecondaryAdministrator(){
            provincialSecondaryAdministrator().then(res=>{
            this.isSecondaryAdministrator = res.data.isSecondaryAdministrator;
            this.region = res.data.region;
            if(this.isSecondaryAdministrator && res.data.region){
                this.queryParams.region = res.data.region;
            }
        });
        },
        handleExport() {
            let that = this
            this.queryParams.params = {}
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            this.$confirm('是否确认导出各省用户数据项?', '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(function () {
                    that.download(
                        'system/clicksum/usercountexport',
                        {
                            ...that.queryParams
                        },
                        `usercount_${new Date().getTime()}.xlsx`
                    )
                })
                .catch({})
        },

        getclicksumusercount() {
            let that = this
            that.list1 = []
            that.list2 = []
            that.list3 = []
            that.headquarters = 0
            this.queryParams.params = {}
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            if(this.isSecondaryAdministrator && this.region){
                this.queryParams.region = this.region;
            }
            clicksumusercount(this.queryParams).then(res => {
                res.province.forEach(element => {
                    element.peopleNumber = 0
                    res.datalist.forEach(item => {
                        if (element.provinceName === item.provinceName) {
                            element.peopleNumber = item.peopleNumber
                        }
                    })
                })

                //数据为0的省份
                // if (isaddList.length > 0) {
                //   isaddList.forEach(element => {
                //     res.datalist.push({ peopleNumber: 0, provinceName: element.provinceName, provinceType: element.provinceType })
                //   });
                // }
                res.datalist.forEach(item => {
                    if (item.provinceName === '总部') {
                        that.headquarters = item.peopleNumber
                    }
                    if(this.isSecondaryAdministrator && this.region === item.provinceName){
                        that.headquarters = item.peopleNumber
                    }
                })

                let list1 = []
                res.province.forEach(element => {
                    if (element.provinceType === 0) list1.push(element)
                    if (element.provinceType === 1) that.list2.push(element)
                    if (element.provinceType === 2) that.list3.push(element)
                })

                // const len = list1.length //len为数组的长度
                // const n = 12 // 假设每行显示12个
                // const Num = len % 12 === 0 ? len / 12 : Math.floor(len / 12 + 1) //得出多少份
                // const res1 = [] //定义数组接受最终的数据
                // for (let i = 0; i < Num; i++) {
                //     // slice() 方法返回一个从开始到结束（不包括结束）的数组。且原始数组不会被修改。
                //     const newArr = list1.slice(i * n, i * n + n) //得到每份的数据
                //     res1.push(newArr) //往res数组里加数据
                // }
                this.list1 = list1
                // console.log(this.list2, res1)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    width: 100%;
    font-family: '思源黑体';
    background: #f0f2f5;
    min-height: calc(100vh - 84px);

    .app-conter-box {
        width: 1200px;
        margin: auto;
        overflow: hidden;

        .dashboard-editor-header {
            font-size: 24px;
            font-weight: 900;
            color: #0066e4;
            display: flex;
            align-items: center;
            height: 60px;
            border-radius: 10px;
            justify-content: space-between;
            padding: 0 24px;
            box-sizing: border-box;
            background: #ffffff;

            .date {
                // flex: 1;
                display: flex;
                text-align: right;
                font-size: 18px;
                font-weight: 500;
                position: relative;
                padding-right: 10px;
                align-items: center;
                cursor: pointer;

                .onlowdate {
                    width: 72px;
                    height: 36px;
                    border-radius: 10px;
                    opacity: 1;
                    border: 1px solid #3384e9;
                    text-align: center;
                    line-height: 36px;
                    background: #ffffff;
                    margin-right: 20px;
                }
            }
        }
    }
}

.classftion {
    margin-top: 30px;
    width: 100%;
    // height: 877px;
    border-radius: 10px;
    opacity: 1;
    background: #ffffff;
    padding: 25px;
    box-sizing: border-box;
}

.classftion-box {
    display: flex;

    .headquarters-tltie {
        font-size: 18px;
        margin-bottom: 18px;
    }

    .headquarters-box {
        display: flex;
        align-items: center;
        justify-content: space-around;

        width: 211px;
        height: 132px;
        border: 1px solid transparent;
        /* 设置边框宽度 */
        position: relative;
        margin-top: 28px;
        border-image: linear-gradient(to right, #68e5fa, #013fff) 1;
        /* 添加渐变边框 */
        border-radius: 10px;

        .headquarters-num {
            font-size: 36px;
            color: #2269de;
            font-weight: 900;
        }
    }
}

.classftion-left-box-tlite {
    padding: 0 !important;
    // height: 100%;
    display: flex;
    align-items: flex-start;

    img {
        width: 18px;
        height: 24px;
        margin-right: 20px;
    }
}
</style>
