<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业场景" prop="sceneId">
        <el-cascader
          v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear"> <!-- 监听清除事件 -->
        </el-cascader>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select size="small" v-model="queryParams.state" placeholder="请选择审核状态">
          <el-option :key="3" label="未审核" :value="3"></el-option>
          <el-option :key="0" label="审核中" :value="0"></el-option>
          <el-option :key="4" label="审核通过" :value="4"></el-option>
          <el-option :key="2" label="审核不通过" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:device:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:device:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:device:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:device:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="developmentList">
<!--      <el-table-column label="id" align="center" prop="id" />-->
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="名称" align="center" prop="userName" />
      <el-table-column label="落地案例数量" align="center" prop="achieveNumber" />
      <el-table-column label="可支撑人数" align="center" prop="number" />
      <el-table-column label="落地案例地址" align="center" prop="achieveAddress" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ setTruncatedAddress(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="state" align="center" label="审核进度" width="160">
        <template slot-scope="scope">
          <span>{{scope.row.state===0?'审核中':scope.row.state===4?'审核通过':scope.row.state===2?'审核不通过':'未审核'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="定制开发内容" align="center" prop="imgUrl">
        <template slot-scope="scope">
          <img v-show="scope.row.imgUrl!=null&&scope.row.imgUrl!=''" style="width:50px;height:50px;cursor: pointer;" :src="scope.row.imgUrl" @click.stop="dialogVisible = true;dialogImgUrl=scope.row.imgUrl" />
        </template>
      </el-table-column>
      <el-table-column label="驳回原因" align="center" prop="explain" v-if="queryParams.state === 2">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.explain"
            raw-content
            placement="top-start"
            v-if="scope.row.explain"
          >
            <span v-if="scope.row.explain && scope.row.explain.length <= 30">
               {{ scope.row.explain }}
          </span>
            <span v-if="scope.row.explain && scope.row.explain.length > 30">
               {{ scope.row.explain.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.explain== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <router-link :to="'/developmentDetail/development/data/' + scope.row.id" class="link-type">
            <span>查看详情</span>
          </router-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    <el-dialog :visible.sync="dialogVisible" title="预览" width="1200" append-to-body>
      <img :src="dialogImgUrl" style="display: block; max-width: 100%; margin: 0 auto;">
    </el-dialog>



    <el-dialog :title="title" :visible.sync="open" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业" prop="industryCode">
              <el-select v-model="form.industryCode" placeholder="请选择行业">
                <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行业场景" prop="sceneId">
              <el-cascader v-model="form.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" @change="queryParamsHandleChange"></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="名称" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入名称"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="落地案例数量" prop="achieveNumber">
              <el-input-number :min="0" :max="99999999" :controls="false" placeholder="请输入数量" v-model="form.achieveNumber" style="text-align:center;" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="可支撑人数" prop="number">
              <!-- <el-input v-model="form.number" placeholder="请输入可支撑人数"/> -->
              <el-input-number :min="0" :max="9999999999" :controls="false" placeholder="支撑人数" v-model="form.number" style="text-align:center;" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="落地案例地址" prop="achieveAddress">
          <template slot-scope="scope">
              <!-- <my-select :options='optionsProvince' style="width:250px" v-model="scope.row.achieveAddress" :ref="'ref'+scope.row.id" :key="'id'+scope.row.id"></my-select> -->
              <my-select :options='optionsProvince' style="width:250px" v-model="form.achieveAddress"></my-select>
          </template>
        </el-form-item>

        <el-form-item label="审核进度" prop="state">
          <template slot-scope="scope">
              <p>{{scope.row.state===0?'审核中':scope.row.state===4?'审核通过':scope.row.state===2?'审核未通过':scope.row.state===3?'未审核':''}}</p>
            </template>
        </el-form-item>

        <el-form-item label="审核不通过说明" prop="explain">
          <el-input v-model="form.explain" type="textarea" :rows="3" placeholder="请输入审核不通过说明" maxlength="1000" show-word-limit />
        </el-form-item>
        <!-- <el-form-item label="应用场景及亮点" prop="mainScene">
          <el-input v-model="form.mainScene" type="textarea" :rows="3" placeholder="请输入应用场景及亮点" maxlength="1000" show-word-limit />
        </el-form-item>
        <el-form-item label="项目整体成本" prop="projectCost">
          <el-input v-model="form.projectCost" type="textarea" :rows="3" placeholder="请输入项目整体成本" maxlength="1000" show-word-limit />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <!-- <el-button type="primary" v-if="region !='总部'" @click="handleExamine">提交审核</el-button> -->
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import MySelect from "../../../components/MySelect/MySelect.vue";
import { listDevelopment, getDevelopment } from "@/api/system/development";
import { getProvince } from "../../../utils/province";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";
import Template from "@/views/system/template";
export default {
  name: "Development",
  components: {
    Template,
    MySelect
  },
  data() {
    return {
      dialogVisible: false,
      dialogImgUrl: '',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 定制开发表格数据
      developmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industryId: null,
        sceneId: null,
        achieveNumber: null,
        achieveAddress: null,
        number: null,
        state: null,
        imgUrl: null,
        userName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industryCode: [
          { required: true, message: "行业不能为空", trigger: "blur" }
        ],

      },
      optionsProvince: [],
      options: [],
      industryList: [],
    };
  },
  watch: {
    // 监视查询条件 `type` 的变化
    'queryParams.state'(newType, oldType) {
      if (newType !== oldType) {
        this.queryParams.pageNum = 1; // 重置页码
        this.getList(); // 重新获取列表数据
      }
    }
  },
  created() {
    this.getList();
    this.optionsProvince = getProvince();
    listIndustry().then(response => {
      this.industryList = response.rows;
      listScene().then(response => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });
  },
  methods: {
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.queryParams.industryId = value[0];
      };

      if (value != null || value.length > 1) {
        this.queryParams.sceneId = value[1];
      };
    },
    //设置行业场景下拉
    setOption() {
      this.industryList.forEach(element => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: []
        }
        this.sceneList.forEach(row => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName
            });
          }
        });
        this.options.push(item);
      });
      console.log(this.options);
    },
    setProvince(row) {
      let cityName = "";
      if (row.qxDevelopmentCityList.length == 340) {
        return "全国";
      }
      row.qxDevelopmentCityList.forEach(row => {
        this.optionsProvince.forEach(item => {
          if (row.provinceId == item.value) {
            item.children.forEach(child => {
              if (row.cityId == child.value) {
                cityName += (cityName === "" ? child.label : "," + child.label);
              }
            });
          }
        });
      });
      return cityName;
    },
    /** 查询定制开发列表 */
    getList() {
      this.loading = true;
      listDevelopment(this.queryParams).then(response => {
        this.developmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        industryId: null,
        sceneId: null,
        achieveNumber: null,
        achieveAddress: null,
        number: null,
        state: null,
        imgUrl: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.sceneIds = [];
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加行业生态链";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDevelopment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改定制开发";
      });
    },
    setTruncatedAddress(row) {
      const address = this.setProvince(row);
      return address.length > 30 ? address.substring(0, 30) + '...' : address;
    },
    setIndustryCode(code) {
      let name = "";
      this.industryList.forEach(item => {
        if (item.industryCode == code) {
          name = item.industryName
        }
      })
      return name;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        // if (valid) {
        //   if (this.form.mainCaseId != null) {
        //     updateMain(this.form).then(response => {
        //       this.msgSuccess("修改成功");
        //       this.open = false;
        //       this.getList();
        //     });
        //   } else {
        //     addMain(this.form).then(response => {
        //       this.msgSuccess("新增成功");
        //       this.open = false;
        //       this.getList();
        //     });
        //   }
        // }
      });
    },
  }
};
</script>
