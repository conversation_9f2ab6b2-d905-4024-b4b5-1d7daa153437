import request from '@/utils/request'

// 查询E编排组件列表
export function listAssembly(query) {
  return request({
    url: '/system/assemblyto/list',
    method: 'get',
    params: query
  })
}

// 查询E编排组件详细
export function getAssembly(assemblyId) {
  return request({
    url: '/system/assemblyto/' + assemblyId,
    method: 'get'
  })
}

// 新增E编排组件
export function addAssembly(data) {
  return request({
    url: '/system/assemblyto',
    method: 'post',
    data: data
  })
}

// 修改E编排组件
export function updateAssembly(data) {
  return request({
    url: '/system/assemblyto',
    method: 'put',
    data: data
  })
}

// 删除E编排组件
export function delAssembly(assemblyId) {
  return request({
    url: '/system/assemblyto/' + assemblyId,
    method: 'delete'
  })
}

// 导出E编排组件
export function exportAssembly(query) {
  return request({
    url: '/system/assemblyto/export',
    method: 'get',
    params: query
  })
}
