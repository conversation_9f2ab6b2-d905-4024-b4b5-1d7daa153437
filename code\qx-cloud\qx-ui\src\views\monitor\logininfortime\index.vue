<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="名称" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入姓名" clearable size="small"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>

      <el-form-item label="省份" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入省份" clearable size="small"
                  @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <el-form-item label="时间" prop="createTime">
        <el-date-picker v-model="createTime" size="small" type="datetimerange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                   v-hasPermi="['monitor:logininforcount:export']">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" prop="id"/>
      <el-table-column label="名称" align="center" prop="nickname"/>
      <el-table-column label="省份" align="center" prop="provinceName"/>
      <el-table-column label="登录时间" align="center" prop="loginTime" sortable/>
      <el-table-column label="统计时间(s)" align="center" prop="timeLength"/>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>
  </div>
</template>

<script>
import {timeList, exportLogininforTime} from "@/api/monitor/logininfor";
import {Loading} from 'element-ui'

export default {
  name: "logininforCount",
  components: {},
  data() {
    return {
      createTime: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 前端用户点赞表格数据
      list: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      console.log(this.ids)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查询前端用户点赞列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.createTime != null && this.createTime.length > 0) {
        this.queryParams.params.beginTime = this.parseTime(this.createTime[0], '{y}-{m}-{d} {h}:{i}:{s}');
        this.queryParams.params.endTime = this.parseTime(this.createTime[1], '{y}-{m}-{d} {h}:{i}:{s}');
      }
      timeList(this.queryParams).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.createTime = []
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if (that.ids.length > 0) {
        msg = '是否确认导出所筛选或选中的登录时间数据项';
      } else {
        msg = '是否确认导出所有的登录时间数据项';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/monitor/logininfor/timeexport', exportParams, `timeexport_${new Date().getTime()}.xlsx`)
      }).catch({});
    }
  }
};
</script>
