<template>
  <div class="app-container" style="padding:20px;">

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px" style="text-align:left;">
      <el-form-item label="行业" prop="industryCode">
        <el-select size="small" v-model="queryParams.industryCode" placeholder="请选择行业" clearable @clear="handleIndustryClear">
          <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="案例标题" prop="mainCaseTitle">
        <el-input v-model="queryParams.mainCaseTitle" placeholder="请输入案例标题" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="审核状态" prop="mainCaseState">
        <el-select size="small" v-model="queryParams.mainCaseState" placeholder="请选择审核状态">
          <el-option :key="3" label="未审核" :value="3"></el-option>
          <el-option :key="0" label="审核中" :value="0"></el-option>
          <el-option :key="4" label="审核通过" :value="4"></el-option>
          <el-option :key="2" label="审核未通过" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="mainList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="mainCaseId" /> -->
      <el-table-column label="案例标题" align="center" prop="mainCaseTitle" />
      <el-table-column label="案例封面" align="center" prop="mainCaseCover">
        <template slot-scope="scope">
          <el-image style="width:100px;height:50px; margin: 0 auto;cursor:pointer;" :src="scope.row.mainCaseCover" :z-index="9999" :preview-src-list="[scope.row.mainCaseCover]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="是否对外" align="center" prop="isAbout">
        <template slot-scope="scope">
          <span>{{scope.row.isAbout==0?'否':'是'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="行业名称" align="center" prop="industryCode">
        <template slot-scope="scope">
          <p>{{setIndustryCode(scope.row.industryCode)}}</p>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="mainCaseState">
        <template slot-scope="scope">
          <p>{{scope.row.mainCaseState===0?'审核中':scope.row.mainCaseState===4?'审核通过':scope.row.mainCaseState===2?'审核未通过':scope.row.mainCaseState===3?'未审核':''}}</p>
        </template>
      </el-table-column>
      <el-table-column v-if="region !='总部'" label="审核未通过说明" align="center" prop="rejectExplain" />
      <el-table-column label="管理员显示隐藏" align="center" prop="showHide">
        <template slot-scope="scope">
          <p>{{scope.row.showHide===0?'显示':'隐藏'}}</p>
        </template>
      </el-table-column>
      <el-table-column label="隐藏说明" align="center" prop="hideExplain" />
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" v-if="scope.row.mainCaseState!=0 && scope.row.isOneSelf===1" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-s-promotion" v-if="scope.row.isOneSelf===1" @click="handleExamine(scope.row)">提交审核</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" v-if="scope.row.isOneSelf===1" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    <!-- 添加或修改用户案例对话框 -->
    <el-dialog :title="title" :visible.sync="open" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业" prop="industryCode">
              <el-select v-model="form.industryCode" placeholder="请选择行业">
                <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否对外" prop="isAbout">
              <el-radio-group v-model="form.isAbout">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="标签" prop="tagCode">
          <el-select v-model="form.tagCode" filterable multiple placeholder="请选择标签">
            <el-option v-for="dict in tagTypeList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="案例标题" prop="mainCaseTitle">
          <el-input v-model="form.mainCaseTitle" placeholder="请输入案例标题" maxlength="16" show-word-limit />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例封面" prop="mainCaseCover">
              <ImageUpload @input="setInput" :value="form.mainCaseCover" :fileSize="2" accept=".jpg,.jpeg,.png" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例视频" prop="mainCaseContent">
              <ImageUpload @input="setMainCaseContent" tipContent="(备注:如没有视频可传图片,文件最大500M)" accept=".jpg,.jpeg,.png,.mp4" :fileSize="500" :value="form.mainCaseContent" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="案例详情编辑" prop="mainScene">
          <el-button type="primary" @click="EArrangeEdit()" size="mini">点击进入编辑</el-button>
          <img :src="form.mainScene" alt="" v-if="form.mainScene != null && form.mainScene != '' && form.mainScene.indexOf('http') > -1" style="width: 160px;height: 90px;margin-left:30px;">
          <!-- <el-input v-model="form.mainScene" type="textarea" :rows="3" placeholder="请输入案例详情编辑" maxlength="1000" show-word-limit /> -->
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button type="primary" v-if="region !='总部'" @click="handleExamine">提交审核</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改用户案例对话框 -->
    <el-dialog :title="title" :visible.sync="viewOpen" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业:" prop="industryCode">
              <div>{{setIndustryCode(form.industryCode)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否对外:" prop="isAbout">
              <div>{{form.isAbout==0?'否':'是'}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例标题:" prop="mainCaseTitle">
          <div>{{form.mainCaseTitle}}</div>
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="案例封面:" prop="mainCaseCover">
              <el-image style="width:150px;height:150px; margin: 0 auto;cursor:pointer;" :src="form.mainCaseCover" :z-index="9999" :preview-src-list="[form.mainCaseCover]">
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="案例内容" prop="mainCaseContent">
              <video v-if="fileName=='.mp4'" width="300px" height="150px" controls>
                <source :src="form.mainCaseContent" type="video/mp4">
              </video>

              <el-image v-else  style="width:150px;height:150px; margin: 0 auto;cursor:pointer;" :src="form.mainCaseContent" :z-index="9999" :preview-src-list="[form.mainCaseContent]">
              </el-image>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例详情编辑" prop="explainTitle">
          <el-image v-if="form.mainScene != null && form.mainScene != '' && form.mainScene.indexOf('http') > -1"
             style="width: 160px;height: 90px;margin-left:30px;cursor:pointer;" :src="form.mainScene" :z-index="9999" :preview-src-list="[form.mainScene]">
          </el-image>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- <el-dialog :title="title" :visible.sync="viewOpen" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业" prop="industryCode">
              <el-select v-model="form.industryCode" placeholder="请选择行业" disabled>
                <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否对外" prop="isAbout">
              <el-radio-group v-model="form.isAbout" disabled>
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="标签" prop="tagCode">
          <el-select v-model="form.tagCode" filterable multiple placeholder="请选择标签" disabled>
            <el-option
              v-for="dict in tagTypeList"
              :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="案例标题" prop="mainCaseTitle">
          <el-input v-model="form.mainCaseTitle" placeholder="请输入案例标题" maxlength="16" show-word-limit disabled />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="案例封面" prop="mainCaseCover">
              <ImageUpload @input="setInput" :value="form.mainCaseCover" :fileSize="30" accept=".jpg,.jpeg,.png" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案例视频" prop="mainCaseContent">
              <ImageUpload @input="setMainCaseContent"  tipContent="(备注:如没有视频可传图片,文件最大200M)" accept=".jpg,.jpeg,.png,.mp4" :value="form.mainCaseContent"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="案例详情编辑" prop="mainScene">
          <el-button type="primary" @click="EArrangeEdit()" size="mini" disabled>点击进入编辑</el-button>
          <img :src="form.mainScene" alt="" v-if="form.mainScene != null && form.mainScene != '' && form.mainScene.indexOf('http') > -1" style="width: 160px;height: 90px;margin-left:30px;" disabled>
        </el-form-item>
      </el-form>
    </el-dialog> -->

  </div>
</template>

<script>
import ImageUpload from "../../../../components/ImageUpload/ImageUpload";
import {
  add,
  update,
  deleteTo,
  detail,
  list,
  industrylist,
  examine,
} from "@/api/hzhb/case";
import { getToken } from "@/utils/auth";
import store from "@/store";

export default {
  name: "casemanage",
  components: {
    ImageUpload,
  },
  data() {
    return {
      divSFYX: null,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      tagTypeList: [],
      // 总条数
      total: 0,
      // 用户案例表格数据
      mainList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      viewOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mainCaseTitle: null,
        mainCaseCover: null,
        mainCaseContent: null,
        mainCaseExplain: null,
        userId: null,
        region: null,
        orderByColumn: "create_time",
        isAsc: "desc",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industryCode: [
          { required: true, message: "行业不能为空", trigger: "blur" },
        ],
        isAbout: [
          { required: true, message: "对内对外不能为空", trigger: "change" },
        ],
        mainCaseTitle: [
          { required: true, message: "案例标题不能为空", trigger: "blur" },
        ],
        mainCaseCover: [
          { required: true, message: "案例封面不能为空", trigger: "change" },
        ],
        mainCaseContent: [
          { required: true, message: "案例内容不能为空", trigger: "change" },
        ],
        mainScene: [
          { required: true, message: "案例详情不能为空", trigger: "change" },
        ],
      },
      industryList: [],
      email: store.getters.email,
      token: "",
      accountType: "",
    };
  },
  created() {
    this.getDicts("tag_type").then((response) => {
      this.tagTypeList = response.data;
    });
    this.getIndustryList();
    this.token = getToken();

    this.accountType = store.getters.accountType;
    console.log(this.accountType);

    this.region = store.getters.region;
    console.log(this.region);
    this.Move();

    let that = this;

    window.addEventListener(
      "message",
      (e) => {
        if (e.data != null) {
          if (e.data.data == "closeArrange") {
            that.arrangeClose();
            let data = e.data;
            if (
              that.form.uuid != null &&
              that.form.uuid == data.uuid &&
              data.image != "" &&
              data.image != null
            ) {
              that.form.img = data.image;
              that.form.arrangeId = data.id;
              that.form.mainScene = data.image;

              if (this.form.mainCaseId != null) {
                update(this.form).then((response) => {
                  this.msgSuccess("自动保存数据成功！");
                });
              } else {
                add(this.form).then((response) => {
                  if (response.code === 200) {
                    this.form.mainCaseId = response.data;
                    this.msgSuccess("自动保存数据成功！");
                  }
                });
              }
              that.getList();
            }
          }
        }
        console.log(e.data, e.data.data);
      },
      false
    );
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    //关闭e编排
    arrangeClose() {
      document.body.removeChild(this.divSFYX);
      this.open = true;
      this.Move();
    },
    EArrangeEdit() {
      let that = this;
      if (this.form.industryCode == null || this.form.industryCode == "") {
        that.$message.error("行业不能为空,编辑前请补充完基础数据");
        return false;
      }
      if (this.form.mainCaseTitle == null || this.form.mainCaseTitle == "") {
        that.$message.error("案例标题不能为空,编辑前请补充完基础数据");
        return false;
      }
      if (this.form.mainCaseCover == null || this.form.mainCaseCover == "") {
        that.$message.error("案例封面不能为空,编辑前请补充完基础数据");
        return false;
      }
      if (
        this.form.mainCaseContent == null ||
        this.form.mainCaseContent == ""
      ) {
        that.$message.error("案例内容不能为空,编辑前请补充完基础数据");
        return false;
      }

      let row = this.form;
      if (row.arrangeId != null && row.arrangeId != undefined)
        row.uuid = row.arrangeId;
      else if (row.mainCaseId == null)
        row.uuid =
          Math.floor(Math.random() * (99999999 - 10000000)) +
          10000000 +
          "" +
          (Math.floor(Math.random() * (99999999 - 10000000)) + 10000000);
      else row.uuid = row.mainCaseId;

      if (row.arrangeId == null || row.arrangeId == undefined) {
        row.arrangeId = row.uuid;
      }
      console.log(row);
      let url =
        "http://36.134.94.250:18080/ishowa/ebp/#/project/items?Email=" +
        this.email +
        "&Authorization=" +
        this.token +
        "&ProjectId=" +
        row.arrangeId +
        "&Uuid=" +
        row.uuid;
      //let url = "http://jiekou.meswl.com/ebp#/project/items?Email=" + this.email + "&Authorization=" + this.token + "&ProjectId=" + row.arrangeId + "&Uuid=" + row.uuid;
      that.divSFYX = window.document.createElement("div");
      let html =
        " <div style='width: 100vw;overflow: hidden;height: 100vh;position: absolute;top: 0;left: 0;z-index: 1001;' v-show='dialogTableVisible'>" +
        "<iframe style='width: 100%;padding: 0;border: 0;margin: 0;height: 100%;overflow: hidden;' scrolling='no' src='" +
        url +
        "' class='panorama-ifram-item'></iframe></div>";
      that.divSFYX.innerHTML = html;
      this.open = false;
      document.body.appendChild(that.divSFYX);
      this.stopMove();
    },
    //停止页面滚动
    stopMove() {
      let m = function (e) {
        e.preventDefault();
      };
      document.documentElement.style.overflow = "hidden";
      document.documentElement.scrollTop = 0;
      document.addEventListener("touchmove", m, { passive: false }); //禁止页面滑动
    },
    //开启页面滚动
    Move() {
      let m = function (e) {
        e.preventDefault();
      };
      document.documentElement.style.overflow = ""; //出现滚动条
      document.removeEventListener("touchmove", m, { passive: true });
    },
    setIndustryCode(code) {
      let name = "";
      this.industryList.forEach((item) => {
        if (item.industryCode == code) {
          name = item.industryName;
        }
      });
      return name;
    },
    getIndustryList() {
      let that = this;
      industrylist({}).then((res) => {
        if (res.code === 200) {
          that.industryList = res.data;
          this.getList();
        }
      });
    },
    setMainCaseContent(value) {
      this.$set(this.form, "mainCaseContent", value);
    },
    setInput(value) {
      this.$set(this.form, "mainCaseCover", value);
    },
    /** 查询用户案例列表 */
    getList() {
      this.loading = true;
      list(this.queryParams).then((response) => {
        this.mainList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mainCaseId: null,
        mainCaseTitle: null,
        mainCaseCover: null,
        mainCaseContent: null,
        createTime: null,
        userId: null,
        region: null,
        industryCode: null,
        isAbout: 0,
        explainTitle: null,
        projectExplain: null,
        mainScene: null,
        projectCost: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.mainCaseId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加落地案例";
      //console.log(this.form.mainScene != null && this.form.mainScene != '' && this.form.mainScene.indexOf('http') > -1)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let that = this;
      this.reset();
      const mainCaseId = row.mainCaseId || this.ids;
      detail(mainCaseId).then((response) => {
        this.form = response.data;
        this.form.isAbout = parseInt(this.form.isAbout);
        this.open = true;
        this.title = "修改落地案例";
      });
    },
    handleView(row) {
      let that = this;
      this.reset();
      const mainCaseId = row.mainCaseId || this.ids;
      detail(mainCaseId).then((response) => {
        this.form = response.data;
        this.form.isAbout = parseInt(this.form.isAbout);
        this.viewOpen = true;
        this.title = "查看落地案例";
      });
    },
    /** 提交按钮 */
    submitForm() {
      let that = this;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.mainCaseId != null) {
            update(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            add(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    handleExamine(row) {
      let that = this;
      if (row != null && row.mainCaseId != null) {
        examine({ mainCaseId: row.mainCaseId }).then((response) => {
          if (response.code === 200) {
            that.msgSuccess("提交审核成功");
            that.open = false;
            that.getList();
          }
        });
      } else {
        that.$refs["form"].validate((valid) => {
          if (valid) {
            examine(that.form).then((response) => {
              if (response.code === 200) {
                that.msgSuccess("提交审核成功");
                that.open = false;
                that.getList();
              }
            });
          }
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // let that = this;
      // const mainCaseIds = row.mainCaseId || this.ids;
      const mainCaseIds = row.mainCaseId == null ? this.ids : [row.mainCaseId];
      const names = this.mainList
        .filter((item) => this.ids.includes(item.mainCaseId))
        .map((item) => item.mainCaseTitle)
        .join(", ");

      this.$confirm(
        '是否确认删除用户案例标题为"' + names + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return deleteTo(mainCaseIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.mb8 {
  margin-bottom: 8px;
}
.panorama-ifram {
  width: 100vw;
  overflow: hidden;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1001;
}
.panorama-ifram-item {
  width: 100%;
  padding: 0;
  border: 0;
  margin: 0;
  height: 100%;
  overflow: hidden;
}

::v-deep .el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 40px;
      font-size: 13px;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*="el-icon-"] + span {
      margin-left: 1px;
    }
  }
}
</style>
