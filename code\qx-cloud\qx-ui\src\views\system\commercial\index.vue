<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业名称" prop="industryName">
        <el-input v-model="queryParams.industryName" placeholder="请输入行业名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="标题" prop="industryName">
        <el-input v-model="queryParams.industryName" placeholder="请输入标题" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:industry:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:industry:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:industry:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:industry:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="networkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行业名称" align="center" prop="industryName" />
      <el-table-column label="标题" align="center" prop="title" >
        <template slot-scope="scope">
         <span>{{  scope.row.title.replace('商业价值', '成本预估') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="封面图" align="center" prop="photoUrl">
        <template slot-scope="scope">
          <img style="width:50px;height:50px;" :src="scope.row.photoUrl" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:industry:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:industry:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改行业对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="行业" prop="industryId">
          <el-select v-model="form.industryId" placeholder="请选择行业">
            <el-option v-for="item in industryList" :key="item.id" :label="item.industryName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tagCode">
          <el-select v-model="form.tagCode" filterable multiple placeholder="请选择标签">
            <el-option
              v-for="dict in tagTypeList"
              :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="封面图" prop="photoUrl">
          <ImageUpload @input="setInput" :value="form.photoUrl" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {list,add,upd,del} from "@/api/system/commercial";
import ImageUpload from "../../../components/ImageUpload";
import {listIndustry} from "@/api/system/industry";
import PanoramaUpload from "@/components/PanoramaUpload";
export default {
  name: "Industry",
  components: {
    ImageUpload, PanoramaUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 行业表格数据
      industryList: [],
      networkList: [],
      tagTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industryId: [
          { required: true, message: "行业不能为空", trigger: "blur" },
        ],
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" },
        ],
        sceneUrl: [
          { required: true, message: "封面图不能为空", trigger: "blur" },
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("tag_type").then(response => {
      this.tagTypeList = response.data;
    });
  },
  methods: {
    setInput(value) {
      this.$set(this.form, "photoUrl", value);
    },
    /** 查询行业列表 */
    getList() {
      this.loading = true;
      list(this.queryParams).then(response => {
        this.networkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      listIndustry(this.queryParams).then(response => {
        this.industryList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        industryId:null,
        industryName:null,
        title:null,
        tagCode: [],
        photoUrl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加成本预估";
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.form = {
        ...row
      };
      this.open = true;
      this.title = "修改成本预估";
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.id != null) {
        upd(this.form).then(response => {
          this.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      } else {
        add(this.form).then(response => {
          this.msgSuccess("新增成功");
          this.open = false;
          this.getList();
        });
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id || this.ids;
      this.$confirm('是否确认删除'+id+'数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then((response) => {
        return del(id);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },

    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的行业数据';
      }else {
        msg = '是否确认导出成本预估数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/industry/export', exportParams, `industry_${new Date().getTime()}.xlsx`)
      }).catch({});
    }
  }
};
</script>
