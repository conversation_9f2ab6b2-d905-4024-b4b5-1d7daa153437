<template>
    <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '300px'
        },
        echartData: {
            type: Array,
            default: []
        },
        color: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            chart: null
        }
    },
    watch: {
        // echartData: 变化initChart
        echartData: {
            handler(newVal, oldVal) {
                if (this.chart) {
                    this.chart.dispose()
                    this.chart = null
                }
                this.initChart()
            },
            deep: true
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            // background: #FFAA57 #4F7AFC #93BEFF;
            let bgColor = '#fff'
            let color = this.color
            this.chart = echarts.init(this.$el, 'macarons')
            let echartData = this.echartData
            let formatNumber = function(num) {
                let reg = /(?=(\B)(\d{3})+$)/g
                return num.toString().replace(reg, ',')
            }
            this.chart.setOption({
                backgroundColor: bgColor,
                color: color,
                tooltip: {
                    trigger: 'item',
                    formatter: ' {c} ({d}%)'
                },
                legend: {
                    left: 'center',
                    bottom: '0',
                    color: '#000',
                    icon: 'circle', // 设置图例的指示点为圆点
                    formatter: function(name) {
                        // 假设 echartData 是一个包含数据的对象数组
                        const item = echartData.find(item => item.name === name)
                        if (item) {
                            // 计算 value 的最大长度
                            const maxValueLength = Math.max(...echartData.map(item => formatNumber(item.value).length))
                            const maxNameLength = 10 // 设置 name 的最大长度
                            // 确保 name 部分与 value 部分对齐
                            const paddedName = name.padEnd(maxNameLength, ' ')
                            const paddedValue = formatNumber(item.value).padStart(maxValueLength, ' ')
                            return `{name|${paddedName}} {value|${paddedValue}人}`
                        }
                        return name
                    },
                    // 配置图例项的样式
                    textStyle: {
                        rich: {
                            name: {
                                width: 140, // 设定 name 部分的宽度
                                align: 'left'
                            },
                            value: {
                                width: 50, // 设定 value 部分的宽度
                                align: 'left'
                            }
                        }
                    }
                },
                series: [
                    {
                        type: 'pie',
                        radius: ['50%', '70%'],
                        center: ['50%', '38%'],
                        data: echartData,
                        emphasis: {
                            scale: false
                        },
                        label: {
                            show: false // 隐藏标签
                        },
                        labelLine: {
                            show: false // 隐藏指示线
                        },
                        animationEasing: 'cubicInOut',
                        animationDuration: 2600
                    }
                ]
            })
        }
    }
}
</script>
