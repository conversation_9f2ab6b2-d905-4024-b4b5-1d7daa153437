{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\searchlog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\searchlog\\index.vue", "mtime": 1755850488695}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747036191864}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747036191888}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RTZWFyY2hMb2csIGdldFNlYXJjaERldGFpbCB9IGZyb20gIkAvYXBpL3N5c3RlbS9zZWFyY2hsb2ciOwppbXBvcnQgeyBhZGREYXRlUmFuZ2UgfSBmcm9tICJAL3V0aWxzL3F4IjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2VhcmNoTG9nIiwKICBjb21wb25lbnRzOiB7CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOeDremXqOaQnOe0ouiusOW9leihqOagvOaVsOaNrgogICAgICBzZWFyY2hMb2dMaXN0OiBbXSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGtleXdvcmQ6IG51bGwsCiAgICAgICAgY2xpY2tDb3VudE1pbjogbnVsbCwKICAgICAgICBjbGlja0NvdW50TWF4OiBudWxsLAogICAgICAgIHRlcm1pbmFsOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOivpuaDheWvueivneahhgogICAgICBkZXRhaWxPcGVuOiBmYWxzZSwKICAgICAgZGV0YWlsTG9hZGluZzogZmFsc2UsCiAgICAgIGRldGFpbFRvdGFsOiAwLAogICAgICBkZXRhaWxMaXN0OiBbXSwKICAgICAgY3VycmVudEtleXdvcmQ6ICcnLAogICAgICBkZXRhaWxEYXRlUmFuZ2U6IFtdLAogICAgICBkZXRhaWxRdWVyeVBhcmFtczogewogICAgICAgIGtleXdvcmQ6ICcnLAogICAgICAgIHRlcm1pbmFsOiAwLAogICAgICAgIHBhZ2VObzogMSwKICAgICAgICBwYWdlU2l6ZTogMjAKICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lng63pl6jmkJzntKLorrDlvZXliJfooaggKi8KICAgIGdldExpc3QoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGxldCBwYXJhbXMgPSBhZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpOwogICAgICAvLyDnoa7kv53ml7bpl7Tlj4LmlbDmraPnoa7kvKDpgJIKICAgICAgaWYgKHRoaXMuZGF0ZVJhbmdlICYmIHRoaXMuZGF0ZVJhbmdlLmxlbmd0aCA9PT0gMikgewogICAgICAgIHBhcmFtcy5iZWdpblRpbWUgPSB0aGlzLmRhdGVSYW5nZVswXTsKICAgICAgICBwYXJhbXMuZW5kVGltZSA9IHRoaXMuZGF0ZVJhbmdlWzFdOwogICAgICB9CiAgICAgIGxpc3RTZWFyY2hMb2cocGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnNlYXJjaExvZ0xpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKiDor6bmg4XmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZURldGFpbChyb3cpIHsKICAgICAgdGhpcy5jdXJyZW50S2V5d29yZCA9IHJvdy5rZXl3b3JkOwogICAgICB0aGlzLmRldGFpbFF1ZXJ5UGFyYW1zLmtleXdvcmQgPSByb3cua2V5d29yZDsKICAgICAgLy8g6buY6K6k5L2/55So5YiX6KGo5b2T5YmN6YCJ5oup55qE57uI56uv57G75Z6LCiAgICAgIHRoaXMuZGV0YWlsUXVlcnlQYXJhbXMudGVybWluYWwgPSB0aGlzLnF1ZXJ5UGFyYW1zLnRlcm1pbmFsIHx8IDA7CiAgICAgIHRoaXMuZGV0YWlsUXVlcnlQYXJhbXMucGFnZU5vID0gMTsKICAgICAgdGhpcy5kZXRhaWxEYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5kZXRhaWxPcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5nZXREZXRhaWxMaXN0KCk7CiAgICB9LAogICAgLyoqIOafpeivouivpuaDheWIl+ihqCAqLwogICAgZ2V0RGV0YWlsTGlzdCgpIHsKICAgICAgdGhpcy5kZXRhaWxMb2FkaW5nID0gdHJ1ZTsKICAgICAgbGV0IHBhcmFtcyA9IHsgLi4udGhpcy5kZXRhaWxRdWVyeVBhcmFtcyB9OwoKICAgICAgLy8g5aSE55CG5pe26Ze06IyD5Zu0CiAgICAgIGlmICh0aGlzLmRldGFpbERhdGVSYW5nZSAmJiB0aGlzLmRldGFpbERhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICBwYXJhbXMuc3RhcnRUaW1lID0gdGhpcy5kZXRhaWxEYXRlUmFuZ2VbMF07CiAgICAgICAgcGFyYW1zLmVuZFRpbWUgPSB0aGlzLmRldGFpbERhdGVSYW5nZVsxXTsKICAgICAgfQoKICAgICAgZ2V0U2VhcmNoRGV0YWlsKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5kZXRhaWxMaXN0ID0gcmVzcG9uc2UubGlzdDsKICAgICAgICB0aGlzLmRldGFpbFRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5kZXRhaWxMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDorqHnrpfluo/lj7cgKi8KICAgIGdldEluZGV4KGluZGV4KSB7CiAgICAgIHJldHVybiAodGhpcy5kZXRhaWxRdWVyeVBhcmFtcy5wYWdlTm8gLSAxKSAqIHRoaXMuZGV0YWlsUXVlcnlQYXJhbXMucGFnZVNpemUgKyBpbmRleCArIDE7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/searchlog", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"关键词\" prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"请输入关键词\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"点击量\" prop=\"clickCountMin\">\n        <el-input\n          v-model=\"queryParams.clickCountMin\"\n          placeholder=\"最小值\"\n          clearable\n          size=\"small\"\n          style=\"width: 100px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n        <span style=\"margin: 0 5px;\">-</span>\n        <el-input\n          v-model=\"queryParams.clickCountMax\"\n          placeholder=\"最大值\"\n          clearable\n          size=\"small\"\n          style=\"width: 100px;\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"终端\" prop=\"terminal\">\n        <el-select v-model=\"queryParams.terminal\" placeholder=\"请选择终端\" clearable size=\"small\">\n          <el-option label=\"全部\" :value=\"0\" />\n          <el-option label=\"PC端\" :value=\"1\" />\n          <el-option label=\"智库APP\" :value=\"2\" />\n          <el-option label=\"移动端办公APP\" :value=\"3\" />\n          <el-option label=\"网大\" :value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"searchLogList\">\n      <el-table-column label=\"关键词\" align=\"center\" prop=\"keyword\" />\n      <el-table-column label=\"点击量\" align=\"center\" prop=\"clickCount\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleDetail(scope.row)\"\n          >详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 详情对话框 -->\n    <el-dialog title=\"点击详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-form :model=\"detailQueryParams\" ref=\"detailQueryForm\" :inline=\"true\" label-width=\"80px\">\n        <el-form-item label=\"关键词\">\n          <span>{{ currentKeyword }}</span>\n        </el-form-item>\n        <el-form-item label=\"终端\" prop=\"terminal\">\n          <el-select v-model=\"detailQueryParams.terminal\" placeholder=\"请选择终端\" size=\"small\" @change=\"getDetailList\">\n            <el-option label=\"全部\" :value=\"0\" />\n            <el-option label=\"PC端\" :value=\"1\" />\n            <el-option label=\"智库APP\" :value=\"2\" />\n            <el-option label=\"移动端办公APP\" :value=\"3\" />\n            <el-option label=\"网大\" :value=\"4\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"时间\">\n          <el-date-picker\n            v-model=\"detailDateRange\"\n            size=\"small\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n            type=\"datetimerange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始时间\"\n            end-placeholder=\"结束时间\"\n            @change=\"getDetailList\"\n          ></el-date-picker>\n        </el-form-item>\n      </el-form>\n      \n      <el-table v-loading=\"detailLoading\" :data=\"detailList\" max-height=\"400\">\n        <el-table-column label=\"序号\" align=\"center\" width=\"80\" type=\"index\" :index=\"getIndex\" />\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\n        <el-table-column label=\"点击时间\" align=\"center\" prop=\"clickTime\" width=\"180\" />\n      </el-table>\n      \n      <pagination\n        v-show=\"detailTotal>0\"\n        :total=\"detailTotal\"\n        :page.sync=\"detailQueryParams.pageNo\"\n        :limit.sync=\"detailQueryParams.pageSize\"\n        @pagination=\"getDetailList\"\n        style=\"margin-top: 20px;\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listSearchLog, getSearchDetail } from \"@/api/system/searchlog\";\nimport { addDateRange } from \"@/utils/qx\";\n\nexport default {\n  name: \"SearchLog\",\n  components: {\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 热门搜索记录表格数据\n      searchLogList: [],\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: null,\n        clickCountMin: null,\n        clickCountMax: null,\n        terminal: null\n      },\n      // 详情对话框\n      detailOpen: false,\n      detailLoading: false,\n      detailTotal: 0,\n      detailList: [],\n      currentKeyword: '',\n      detailDateRange: [],\n      detailQueryParams: {\n        keyword: '',\n        terminal: 0,\n        pageNo: 1,\n        pageSize: 20\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询热门搜索记录列表 */\n    getList() {\n      this.loading = true;\n      let params = addDateRange(this.queryParams, this.dateRange);\n      // 确保时间参数正确传递\n      if (this.dateRange && this.dateRange.length === 2) {\n        params.beginTime = this.dateRange[0];\n        params.endTime = this.dateRange[1];\n      }\n      listSearchLog(params).then(response => {\n        this.searchLogList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 详情按钮操作 */\n    handleDetail(row) {\n      this.currentKeyword = row.keyword;\n      this.detailQueryParams.keyword = row.keyword;\n      // 默认使用列表当前选择的终端类型\n      this.detailQueryParams.terminal = this.queryParams.terminal || 0;\n      this.detailQueryParams.pageNo = 1;\n      this.detailDateRange = [];\n      this.detailOpen = true;\n      this.getDetailList();\n    },\n    /** 查询详情列表 */\n    getDetailList() {\n      this.detailLoading = true;\n      let params = { ...this.detailQueryParams };\n\n      // 处理时间范围\n      if (this.detailDateRange && this.detailDateRange.length === 2) {\n        params.startTime = this.detailDateRange[0];\n        params.endTime = this.detailDateRange[1];\n      }\n\n      getSearchDetail(params).then(response => {\n        this.detailList = response.list;\n        this.detailTotal = response.total;\n        this.detailLoading = false;\n      });\n    },\n    /** 计算序号 */\n    getIndex(index) {\n      return (this.detailQueryParams.pageNo - 1) * this.detailQueryParams.pageSize + index + 1;\n    }\n  }\n};\n</script>\n"]}]}