{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\searchlog\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\searchlog\\index.vue", "mtime": 1755848115336}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747036191864}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747036191888}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RTZWFyY2hMb2csIGdldFNlYXJjaERldGFpbCB9IGZyb20gIkAvYXBpL3N5c3RlbS9zZWFyY2hsb2ciOwppbXBvcnQgeyBhZGREYXRlUmFuZ2UgfSBmcm9tICJAL3V0aWxzL3F4IjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2VhcmNoTG9nIiwKICBjb21wb25lbnRzOiB7CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy8g6YGu572p5bGCCiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOeDremXqOaQnOe0ouiusOW9leihqOagvOaVsOaNrgogICAgICBzZWFyY2hMb2dMaXN0OiBbXSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGtleXdvcmQ6IG51bGwsCiAgICAgICAgY2xpY2tDb3VudDogbnVsbCwKICAgICAgICB0ZXJtaW5hbDogbnVsbAogICAgICB9LAogICAgICAvLyDor6bmg4Xlr7nor53moYYKICAgICAgZGV0YWlsT3BlbjogZmFsc2UsCiAgICAgIGRldGFpbExvYWRpbmc6IGZhbHNlLAogICAgICBkZXRhaWxUb3RhbDogMCwKICAgICAgZGV0YWlsTGlzdDogW10sCiAgICAgIGN1cnJlbnRLZXl3b3JkOiAnJywKICAgICAgZGV0YWlsRGF0ZVJhbmdlOiBbXSwKICAgICAgZGV0YWlsUXVlcnlQYXJhbXM6IHsKICAgICAgICBrZXl3b3JkOiAnJywKICAgICAgICB0ZXJtaW5hbDogMCwKICAgICAgICBwYWdlTm86IDEsCiAgICAgICAgcGFnZVNpemU6IDIwCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i54Ot6Zeo5pCc57Si6K6w5b2V5YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbXMgPSBhZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpOwogICAgICBsaXN0U2VhcmNoTG9nKHBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5zZWFyY2hMb2dMaXN0ID0gcmVzcG9uc2Uucm93czsKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKiog6K+m5oOF5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZXRhaWwocm93KSB7CiAgICAgIHRoaXMuY3VycmVudEtleXdvcmQgPSByb3cua2V5d29yZDsKICAgICAgdGhpcy5kZXRhaWxRdWVyeVBhcmFtcy5rZXl3b3JkID0gcm93LmtleXdvcmQ7CiAgICAgIHRoaXMuZGV0YWlsUXVlcnlQYXJhbXMudGVybWluYWwgPSAwOwogICAgICB0aGlzLmRldGFpbFF1ZXJ5UGFyYW1zLnBhZ2VObyA9IDE7CiAgICAgIHRoaXMuZGV0YWlsRGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMuZGV0YWlsT3BlbiA9IHRydWU7CiAgICAgIHRoaXMuZ2V0RGV0YWlsTGlzdCgpOwogICAgfSwKICAgIC8qKiDmn6Xor6Lor6bmg4XliJfooaggKi8KICAgIGdldERldGFpbExpc3QoKSB7CiAgICAgIHRoaXMuZGV0YWlsTG9hZGluZyA9IHRydWU7CiAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnRoaXMuZGV0YWlsUXVlcnlQYXJhbXMgfTsKICAgICAgCiAgICAgIC8vIOWkhOeQhuaXtumXtOiMg+WbtAogICAgICBpZiAodGhpcy5kZXRhaWxEYXRlUmFuZ2UgJiYgdGhpcy5kZXRhaWxEYXRlUmFuZ2UubGVuZ3RoID09PSAyKSB7CiAgICAgICAgcGFyYW1zLnN0YXJ0VGltZSA9IHRoaXMuZGV0YWlsRGF0ZVJhbmdlWzBdOwogICAgICAgIHBhcmFtcy5lbmRUaW1lID0gdGhpcy5kZXRhaWxEYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgCiAgICAgIGdldFNlYXJjaERldGFpbChwYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZGV0YWlsTGlzdCA9IHJlc3BvbnNlLmxpc3Q7CiAgICAgICAgdGhpcy5kZXRhaWxUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOwogICAgICAgIHRoaXMuZGV0YWlsTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/searchlog", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"关键词\" prop=\"keyword\">\n        <el-input\n          v-model=\"queryParams.keyword\"\n          placeholder=\"请输入关键词\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"点击量\" prop=\"clickCount\">\n        <el-input\n          v-model=\"queryParams.clickCount\"\n          placeholder=\"请输入点击量\"\n          clearable\n          size=\"small\"\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"终端\" prop=\"terminal\">\n        <el-select v-model=\"queryParams.terminal\" placeholder=\"请选择终端\" clearable size=\"small\">\n          <el-option label=\"全部\" :value=\"0\" />\n          <el-option label=\"PC端\" :value=\"1\" />\n          <el-option label=\"智库APP\" :value=\"2\" />\n          <el-option label=\"移动端办公APP\" :value=\"3\" />\n          <el-option label=\"网大\" :value=\"4\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"时间\">\n        <el-date-picker\n          v-model=\"dateRange\"\n          size=\"small\"\n          style=\"width: 240px\"\n          value-format=\"yyyy-MM-dd\"\n          type=\"daterange\"\n          range-separator=\"-\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n        ></el-date-picker>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table v-loading=\"loading\" :data=\"searchLogList\">\n      <el-table-column label=\"关键词\" align=\"center\" prop=\"keyword\" />\n      <el-table-column label=\"点击量\" align=\"center\" prop=\"clickCount\" />\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-view\"\n            @click=\"handleDetail(scope.row)\"\n          >详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    \n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 详情对话框 -->\n    <el-dialog title=\"点击详情\" :visible.sync=\"detailOpen\" width=\"800px\" append-to-body>\n      <el-form :model=\"detailQueryParams\" ref=\"detailQueryForm\" :inline=\"true\" label-width=\"80px\">\n        <el-form-item label=\"关键词\">\n          <span>{{ currentKeyword }}</span>\n        </el-form-item>\n        <el-form-item label=\"终端\" prop=\"terminal\">\n          <el-select v-model=\"detailQueryParams.terminal\" placeholder=\"请选择终端\" size=\"small\" @change=\"getDetailList\">\n            <el-option label=\"全部\" :value=\"0\" />\n            <el-option label=\"PC端\" :value=\"1\" />\n            <el-option label=\"智库APP\" :value=\"2\" />\n            <el-option label=\"移动端办公APP\" :value=\"3\" />\n            <el-option label=\"网大\" :value=\"4\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"时间\">\n          <el-date-picker\n            v-model=\"detailDateRange\"\n            size=\"small\"\n            style=\"width: 240px\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n            type=\"datetimerange\"\n            range-separator=\"-\"\n            start-placeholder=\"开始时间\"\n            end-placeholder=\"结束时间\"\n            @change=\"getDetailList\"\n          ></el-date-picker>\n        </el-form-item>\n      </el-form>\n      \n      <el-table v-loading=\"detailLoading\" :data=\"detailList\" max-height=\"400\">\n        <el-table-column label=\"ID\" align=\"center\" prop=\"id\" width=\"80\" />\n        <el-table-column label=\"姓名\" align=\"center\" prop=\"name\" />\n        <el-table-column label=\"点击时间\" align=\"center\" prop=\"clickTime\" width=\"180\" />\n      </el-table>\n      \n      <pagination\n        v-show=\"detailTotal>0\"\n        :total=\"detailTotal\"\n        :page.sync=\"detailQueryParams.pageNo\"\n        :limit.sync=\"detailQueryParams.pageSize\"\n        @pagination=\"getDetailList\"\n        style=\"margin-top: 20px;\"\n      />\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailOpen = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listSearchLog, getSearchDetail } from \"@/api/system/searchlog\";\nimport { addDateRange } from \"@/utils/qx\";\n\nexport default {\n  name: \"SearchLog\",\n  components: {\n  },\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 热门搜索记录表格数据\n      searchLogList: [],\n      // 日期范围\n      dateRange: [],\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        keyword: null,\n        clickCount: null,\n        terminal: null\n      },\n      // 详情对话框\n      detailOpen: false,\n      detailLoading: false,\n      detailTotal: 0,\n      detailList: [],\n      currentKeyword: '',\n      detailDateRange: [],\n      detailQueryParams: {\n        keyword: '',\n        terminal: 0,\n        pageNo: 1,\n        pageSize: 20\n      }\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询热门搜索记录列表 */\n    getList() {\n      this.loading = true;\n      const params = addDateRange(this.queryParams, this.dateRange);\n      listSearchLog(params).then(response => {\n        this.searchLogList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.dateRange = [];\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    /** 详情按钮操作 */\n    handleDetail(row) {\n      this.currentKeyword = row.keyword;\n      this.detailQueryParams.keyword = row.keyword;\n      this.detailQueryParams.terminal = 0;\n      this.detailQueryParams.pageNo = 1;\n      this.detailDateRange = [];\n      this.detailOpen = true;\n      this.getDetailList();\n    },\n    /** 查询详情列表 */\n    getDetailList() {\n      this.detailLoading = true;\n      let params = { ...this.detailQueryParams };\n      \n      // 处理时间范围\n      if (this.detailDateRange && this.detailDateRange.length === 2) {\n        params.startTime = this.detailDateRange[0];\n        params.endTime = this.detailDateRange[1];\n      }\n      \n      getSearchDetail(params).then(response => {\n        this.detailList = response.list;\n        this.detailTotal = response.total;\n        this.detailLoading = false;\n      });\n    }\n  }\n};\n</script>\n"]}]}