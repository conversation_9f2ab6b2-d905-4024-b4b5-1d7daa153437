<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="150px">
      <el-form-item label="送审类型" prop="examineType">
        <el-select v-model="queryParams.examineType" placeholder="请选择送审类型" clearable size="small">
          <el-option v-for="dict in examineTypeOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="examineState">
        <el-select size="small" v-model="queryParams.examineState" placeholder="请选择审核状态">
          <el-option :key="-1" label="未审核" :value="-1"></el-option>
          <el-option :key="0" label="审核中" :value="0"></el-option>
          <el-option :key="1" label="通过" :value="1"></el-option>
          <el-option :key="2" label="拒绝" :value="2"></el-option>
          <el-option :key="3" label="疑似" :value="3"></el-option>
          <el-option :key="4" label="失败" :value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="送审标题" prop="examineTitle">
        <el-input v-model="queryParams.examineTitle" placeholder="请输入文件路径名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-s-promotion" size="mini" @click="handleExamine" v-hasPermi="['examine:contentexamine:export']">送审</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contentexamineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务ID" align="center" prop="taskId" />
      <el-table-column label="送审类型" align="center" prop="examineType">
        <template slot-scope="scope">
          <span>{{getexamineType(scope.row.examineType)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="送审标题" align="center" prop="examineTitle" />
      <el-table-column label="送审时间" align="center" prop="lastModified" width="180">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.lastModified, "{y}-{m}-{d} {h}:{s}:{i}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审批时间" align="center" prop="deliveryTime" width="180">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.deliveryTime, "{y}-{m}-{d} {h}:{s}:{i}")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column label="送审描述" align="center" prop="describetion" />
      <!-- <el-table-column label="送审时间" align="center" prop="deliveryTime" width="180"/> -->
      <el-table-column label="审核状态" align="center" prop="examineState" width="200">
        <template slot-scope="scope">
          <span>{{
            scope.row.examineState == -1 || scope.row.examineState == null ? "未审核"
              : scope.row.examineState == 0 ? "审核中"
              : scope.row.examineState == 1 ? "通过"  : scope.row.examineState == 2? "拒绝"  : scope.row.examineState == 3? "疑似": "失败"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleUpdate(scope.row)" v-hasPermi="['examine:contentexamine:edit']">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-row :gutter="5" type="flex" style="flex-wrap:wrap">
          <el-col :span="24">
            <el-form-item label="送审标题" prop="key">
              <label>{{form.examineTitle}}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="5" type="flex" style="flex-wrap:wrap">
          <el-col :span="8">
            <el-form-item label="送审类型" prop="bucketName">
              <label>{{form.bucketName}}</label>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送审时间" prop="lastModified">
              <label>{{parseTime(form.lastModified , "{y}-{m}-{d} {h}:{s}:{i}")}}</label>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审核状态" prop="examineState">
              {{
            form.examineState == -1 || form.examineState == null? "未审核": form.examineState == 0? "审核中": form.examineState == 1
              ? "通过"  : form.examineState == 2? "拒绝"  : form.examineState == 3? "疑似": "失败"
          }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="5" type="flex" style="flex-wrap:wrap">
          <el-col :span="8">
            <el-form-item label="审批时间" prop="deliveryTime">
              <label>{{parseTime(form.deliveryTime , "{y}-{m}-{d} {h}:{s}:{i}")}}</label>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="任务ID" prop="taskId">
              <label>{{form.taskId}}</label>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="审核描述" prop="describetion">
              <label>{{form.describetion}}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="5" type="flex" style="flex-wrap:wrap">
          <el-col :span="24">
            <el-form-item label="机审结果" prop="machineAudit">
              <label>{{form.machineAudit}}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="5" type="flex" style="flex-wrap:wrap">
          <el-col :span="24">
            <el-form-item label="人审结果" prop="manualAudit">
              <label>{{form.manualAudit}}</label>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="5" type="flex" style="flex-wrap:wrap">
          <el-col :span="24">
            <el-form-item label="送审内容" prop="key">
              <label>{{form.key}}</label>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>

    <el-dialog title="送审路径" :visible.sync="openSetExamine" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="examineform" :model="examineform" :rules="examinerules" label-width="80px">
        <el-form-item label="送审类型" prop="examineType">
          <el-select v-model="examineform.examineType" placeholder="请选择送审类型" clearable>
            <el-option v-for="dict in examineTypeOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
          </el-select>
        </el-form-item>

        <el-form-item v-show="examineform.examineType==0" label="标题" prop="examineTitle">
          <el-input v-model="examineform.examineTitle" placeholder="请输入送审标题" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item v-show="examineform.examineType==0" label="EOS路径" prop="key">
          <el-input v-model="examineform.key" placeholder="请输入EOS对象存储路径" maxlength="100" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitExamineForm">确 定</el-button>
        <el-button @click="cancelExamine">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listContentexamine, getContentexamine, getExamine } from "@/api/examine/contentexamine";

export default {
  name: "Contentexamine",
  data() {
    return {
      openSetExamine: false,
      examineform: {},
      // 表单校验

      examinerules: {
        examineType: [{ required: true, message: "送审类型不能为空", trigger: "change" }],
        key: [
          {
            required: true,
            validator: (rule, value, callback) => {
              // 自定义验证逻辑
              if (!value && this.examineform.examineType == 0) {
                callback(new Error("送审EOS对象存储路径不能为空"));
              } else {
                callback(); // 验证通过
              }
            },
            trigger: "blur"
          }
        ],
        examineTitle: [
          {
            required: true,
            validator: (rule, value, callback) => {
              // 自定义验证逻辑
              if (!value && this.examineform.examineType == 0) {
                callback(new Error("送审标题不能为空"));
              } else {
                callback(); // 验证通过
              }
            },
            trigger: "blur"
          }
        ]
      },

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 内容审核表格数据
      contentexamineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bucketName: null,
        key: null,
        lastModified: null,
        size: null,
        deliveryTime: null,
        status: null,
        taskId: null,
        describetion: null,
        machineAudit: null,
        manualAudit: null,
        examineState: null
      },
      // 表单参数
      form: {},
      examineTypeOptions: []
    };
  },
  created() {
    this.getList();
    this.getDicts("qx_content_examine_type").then(response => {
      this.examineTypeOptions = response.data;
    });
  },
  methods: {
    getexamineType(dictValue) {
      for (let i = 0; i < this.examineTypeOptions.length; i++) {
        if (this.examineTypeOptions[i].dictValue == dictValue) return this.examineTypeOptions[i].dictLabel;
      }
      return "";
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        bucketName: null,
        key: null,
        lastModified: null,
        size: null,
        deliveryTime: null,
        status: null,
        taskId: null,
        describetion: null,
        machineAudit: null,
        manualAudit: null,
        examineState: null
      };
      this.resetForm("form");
    },
    handleExamine() {
      this.examineform = {
        examineTitle: null,
        key: null
      };
      this.openSetExamine = true;
    },
    cancelExamine() {
      this.openSetExamine = false;
      this.examineform = {
        examineTitle: null,
        key: null
      };
    },
    /** 提交按钮 */
    submitExamineForm() {
      this.$refs["examineform"].validate(valid => {
        if (valid) {
            getExamine(this.examineform)
              .then(response => {
                this.msgSuccess("送审成功");
                this.openSetExamine = false;
                this.getList();
              })
              .catch(r => {
                this.msgSuccess("送审失败，请联系系统管理员");
              });
        }
      });
    },
    /** 查询内容审核列表 */
    getList() {
      this.loading = true;
      listContentexamine(this.queryParams).then(response => {
        this.contentexamineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getContentexamine(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "内容审核详情";
      });
    }
  }
};
</script>
