<template>
    <div class="app-container">
        <div class="classftion-right-big-tltle">
            <img src="@/assets/images/index/biaoti.png" alt="" />
            <div>用户点赞数</div>
            <div class="more" @click="handleMoreClick('usersupport')">查看更多</div>

            <!-- <div class="title-change" @click="titleChange()"></div> -->
        </div>

        <div class="support-chart" v-if="dayDateList.length">
            <div class="tlite">各行业点赞</div>
            <div :style="'height: 340px;width:'+(linxAxisData*28)+'px;'">
            <barChart ref="barChart" :linxAxisData="linxAxisData" :linchartData="linchartData" :height="'380px'"
                :width="'2500px'" class="lineclickchart" :types="1" />
            </div>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
import { listAll } from '@/api/system/support'
import barChart from './lineclickchart.vue'
import { listIndustry } from '@/api/system/industry'

export default {
    name: 'userBrisk',
    components: {
        barChart
    },
    data() {
        return {
            isShow: false,
            queryParams: {},
            // dayDate: [],
            dayDateList: [],

            list1: [],
            //行业列表
            industryList: [],
            linxAxisData: [],
            linchartData: []
        }
    },
    props: {
        dayDate: {
            type: Array,
            default: []
        }
    },
    watch: {
        // 监听 Date 数组的变化
        dayDate: {
            handler() {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
                this.getDaySum()
            },
            // immediate: true, // 初始化时立即执行一次
            deep: true // 深度监听
        }
    },
    created() {
        // const end = new Date()
        // const start = new Date()
        // start.setTime(start.getTime() - 3600 * 1000 * 24 * 10)
        // this.dayDate = [start, end]

        setScale()
        window.onresize = () => {
            setScale()
        }
        this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
        this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
        listIndustry().then(response => {
            this.industryList = response.rows
            this.getDaySum()
        })
    },
    methods: {
        // 跳转传参数 开始 结束时间
        handleMoreClick(url) {
            this.$router.push("indexDetail/" + url + "?dayDate=" + this.dayDate)

            console.log(url)
        },
        //切换图表列表
        titleChange() {
            this.isShow = !this.isShow
        },

        downClick(name) {
            let that = this
            that.$refs[name].focus()
        },
        getDaySum() {
            let that = this
            // this.queryParams.params = {}
            // if (this.dayDate.length > 0) {
            //     this.queryParams.params.createTime = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
            //     this.queryParams.params.endTime = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            // }
            let echartMapXData = []
            let echartNumYData = []
            listAll(this.queryParams).then(response => {
                this.dayDateList = response.data

                that.industryList.forEach(row => {
                    let isAdd = true
                    that.dayDateList.forEach(item => {
                        if (item.industryCode == row.industryCode) isAdd = false
                    })
                    if (isAdd) {
                        that.dayDateList.push({ supportCount: 0, industryName: row.industryName, industryCode: row.industryCode })
                    }
                })
                that.dayDateList.forEach(item => {
                    echartNumYData.push(item.supportCount)
                    echartMapXData.push(item.industryName)
                })
                console.log(echartMapXData, echartNumYData)
                this.linchartData = echartNumYData
                this.linxAxisData = echartMapXData
                // that.$refs['barChart'].initChart(echartMapXData, echartNumYData)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0em;

    .classftion-right-big-tltle {
        font-size: 24px;
        font-weight: 900;
        color: #0066e4;
        display: flex;
        align-items: center;
        margin: 20px 0;

        img {
            width: 18px;
            height: 24px;
            margin-right: 16px;
        }

        .more {
            flex: 1;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
            position: relative;
            padding-right: 10px;
            cursor: pointer;
        }

        .more::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            border-top: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            border-right: 2px solid #0066e4;
            /* 修改颜色以适应你的设计 */
            transform: translateY(-50%) rotate(45deg);
        }
    }

    /* justify-content: space-between; */
}

.support-chart {
    width: 1634px;
    height: 440px;
    border-radius: 10px;
    opacity: 1;
    overflow-x: auto;
    overflow-y: hidden;
    background: #ffffff;

    .tlite {
        padding-left: 25px;
        padding-top: 16px;
        font-size: 20px;
        font-weight: 700;
    }
}
</style>
