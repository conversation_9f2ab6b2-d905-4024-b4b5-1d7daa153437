import request from '@/utils/request'

export function developmentlist(query) {
    return request({
        url: '/system/hzhb/examine/development/list',
        method: 'get',
        params: query
    })
}

export function userlist(query) {
    return request({
        url: '/system/hzhb/examine/user/list',
        method: 'get',
        params: query
    })
}


export function scenelist(query) {
    return request({
        url: '/system/hzhb/examine/scene/list',
        method: 'get',
        params: query
    })
}

export function industrylist(query) {
    return request({
        url: '/system/hzhb/examine/industry/list',
        method: 'get',
        params: query
    })
}



export function reject(data) {
    return request({
        url: '/system/hzhb/examine/reject',
        method: 'post',
        data: data
    })
}

export function showhide(data) {
    return request({
        url: '/system/hzhb/examine/showhide',
        method: 'post',
        data: data
    })
}

export function agree(data) {
    return request({
        url: '/system/hzhb/examine/agree',
        method: 'post',
        data: data
    })
}


export function servicelist(params) {
    return request({
        url: '/system/hzhb/examine/service/list',
        method: 'get',
        params: params
    })
}
