import request from '@/utils/request'

// 查询ai基础数据列表
export function listCase(query) {
  return request({
    url: '/system/aicase/list',
    method: 'get',
    params: query
  })
}
export function listTypeData(query) {
  return request({
    url: '/system/aicase/typedata',
    method: 'get',
    params: query
  })
}


// 查询ai基础数据详细
export function getCase(id) {
  return request({
    url: '/system/aicase/' + id,
    method: 'get'
  })
}

// 新增ai基础数据
export function addCase(data) {
  return request({
    url: '/system/aicase',
    method: 'post',
    data: data
  })
}

// 修改ai基础数据
export function updateCase(data) {
  return request({
    url: '/system/aicase',
    method: 'put',
    data: data
  })
}

// 删除ai基础数据
export function delCase(id) {
  return request({
    url: '/system/aicase/' + id,
    method: 'delete'
  })
}
