<template>
    <div class="app-container" :style="open ? 'height:800px' : ''">
        <div class="app-conter-box">
            <div class="region-dialog-bg" v-if="open">
                <div style="width: 1200px;border-radius: 10px;background: #ffffff;height: 500px;     padding: 25px;">
                    <div class="region-dialog-teltel">
                        <img style="width: 24px;" src="@/assets/images/index/biaoti.png" alt="" />
                        <div style="margin-left: 20px;font-size: 20px;">{{ industryName }}</div>
                        <div class="close" @click="open = false">
                            <img style="width: 34px;" src="@/assets/images/index/close.png" alt="" />
                        </div>
                    </div>
                    <myLine :width="'1100px'" :height="'400px'" :List="itemList"></myLine>
                </div>
            </div>
            <div>
                <div class="dashboard-editor-header mt20">
                    各行业用户分省点击
                    <div class="date">
                        <el-date-picker v-model="dayDate" @change="getList()" style="width:300px" format="yyyy/MM/dd"
                            prefix-icon="" :clearable="false" ref="elDatePickControl" type="daterange"
                            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                                disabledDate: time => {
                                    return time.getTime() > Date.now()
                                }
                            }"></el-date-picker>
                    </div>
                </div>
            </div>
            <div class="region-list" v-for="(items, indexs) in this.dataList" :key="indexs">
                <div v-for="(item, index) in items" :key="index" class="region-item"
                    :style="{ border: customColor[item.industryName] || '#f5f5f5' }">
                    <div class="region-item-tlite">
                        <div class="region-item-t">{{ item.industryName }}</div>
                        <div class="region-more" @click="isMore(item)">more</div>
                    </div>

                    <div class="region-item-list" :style="{ 'border-right': index < 2 ? '1px dashed #c4c4c4' : '' }">
                        <template v-for="(row, index) in item.industryModuleClick">
                            <div class="region-item-content" :key="index" v-if="index < 5">
                                <div style="padding: 0 30px;">
                                    <div>
                                        <span>NO.{{ index + 1 }}&nbsp;</span>
                                        <span>{{ row.provinceCode.replace('省', '') }}</span>
                                    </div>
                                    <div style="color: #005CDC;">{{ row.clickNumber }}</div>
                                </div>
                                <div>
                                    <div style="width: 100%;">
                                        <el-progress :percentage="row.ratio"></el-progress>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { setScale } from '@/utils/setScale'
import { provinceclick } from '@/api/system/sum.js'
import myLine from '../../dashboard/myLine.vue'
export default {
    name: 'Config',
    data() {
        return {
            // dayDate: [],
            open: false,
            industryName: '',
            itemList: [],
            customColor: {},
            dataList: [],
            queryParams: {},
            dayDate: []
        }
    },
    components: {
        myLine
    },
    created() {
        setScale()
        window.onresize = () => {
            setScale()
        }
        this.dayDate = this.$route.query.dayDate.split(',')
        this.getList()
    },
    methods: {
        colseDialog() {
            this.open = false
        },
        //显示更多
        isMore(item) {
            this.industryName = item.industryName
            this.itemList = item.industryModuleClick.map(item => ({
                provinceName: item.provinceCode, // 将 provinceCode 换成 provinceName
                peopleNumber: item.clickNumber, // 将 clickNumber 换成 peopleNumber
                // 复制其他字段
                ...item
            }))
            console.log(this.itemList)
            if (this.itemList.length > 0) {
                this.open = true
            }
        },
        getList() {
            if (this.dayDate.length > 0) {
                this.queryParams.startDate = this.parseTime(this.dayDate[0], '{y}-{m}-{d}')
                this.queryParams.endDate = this.parseTime(this.dayDate[1], '{y}-{m}-{d}')
            }
            provinceclick(this.queryParams).then(res => {
                this.dataList = this.chunkArray(res.data, 3)
                console.log(res.data)
            })
        },
        chunkArray(array, size) {
            return array.reduce((acc, _, i) => {
                if (i % size === 0) {
                    acc.push(array.slice(i, i + size))
                }
                return acc
            }, [])
        }
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    padding: 0;
    width: 100%;
    font-family: '思源黑体';
    background: #f0f2f5;
    min-height: calc(100vh - 84px);

    .app-conter-box {
        width: 1200px;
        margin: auto;
        overflow: hidden;

        .dashboard-editor-header {
            font-size: 24px;
            font-weight: 900;
            color: #0066e4;
            display: flex;
            align-items: center;
            height: 60px;
            border-radius: 10px;
            justify-content: space-between;
            padding: 0 24px;
            box-sizing: border-box;
            background: #ffffff;

            .date {
                // flex: 1;
                display: flex;
                text-align: right;
                font-size: 18px;
                font-weight: 500;
                position: relative;
                padding-right: 10px;
                align-items: center;
                cursor: pointer;

                .onlowdate {
                    width: 72px;
                    height: 36px;
                    border-radius: 10px;
                    opacity: 1;
                    border: 1px solid #3384e9;
                    text-align: center;
                    line-height: 36px;
                    background: #ffffff;
                    margin-right: 20px;
                }
            }
        }

        .region-dialog-bg {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background-color: rgb(0, 0, 0, 0.3);
            z-index: 999;
            display: flex;
            align-items: center;
            justify-content: center;

            .region-dialog {
                float: left;
                margin-top: 20px;
                margin-left: calc(50% - 628px);
                width: 1256px;
                height: 900px;
                background: url(../../../assets/images/click/user-region-dialog.png) no-repeat center center;
                background-size: 100% 100%;

                .dialog-title {
                    float: left;
                    width: 120px;
                    margin-top: 20px;
                    margin-left: 16px;
                    height: 33px;
                    font-size: 33px;
                    font-family: MiSans-Heavy, MiSans;
                    font-weight: 800;
                    color: #1366bf;
                    line-height: 33px;
                }

                .dialog-colse {
                    float: right;
                    cursor: pointer;
                    height: 25px;
                    width: 25px;
                    margin-right: 15px;
                    margin-top: 15px;
                    background: url(../../../assets/images/click/user-region-colse.png) no-repeat center center;
                    background-size: 100% 100%;
                }

                .dialog-list {
                    float: left;
                    width: 100%;
                    height: 800px;
                    margin-top: 50px;
                    box-sizing: border-box;
                    padding-left: 20px;

                    .dialog-item-content {
                        float: left;
                        width: 400px;
                        height: 50px;
                        margin: 0px auto 20px auto;
                        /* height: 300px; */
                        /* background: #000; */
                    }
                }

                >div {
                    >div {
                        >div {
                            width: 100%;
                            display: flex;
                            justify-content: space-between;
                            font-size: 20px;
                            color: #1366bf;
                        }
                    }
                }
            }
        }
    }

    .region-list {
        width: 100%;
        // height: 460px;
        border-radius: 10px;
        opacity: 1;
        background: #ffffff;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 30px;

        .region-item {
            width: 33%;
            height: 100%;

            box-sizing: border-box;

            // margin: 40px 18px;
            .region-item-tlite {
                display: flex;
                justify-content: space-between;
                align-items: center;
                // height: 30px;
                margin: 32px 25px;
                padding-bottom: 15px;
                border-bottom: 1px solid #005cdc;
            }

            .region-more {
                cursor: pointer;
                height: 30px;
                line-height: 29px;
                width: 60px;
                font-size: 12px;
                border-radius: 5px;
                border: 1px solid #1366bf;
                text-align: center;
                color: #1366bf;
            }

            .region-item-t {
                // padding: 32px;
                font-size: 20px;
                color: #1366bf;
                font-weight: bold;
                text-align: left;
                color: #222222;
            }

            .region-item-list {
                margin-top: 20px;

                .region-item-content {
                    width: 100%;
                    margin: 0px auto 20px auto;
                    padding: 0 25px;
                    /* height: 300px; */
                    /* background: #000; */
                }
            }

            // .region-item-list:nth-child(1) {
            //     border-right: 1px solid #c4c4c4 !important;
            // }
            >div {
                >div {
                    >div {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        font-size: 20px;
                        color: #222;
                    }
                }
            }
        }
    }

    /* justify-content: space-between; */
}

.region-item:nth-child(4n + 1),
.region-item:first-child {
    margin-left: 0 !important;
}

.region-item:nth-child(4n),
.region-item:last-child {
    margin-right: 0 !important;
}

::v-deep .el-progress__text {
    display: none;
}

::v-deep .el-progress-bar__inner {
    background: linear-gradient(90deg, #68e5fa 0%, #013fff 100%) !important;
}

::v-deep .el-progress-bar__outer {
    background: #eaeff8;

    /* opacity: 0.2; */
}

/*修改滚动条样式*/
// .region-item-list::-webkit-scrollbar {
//     width: 6px;
//     height: 10px;
// }
// .region-item-list::-webkit-scrollbar-track {
//     background: rgb(239, 239, 239);
//     border-radius: 2px;
// }
// .region-item-list::-webkit-scrollbar-thumb {
//     background: #31d6a9;
//     border-radius: 10px;
// }
// .region-item-list::-webkit-scrollbar-thumb:hover {
//     background: #42a5a7;
// }
// .region-item-list::-webkit-scrollbar-corner {
//     background: #179a16;
// }

// ::v-deep .el-range-editor--medium .el-range__icon,
// .el-range-editor--medium .el-range__close-icon {
//     display: none;
// }
// ::v-deep .el-date-editor .el-range__icon {
//     line-height: 30px;
// }
// ::v-deep .el-date-editor .el-range-separator {
//     padding: 0;
// }

// ::v-deep .el-date-editor .el-range__close-icon {
//     display: none;
// }
// ::v-deep .el-date-editor .el-range-input {
//     width: 48%;
//     font-size: 20px;
//     height: 38px;
//     line-height: 38px;
//     font-weight: 800;
//     color: #1366bf;
//     font-family: MiSans-Heavy, MiSans;
// }

// ::v-deep .el-range-separator {
//     font-size: 20px;
//     height: 38px;
//     line-height: 38px;
//     font-weight: 800;
//     color: #1366bf;
//     font-family: MiSans-Heavy, MiSans;
// }

// ::v-deep .el-date-editor > input:-moz-placeholder {
//     color: #1366bf;
// }

// ::v-deep .el-date-editor > input:-ms-input-placeholder {
//     color: #1366bf;
// }

// ::v-deep .el-date-editor > input::-webkit-input-placeholder {
//     color: #1366bf;
// }

/* ::v-deep .el-progress-bar {
        width: 124%;
    } */

// .region-item-list:nth-child(1),
// .region-item-list:nth-child(2) {
//     border-right: 1px solid #c4c4c4 !important;
// }

.mt20 {
    margin-bottom: 20px;
}

.region-dialog-teltel {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .close {
        flex: 1;
        text-align: right;
        cursor: pointer;
    }
}
</style>
