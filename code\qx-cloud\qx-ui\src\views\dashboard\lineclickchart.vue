<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    types: {
      type: Number,
      default: 1,
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "300px",
    },
    linxAxisData: {
      type: Array,
      default: [],
    },
    linchartData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  watch: {
    linxAxisData: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
    },
    linchartData: {
      handler() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
      deep: true,
    },
  },
  methods: {
    //添加千分位
    formatCurrency(value) {
      if (value == 0) return 0;
      if (value == ".") return "";
      if (value == "N/A") return "N/A";
      if (!value) return "";
      let val = Number(value); // 提前保留两位小数
      let intPart = parseInt(val); // 获取整数部分
      let intPartFormat = intPart
        .toString()
        .replace(/(\d)(?=(?:\d{3})+$)/g, "$1,"); // 将整数部分逢三一断
      let floatPart = "0"; // 预定义小数部分
      val = val.toString(); // 将number类型转为字符串类型，方便操作
      let value2Array = val.split(".");

      if (value2Array.length === 2) {
        // =2表示数据有小数位
        floatPart = value2Array[1].toString(); // 拿到小数部分
        return intPartFormat + "." + floatPart;
      } else {
        return intPartFormat;
      }
    },

    initChart() {
      // background: #FFAA57 #4F7AFC #93BEFF;
      let bgColor = "#fff";
      let color = this.color;
      this.chart = echarts.init(this.$el, "macarons");
      let echartData = this.echartData;
      let formatNumber = function (num) {
        let reg = /(?=(\B)(\d{3})+$)/g;
        return num.toString().replace(reg, ",");
      };
      // 将 this.linchartData 中的 null 替换为 0
      // this.linchartData = this.linchartData.map(item => (item === null ? 0 : item))
      // console.log(this.linchartData, '1077777777777777777777777')
      this.chart.setOption({
        xAxis: {
          data: this.linxAxisData,
          axisLabel: {
            interval: 0,
            rotate: 0,
            color: "#666666", // 设置字体颜色
            formatter: (value) => {
              if (this.types == 1) {
                return value
                  .split("")
                  .map((char, index) => {
                    if (char === "(" || char === ")") {
                      return char;
                    }
                    return char + "\n";
                  })
                  .join("")
                  .replace(/\(\n/g, "(")
                  .replace(/\n\)/g, ")");
              } else {
                let slicedValue = value.slice(0, 7); // 截取前7个字符
                let result = "";
                for (let i = 0; i < slicedValue.length; i += 4) {
                  result += slicedValue.slice(i, i + 4) + "\n";
                }
                console.log(result);
                return result.trim(); // 去掉末尾的换行符
              }
            },
          },
          axisTick: {
            show: false, // 移除 y 轴的刻度线
          },
          axisLine: {
            lineStyle: {
              color: "#666", // 设置 x 坐标轴线的颜色为 #666
            },
          },
          boundaryGap: true, // 设置边界间隙
        },

        yAxis: {
          minInterval: 1,
          splitLine: {
            lineStyle: {
              type: "dashed", // 设置分割线为虚线
            },
          },
          axisLabel: {
            color: "#D5D7D9", // 设置字体颜色
            formatter: (value) => {
              if (this.types == 1) {
                if (value >= 1000) {
                  return value / 1000 + "k";
                }
                return value;
              } else {
                return value;
              }
            },
          },
          axisTick: {
            show: false, // 移除 y 轴的刻度线
          },
          axisLine: {
            show: false, // 移除 y 轴的坐标轴线
          },
        },
        grid: {
          bottom: "35%",
          left: this.types == 1 ? "2%" : "10%", // 调整左边距
          right: this.types == 1 ? "2%" : "10%", // 调整右边距
        },
        series: [
          {
            type: "bar",
            data: this.linchartData.map((item) => (item === null ? 0 : item)),
            barWidth: this.types == 1 ? 20 : 40, // 设置柱子的宽度为10px
            itemStyle: {
              color:
                this.types == 2
                  ? "#3470FF"
                  : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: "#96d2fb" },
                      { offset: 1, color: "#004fff" },
                    ]),
              barBorderRadius: [3, 3, 0, 0], // 设置圆角
            },
            label: {
              normal: {
                show: true, // 确保标签显示
                position: "top", // 定位在柱子上方
                formatter: (val) => {
                  return `${this.formatCurrency(val.value)}`;
                },
                textStyle: {
                  fontSize: 10,
                  color: "#3470FF",
                },
              },
            },
            showBackground: this.types == 2,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
          },
        ],
      });
    },
  },
};
</script>
