import request from '@/utils/request'

// 查询行业列表
export function list(query) {
  return request({
    url: '/system/commercial/list',
    method: 'get',
    params: query
  })
}

// 修改行业
export function upd(data) {
  return request({
    url: '/system/commercial/upd',
    method: 'post',
    data: data
  })
}

// 删除行业
export function del(data) {
  return request({
    url: '/system/commercial/del/' + data,
    method: 'post',
    data: data
  })
}

// 新增行业
export function add(data) {
  return request({
    url: '/system/commercial/add',
    method: 'post',
    data: data
  })
}
