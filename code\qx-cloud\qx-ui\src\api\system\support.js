import request from '@/utils/request'

// 查询前端用户点赞列表
export function listSupport(query) {
  return request({
    url: '/system/support/listvo',
    method: 'get',
    params: query
  })
}
// 查询前端用户点赞列表
export function listAll(query) {
  return request({
    url: '/system/support/listall',
    method: 'get',
    params: query
  })
}



// 查询前端用户点赞详细
export function getSupport(id) {
  return request({
    url: '/system/support/' + id,
    method: 'get'
  })
}

// 新增前端用户点赞
export function addSupport(data) {
  return request({
    url: '/system/support',
    method: 'post',
    data: data
  })
}

// 修改前端用户点赞
export function updateSupport(data) {
  return request({
    url: '/system/support',
    method: 'put',
    data: data
  })
}

// 删除前端用户点赞
export function delSupport(id) {
  return request({
    url: '/system/support/' + id,
    method: 'delete'
  })
}

// 导出前端用户点赞
export function exportSupport(query) {
  return request({
    url: '/system/support/export',
    method: 'get',
    params: query
  })
}