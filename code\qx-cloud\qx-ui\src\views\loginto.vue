<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">iShow管理运营平台</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="passwordTo">
        <el-input v-model="loginForm.passwordTo" type="password" auto-complete="off" placeholder="密码" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input v-model="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <!-- <div class="forget">忘记密码</div> -->
      <update-pwd />
      <el-form-item style="width:100%;">
        <el-button :loading="loading" size="medium" type="primary" style="width:100%;" @click.native.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2021-2026 iShow管理运营平台All Rights Reserved.</span>
    </div>

    <el-dialog title="4A管控验证码" :visible.sync="openCode" width="450px" append-to-body :close-on-click-modal="false">
      <el-form ref="codeform" :model="codeform" :rules="coderules" label-width="80px" @submit.native.prevent>
        <el-form-item label="验证码" prop="loginCode">
          <el-input v-model="codeform.loginCode" maxlength="10" show-word-limit placeholder="请输入验证码" @keyup.enter.native="submitCode($event)" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <!--        <el-button type="primary" @click="submitCode">确 定</el-button>-->
        <el-button type="primary" :loading="loading" @click="submitCode">
          <span v-if="!loading">确 定</span>
          <span v-else>提交中...</span>
        </el-button>
        <el-button @click="cancelCode">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import IframeMessage from "../utils/iframeMessageBus";
import JSEncrypt from "jsencrypt";
import { getCodeImg, tokenlogin } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import UpdatePwd from "../components/UpdatePwd/updatepwd.vue";
import md5 from "md5";
export default {
  components: {
    UpdatePwd,
  },
  name: "Login",
  data() {
    return {
      openCode: false,
      codeform: { loginCode: "" },
      coderules: {
        loginCode: [
          { required: true, message: "验证码不能为空", trigger: "blur" },
        ],
      },

      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        passwordTo: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        passwordTo: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      loading: false,
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        // this.redirect = route.query && route.query.redirect;
        this.redirect = "index";
      },
      immediate: true,
    },
  },
  mounted() {
    try {
      import(/* webpackPrefetch: true */ './index.vue')
      .then(() => console.log('首页组件预加载成功'))
      .catch(err => console.warn('预加载失败:', err));
      
      let datatoken;
      IframeMessage.getToken().then((res) => {
        if (res.token) {
          const autoloading = this.$loading({
            lock: true,
            text: "自动登录中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          console.log(res);
          datatoken = {
            token: res.token,
            isAdmin: 1,
          };
          this.$store
            .dispatch("LoginToken", datatoken)
            .then((data) => {
              autoloading.close();
              try {
                console.log("window.nlapmPlugin", window.nlapmPlugin);
                if (window.nlapmPlugin !== undefined) {
                  window.nlapmPlugin.login({
                    loginName: md5(token),
                  }); // loginName即为你的登录名（用户唯一标识）
                }
              } catch (e) {
                console.log("nlapmPlugin login erro!");
              }
              console.log("this.$router.push");

              if (this.$route.path !== data && this.$route.path !== "/index") {
                this.$router.push("/index").catch((err) => {
                  console.log("跳转失败:", err);
                });
              } else {
                console.log("已经在目标页面，跳转被跳过");
              }
            })
            .catch(() => {
              autoloading.close();
              this.getCode();
            });
        }

        // console.log('已收到', res)
      });

      let token = this.$route.query.token;
      if (token != null && token != "") {
        const autoloading = this.$loading({
          lock: true,
          text: "自动登录中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
        });
        datatoken = {
          token: token,
        };
        this.$store
          .dispatch("LoginToken", datatoken)
          .then((data) => {
            console.log(data);
            autoloading.close();
            try {
              console.log("window.nlapmPlugin", window.nlapmPlugin);
              if (window.nlapmPlugin !== undefined) {
                window.nlapmPlugin.login({
                  loginName: md5(token),
                }); // loginName即为你的登录名（用户唯一标识）
              }
            } catch (e) {
              console.log("nlapmPlugin login erro!");
            }
            console.log("this.$router.push");

            if (this.$route.path !== data && this.$route.path !== "/index") {
              this.$router.push("/index").catch((err) => {
                console.log("跳转失败:", err);
              });
            } else {
              console.log("已经在目标页面，跳转被跳过");
            }
          })
          .catch(() => {
            autoloading.close();
            this.getCode();
          });
      }
    } catch {}
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    // submitCode() {
    //   this.$refs.codeform.validate(valid => {
    //     if (valid) {
    //       //this.loginForm.code = this.codeform.loginCode;
    //       this.$store.dispatch("VerifyCode", { username: this.loginForm.username, code: this.codeform.loginCode }).then((data) => {
    //         console.log(data);
    //         // this.$router.push({ path: data || "/index" }).catch(() => { });
    //       }).catch(() => {
    //         this.loading = false;
    //       });
    //     }
    //   });
    // },
    // submitCode(event) {
    //   // 阻止回车时页面的默认行为
    //   if (event) {
    //     event.preventDefault();
    //   }
    //
    //   this.$refs.codeform.validate(valid => {
    //     if (valid) {
    //       // 登录验证码验证逻辑
    //       this.$store.dispatch("VerifyCode", { username: this.loginForm.username, code: this.codeform.loginCode }).then((data) => {
    //         console.log(data);
    //         this.$router.push({ path: data || "/index" }).catch(() => { });
    //       }).catch(() => {
    //         this.loading = false;
    //       });
    //     }
    //   });
    // },
    submitCode(event) {
      if (event) {
        event.preventDefault(); // 阻止默认行为
      }

      this.$refs.codeform.validate((valid) => {
        if (valid) {
          // 设置 loading 为 true，显示加载状态
          this.loading = true;

          this.$store
            .dispatch("VerifyCode", {
              username: this.loginForm.username,
              code: this.codeform.loginCode,
            })
            .then((data) => {
              // 请求成功后关闭 loading
              this.loading = false;
              console.log(data);
              try {
                console.log("window.nlapmPlugin", window.nlapmPlugin);
                if (window.nlapmPlugin !== undefined) {
                  window.nlapmPlugin.login({
                    loginName: this.loginForm.username,
                  }); // loginName即为你的登录名（用户唯一标识）
                }
              } catch (e) {
                console.log("nlapmPlugin login erro!");
              }

              this.$router.push({ path: data || "/index" }).catch(() => {});
            })
            .catch(() => {
              // 请求失败后关闭 loading
              this.loading = false;
            });
        }
      });
    },
    cancelCode() {
      this.openCode = false;
      this.codeform.loginCode = "";
    },

    getCode() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
        this.loginForm.publicKey = res.publicKey;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      };
    },
    handleLogin() {
      let that = this;
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), {
              expires: 30,
            });
            Cookies.set("rememberMe", this.loginForm.rememberMe, {
              expires: 30,
            });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove("rememberMe");
          }
          //this.loginForm.password = md5(this.loginForm.passwordTo);

          const encryptor = new JSEncrypt();
          encryptor.setPublicKey(this.loginForm.publicKey); // 设置公钥
          that.loginForm.password = encryptor.encrypt(
            md5(that.loginForm.passwordTo)
          ); // 对需要加密的数据进行加密

          this.$store
            .dispatch("Login", this.loginForm)
            .then((data) => {
              if (data == 0) {
                this.openCode = true;
                this.loading = false;
              } else if (data == 1) {
                this.msgError("登录4A失败，请联系管理员");
                this.loading = false;
              } else {
                console.log(data);
                try {
                  console.log("window.nlapmPlugin", window.nlapmPlugin);
                  if (window.nlapmPlugin !== undefined) {
                    window.nlapmPlugin.login({
                      loginName: this.loginForm.username,
                    }); // loginName即为你的登录名（用户唯一标识）
                  }
                } catch (e) {
                  console.log("nlapmPlugin login erro!");
                }

                this.$router.push({ path: data || "/index" }).catch(() => {});
              }
            })
            .catch(() => {
              this.loading = false;
              this.getCode();
            });
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.png");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
  font-size: 24px;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
</style>
