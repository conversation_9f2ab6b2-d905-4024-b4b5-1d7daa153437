<template>
  <div class="component-upload-image">
    <el-upload :headers="headers" :action="uploadImgUrl" :accept="accept" list-type="picture-card" :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :on-error="handleUploadError" name="file"
      :show-file-list="false" style="display: inline-block; vertical-align: top">
      <el-image v-if="!value" :src="value">
        <div slot="error" class="image-slot">
          <i class="el-icon-plus" />
        </div>
      </el-image>
      <div v-else class="image">
        <video v-if="fileName=='.mp4'" width="150" height="150" id="videoId" :src="previewUrl" controls>
          <!-- <source :src="previewUrl" :key="previewUrl"  type="video/mp4"> -->
        </video>
        <el-image v-else :src="value" v-bind:style="uploadStyle" fit="fill" />

        <div class="mask">
          <div class="actions">
            <span title="预览" @click.stop="previewImage(value)">
              <i class="el-icon-zoom-in" />
            </span>
            <span title="移除" @click.stop="removeImage">
              <i class="el-icon-delete" />
            </span>
          </div>
        </div>
      </div>
    </el-upload>
    <div v-if="tipContent!=''" style="width:150px">{{tipContent}}</div>
    <el-dialog :visible.sync="dialogVisible" title="预览" width="800" append-to-body>
      <video v-if="fileName=='.mp4'" width="100%" height="300" :src="previewUrl" controls>
        <!-- <source :src="previewUrl" :key="previewUrl" type="video/mp4"> -->
      </video>
      <img v-else :src="value" style="display: block; max-width: 100%; margin: 0 auto;">
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
        // Authorization:
        //   "Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjo2NzgzNzY0MzU2ODkwNjI0LCJ1c2VyX2tleSI6IjE3MWNlZTQwLWRjYTUtNDI1Yi05MGQ1LTg5ODg0ODc1MmYwYyIsInVzZXJuYW1lIjoiUVhTWl9jaGVueGluIn0.2gSd90F4Fv94Gi861pUgS8uKREhH3nf-xfUWzOyo52VUUZyjKbcKeSOxrbvEIF18-RFSlnFWaG-tAyVtt5L6hg",
      },
      previewUrl: "", // 预览URL
      fileName: "",
      dialogVisible: false,
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/system/common/upload", // 上传的图片服务器地址
      //uploadImgUrl: "http://*************:8087/ishowa/prod-api/system/common/upload", // 上传的图片服务器地址
      
      

      //uploadImgUrl: "http://***********:9098/common/uploadimg",
    };
  },
  props: {
    value: {
      type: String,
      default: "",
    },
    uploadStyle: {
      type: String,
      default: "width:150px;height:150px;",
    },
    tipContent: {
      type: String,
      default: "",
    },
    accept: {
      type: String,
      default: ".jpg,.jpeg,.png",
    },
    fileSize: {
      type: Number,
      default: 200,
    },
  },
  watch: {
    value: {
      // 对对象进行深度监听
      handler(newVal, oldVal) {
        if (
          (oldVal === "" || oldVal == null) &&
          newVal != null &&
          newVal != ""
        ) {
          this.fileName = newVal.substring(newVal.lastIndexOf("."));
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {},
  methods: {
    // 显示预览弹窗，并更新预览URL
    previewImage(url) {
      // 更新预览URL
      this.previewUrl = url;
      console.log(url);
      this.dialogVisible = true; // 显示预览弹窗
    },
    removeImage() {
      this.$emit("input", "");
    },
    handleUploadSuccess(res) {
      this.previewUrl = res.dat;
      this.$emit("input", res.data);
      console.log("input", res.data);
      this.loading.close();
    },
    handleBeforeUpload(file) {
      let that = this;
      that.fileName = file.name.substring(file.name.lastIndexOf("."));
      if (that.accept.lastIndexOf(that.fileName) < 0) {
        this.$message({
          message: "上传文件只能上传" + that.accept + "格式!",
          type: "error",
          duration: 3000,
        });
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < that.fileSize;
      if (!isLt2M) {
        this.$message({
          message: "上传文件大小不能超过 " + that.fileSize + "M",
          type: "error",
          duration: 3000,
        });
        return false;
      }

      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        background: "rgba(0, 0, 0, 0.7)",
      });
    },
    handleUploadError() {
      this.$message({
        type: "error",
        message: "上传失败",
      });
      this.loading.close();
    },
  },
};
</script>

<style scoped lang="scss">
.image {
  position: relative;
  .mask {
    opacity: 0;
    position: absolute;
    top: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    transition: all 0.3s;
  }
  &:hover .mask {
    opacity: 1;
  }
}
</style>
