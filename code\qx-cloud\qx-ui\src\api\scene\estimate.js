import request from '@/utils/request'

// 查询成本预估列表
export function listEstimate(query) {
  return request({
    url: '/system/scene/estimate/list',
    method: 'get',
    params: query
  })
}

// 查询成本预估详细
export function getEstimate(ID) {
  return request({
    url: '/system/scene/estimate/' + ID,
    method: 'get'
  })
}

// 新增成本预估
export function addEstimate(data) {
  return request({
    url: '/system/scene/estimate',
    method: 'post',
    data: data
  })
}

// 修改成本预估
export function updateEstimate(data) {
  return request({
    url: '/system/scene/estimate',
    method: 'put',
    data: data
  })
}

// 删除成本预估
export function delEstimate(ID) {
  return request({
    url: '/system/scene/estimate/' + ID,
    method: 'delete'
  })
}
