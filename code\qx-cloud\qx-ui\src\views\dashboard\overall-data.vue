<template>
  <div class="app-container">
    <div style="width: 1px;height: 1px;background: #000;"></div>
    <div class="my_flex">
      <div class="left-flex">
        <div class="">
          <div class="visits-num">
            <img src="@/assets/images/index/userNum.png" alt="" style="width: 34px;" />
            <span style="flex: 1;font-size: 24px;padding-left: 14px;color: #fff;">
              用户数量
              <span style="font-size: 12px;">该数据不含时间统计</span>
            </span>
            <img src="@/assets/images/index/snadian.png" alt="" style="width: 8px;" />
          </div>
          <div class="num-text">{{ userSumNum }}</div>
        </div>

        <div class="">
          <div class="visits-num">
            <img src="@/assets/images/index/sum.png" alt="" style="width: 34px;" />
            <span style="flex: 1;font-size: 24px;padding-left: 14px;color: #fff;">访问总数</span>
            <img src="@/assets/images/index/snadian.png" alt="" style="width: 8px;" />
          </div>
          <div class="num-text">{{ sumCount }}</div>
        </div>

      </div>
      <div class="right-flex">
        <div class="modular">
          <div class="tlite">各行业点击</div>
          <div :style="'height: 340px;width:'+(linxAxisData*28)+'px;'">
            <lineclickchart v-if="isshow" :linxAxisData="linxAxisData" :linchartData="linchartData" :height="'340px'" :width="'2500px'" class="lineclickchart" :types="1"></lineclickchart>
          </div>
        </div>
      </div>
    </div>

    <div class="buttom-box">
      <div class="modular1">
        <div class="tlite">各模块点击</div>
        <lineclickchart v-if="isshow" :linxAxisData="linxAxisData1" :linchartData="linchartData1" :height="'400px'" :width="'766px'" :types="2" class="lineclickchart" style="margin-top: 50px;">
        </lineclickchart>
      </div>

      <div class="polarChart">
        <div class="tlite">渠道使用人数</div>

        <div style="position: absolute;top: 45px;right: 10px;font-size: 12px;">
          <div>首次注册</div>
          <div v-if="outerData.length > 0">
            <div v-for="(item, index) in outerData" :key="index">{{ item.name + ':' + item.value + '人' }}
            </div>
          </div>
        </div>
        <polarChart v-if="isshow" :loginCountList="channelList.loginCountList" :useCountList="channelList.useCountList" :height="'350px'" :width="'276px'" :types="2" style="margin-top: 50px;"></polarChart>
      </div>

      <div class="polarChart">
        <div class="tlite">用户访问频次</div>
        <PieChart v-if="isshow" class="Piechart" id="myChart" :height="'350px'" :width="'276px'" :echartData="echartData" :color="color"></PieChart>
      </div>
      <div class="polarChart">
        <div class="tlite">用户访问时长</div>
        <PieChart v-if="isshow" class="Piechart" :height="'350px'" :width="'276px'" :echartData="echartData1" :color="ecolor"></PieChart>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { setScale } from "@/utils/setScale";
import PieChart from "./PieChart";
import lineclickchart from "./lineclickchart";
import polarChart from "./polarChart";
import { clicksumpopulation, channel } from "@/api/system/sum.js";
export default {
  components: {
    PieChart,
    lineclickchart,
    polarChart,
  },
  name: "Config",
  props: {
    Date: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      titleOptions: [
        { id: 1, name: "行业访客" },
        { id: 2, name: "场景访客" },
        { id: 4, name: "网络方案访客" },
        { id: 5, name: "商业价值访客" },
        { id: 6, name: "落地案例访客" },
        { id: 7, name: "VR看现场访客" },
        { id: 8, name: "集成报价访客" },
        { id: 9, name: "行业生态链访客" },
      ],
      color: ["#93BEFF", "#4F7AFC", "#FFAA57"],
      isfirst: false,
      echartData: [],
      echartDataJson: {
        towNum: "0-2次",
        fiveNum: "3-5次",
        sixNum: "6次以上",
      },
      ecolor: ["#FFAA57", "#4F7AFC", "#93BEFF", "#283E81", "#4ECC93"],
      echartData1: [],
      echartData1Json: {
        halfMinuteNum: "10-30s",
        maxMinuteNum: "181s以上",
        oneMinuteNum: "31-60s",
        tenSecondNum: "10s以内",
        threeMinuteNum: "61-180s",
      },
      linxAxisData: [],
      linxAxisData1: [],
      linchartData: [],
      linchartData1: [],
      sumCount: "", //访问总数
      userSumNum: "", // 用户数
      isshow: false,
      pamas: {},
      channelList: [],
      outerData: [],
    };
  },
  watch: {
    // 监听 Date 数组的变化
    Date: {
      handler() {
        this.pamas = {
          startDate: this.parseTime(this.Date[0], "{y}-{m}-{d}"),
          endDate: this.parseTime(this.Date[1], "{y}-{m}-{d}"),
        };

        this.getList();
      },
      immediate: true, // 初始化时立即执行一次
      deep: true, // 深度监听
    },
  },
  mounted() {
    // this.pamas = {
    //     startDate: this.parseTime(this.Date[0], '{y}-{m}-{d}'),
    //     endDate: this.parseTime(this.Date[1], '{y}-{m}-{d}')
    // }
    // this.getList()
    console.log(document.getElementById("#hamburger-container"));

    setScale();
    window.onresize = () => {
      setScale();
    };
  },
  methods: {
    getList() {
      clicksumpopulation(this.pamas).then((res) => {
        this.linxAxisData = [];
        this.linxAxisData1 = [];
        this.linchartData = [];
        this.linchartData1 = [];
        this.echartData = [];
        this.echartData1 = [];
        this.userSumNum = res.data.userSumNum;
        // res.data.visitFrequency.towNum = this.userSumNum - (res.data.visitFrequency.fiveNum + res.data.visitFrequency.sixNum)
        // this.sumCount = res.data.visitFrequency.sumCount

        let qxClickRecordsItemCount = res.data.qxClickRecordsItemCount;
        let visitFrequency = res.data.visitFrequency;
        let industryModuleClick = res.data.industryModuleClick;
        // industryModuleClick.sort((a, b) => a.clickValue - b.clickValue)
        // industryModuleClick.forEach(item => {
        //     if ()
        // });

        //点击类型
        this.titleOptions.forEach((item) => {
          item.sum = 0;
          industryModuleClick.forEach((row) => {
            if (item.id == row.clickType) {
              item.sum += parseInt(row.clickNumber);
            }
          });
        });
        // this.titleOptions.sort((a, b) => b.sum - a.sum)
        this.linchartData1 = [];
        this.titleOptions.forEach((item) => {
          this.linxAxisData1.push(item.name);
          this.linchartData1.push(item.sum);
        });

        //行业分类
        res.data.industryCountList.forEach((item) => {
          item.sum = 0;
          industryModuleClick.forEach((row) => {
            if (
              item.industryCode == row.clickValue &&
              parseInt(row.clickType) < 10
            ) {
              item.sum += parseInt(row.clickNumber);
            }
          });
        });
        // res.data.industryCountList.sort((a, b) => b.sum - a.sum)

        this.linchartData = [];
        res.data.industryCountList.forEach((item) => {
          this.linxAxisData.push(item.industryName);
          this.linchartData.push(item.clickCount);
        });

        console.log(res.data.industryCountList);

        delete visitFrequency.sumCount;

        this.sumCount = 0;
        // this.echartData1[1].value = qxClickRecordsItemCount.halfMinuteNum
        this.echartData1 = [];
        for (var key in qxClickRecordsItemCount) {
          this.echartData1.push({
            name: this.echartData1Json[key],
            value: qxClickRecordsItemCount[key],
          });
          this.sumCount += qxClickRecordsItemCount[key];
          console.log(this.sumCount);
        }
        //  =
        this.echartData = [];
        for (var key in visitFrequency) {
          this.echartData.push({
            name: this.echartDataJson[key],
            value: visitFrequency[key],
          });
        }
        // console.log(this.echartData, '666666666666666666666666666666666666666666666666')

        channel(this.pamas).then((res) => {
          this.isshow = true;
          this.channelList = res.data;
          let channelNames = [
            ...new Set(
              [
                ...this.channelList.loginCountList,
                ...this.channelList.useCountList,
              ].map((item) => item.channelName)
            ),
          ];
          // 外圆环数据
          this.outerData = channelNames.map((channelName) => {
            let count =
              this.channelList.loginCountList.find(
                (item) => item.channelName === channelName
              )?.count || 0;
            return { value: count, name: channelName };
          });
          console.log(
            res.data,
            channelNames,
            "666666666666666666666666666666666666666666666666"
          );
        });
        //console.log(industryModuleClick)
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  padding: 0;

  .buttom-box {
    display: flex;
    justify-content: space-between;
  }
}

.my_flex {
  width: 100%;
  display: flex;
  margin: 20px 0;
  justify-content: space-between;

  .left-flex {
    /* flex: 1; */
    width: 400px;
    height: 360px;
    border-radius: 10px;
    opacity: 1;
    background: #ffffff;

    > div {
      margin: 25px auto;
      width: 340px;
      height: 140px;
      border-radius: 10px;
      opacity: 1;
      background: linear-gradient(90deg, #68befa 0%, #0544ff 100%);
      // display: flex;
      // justify-content: space-between;
    }
  }

  .right-flex {
    width: 1240px;
    /* flex: 1.1; */
    margin-left: 25px;
    height: 360px;
    border-radius: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    background: #ffffff;

    div {
      display: flex;
      justify-content: space-between;
    }
  }
}

.visits-num {
  padding: 17px 11px 13px 20px;
  display: flex;

  // width: 433px;
  // height: 310px;

  // background: url(../../assets/images/click/visits-num.png) no-repeat center center;
  // background-size: 100%;
  // text-align: center;
  // padding-right: 38px;
}

.users-num {
  width: 433px;
  height: 310px;

  background: url(../../assets/images/click/user-num.png) no-repeat center
    center;
  background-size: 100%;
  text-align: center;
}

.Overall_bg {
  width: 896px;
  height: 91px;
  background: url(../../assets/images/click/Overall_bg.png) no-repeat center
    center;
  background-size: 100%;
  text-align: center;
  margin-bottom: 40px;

  div {
    font-weight: 800;
    font-size: 45px;
    padding-left: 21px;
    display: flex;
    align-items: center;
    color: #fff;
  }
}

.utotal {
  width: 433px;
  height: 446px;
  background: url(../../assets/images/click/usertotal.png) no-repeat center
    center;
  background-size: 100%;
  text-align: center;
  margin-bottom: 40px;
}

.utime {
  width: 433px;
  height: 446px;
  background: url(../../assets/images/click/usertime.png) no-repeat center
    center;
  background-size: 100%;
  text-align: center;
  margin-bottom: 40px;
}

.num-text {
  // height: 70px;
  opacity: 1;
  color: #ffffff;
  font-size: 48px;
  font-weight: 900;
  font-family: "思源黑体";
  text-align: center;
}

.Piechart {
  // width: 400px;
  // height: 300px;
  margin: auto;
  margin-top: 40px;
}

.lineclickchart {
  width: 100%;
  height: 100%;
  margin: auto;
  margin-top: 20px;
  /* background: #000;     */
}

.industry {
  width: 900px;
  height: 480px;
  background: url(../../assets/images/click/industry.png) no-repeat center
    center;
  background-size: 100%;
  text-align: center;
  /* margin-bottom: 40px; */
}

.modular {
  text-align: center;
  position: relative;
  background: #fff;
  border-radius: 10px;
  width: 1240px;
  height: 360px;
  background: #ffffff;
}

.modular1 {
  text-align: center;
  position: relative;
  background: #fff;
  border-radius: 10px;
  width: 766px;
  height: 400px;
  background: #ffffff;
}

.modular > .tlite,
.modular1 > .tlite,
.polarChart > .tlite {
  position: absolute;
  left: 25px;
  top: 16px;
  font-size: 20px;
  font-weight: 700;
}

.polarChart {
  width: 276px;
  height: 400px;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin-left: 23px;
  position: relative;
}
</style>
