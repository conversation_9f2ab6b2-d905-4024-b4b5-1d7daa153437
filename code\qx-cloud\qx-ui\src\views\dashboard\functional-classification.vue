<template>
  <div class="app-container">
    <div>
      <!-- <div class="classftion"></div> -->
      <div class="classftion-box">
        <div class="classftion-left-box">
          <div class="classftion-left-box-tlite">平均使用时长(秒)</div>
          <div class="classftion-left-content">
            <div v-for="(item, index) in titleOptions" :key="index" class="classftion-left">
              <div>
                <img style="width: 56px;" :src="item.url" alt="" />
              </div>
              <div>
                <div class="classftion-left-name">{{ item.name }}</div>
                <div class="classftion-left-num">{{ item.timeLength }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="classftion-right-box">
          <div class="classftion-right-tltle">各行业使用详情(秒)</div>
          <div class="classftion-list">
            <div>行业</div>
            <div v-for="(item, index) in titleOptions" :key="index">{{ item.name }}</div>
          </div>

          <div class="classftion-listss">
            <div v-for="(item, index) in qxIndustryList" :key="index">
              <div>{{ item.industryName }}</div>
              <div>{{ item.timeLength1 }}</div>
              <div>{{ item.timeLength4 }}</div>
              <div>{{ item.timeLength5 }}</div>
              <div>{{ item.timeLength6 }}</div>
              <div>{{ item.timeLength7 }}</div>
              <div>{{ item.timeLength8 }}</div>
              <div>{{ item.timeLength9 }}</div>
              <div>{{ item.timeLength12 }}</div>
              <div>{{ item.timeLength13 }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <template v-for="(item, maxindex) in (qxIndustryList.length - 2 - ((qxIndustryList.length - 2) % 5)) / 5 + ((qxIndustryList.length - 2) % 5 > 0 ? 1 : 0)">
            <div :key="maxindex">
                <div class="classftion-box">
                    <div class="classftion-right-box">
                        <div class="classftion-right-big-tltle"></div>
                        <div class="classftion-list">
                            <template v-for="(item, index) in qxIndustryList">
                                <div :key="index" v-if="maxindex * 5 + 2 <= index && index < (maxindex + 1) * 5 + 2" class="classftion-list2">
                                    <div class="classftion-list-tltle">
                                        {{ item.industryName }}
                                    </div>

                                    <template v-for="(row, itemindex) in item.itemList">
                                        <div class="classftion-list-item" v-if="(item.industryCode != 'HY030' && row.name !== '产品介绍' && row.name !== '行业场景') || (item.industryCode == 'HY030' && row.name !== '网络方案' && row.name !== '商业价值')" :key="itemindex">
                                            <div style="font-size: 24px;font-weight: 800;color: #1366BF;">{{ row.name }}</div>
                                            <div style="font-size:28px;color:#30D4A9;font-weight: 800">{{ row.timeLength.toFixed(2) }}</div>
                                        </div>
                                    </template>
</div>
</template>
</div>
</div>
</div>
</div>
</template> -->
  </div>
</template>

<script>
import { sumavg } from "@/api/system/sum.js";
import { setScale } from "@/utils/setScale";
export default {
  name: "Config",
  data() {
    return {
      // topImg: require('../../assets/images/click/平均时长.png'),
      titleOptions: [
        {
          id: 1,
          name: "行业场景",
          url: require("../../assets/images/index/行业场景.png"),
        },
        {
          id: 4,
          name: "网络方案",
          url: require("../../assets/images/index/网络方案.png"),
        },
        {
          id: 5,
          name: "商业价值",
          url: require("../../assets/images/index/商业价值.png"),
        },
        {
          id: 6,
          name: "落地案例",
          url: require("../../assets/images/index/落地案例.png"),
        },
        {
          id: 7,
          name: "VR看现场",
          url: require("../../assets/images/index/vr看现场.png"),
          timeLength: 0,
        },
        {
          id: 8,
          name: "集成报价",
          url: require("../../assets/images/index/行业生态链.png"),
        },
        {
          id: 9,
          name: "行业生态链",
          url: require("../../assets/images/index/集成报价器.png"),
        },
        {
          id: 12,
          name: "产品介绍",
          url: require("../../assets/images/index/产品介绍.png"),
          timeLength: "0",
        },
        {
          id: 13,
          name: "基础能力",
          url: require("../../assets/images/index/基础能力.png"),
          timeLength: "0",
        },
      ],
      qxIndustryList: [],

      list1: [
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
      ],
      list2: [
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
        {
          tltle: "电力",
          name: "行业场景",
          num: 28,
        },
      ],
      pamas: {},
    };
  },
  props: {
    dayDate: {
      type: Array,
      default: [],
    },
  },
  watch: {
    // 监听 Date 数组的变化
    dayDate: {
      handler() {
        this.pamas = {
          startDate: this.parseTime(this.dayDate[0], "{y}-{m}-{d}"),
          endDate: this.parseTime(this.dayDate[1], "{y}-{m}-{d}"),
        };
        this.getList();
      },
      immediate: true, // 初始化时立即执行一次
      deep: true, // 深度监听
    },
  },
  created() {
    setScale();
    window.onresize = () => {
      setScale();
    };
    // this.pamas = {
    //     startDate: this.parseTime(this.Date[0], '{y}-{m}-{d}'),
    //     endDate: this.parseTime(this.Date[1], '{y}-{m}-{d}')
    // }
    // this.getList()
  },
  methods: {
    getList() {
      this.titleOptions.forEach((row) => {
        this.$set(row, "timeLength", 0);
      });
      sumavg(this.pamas).then((res) => {
        this.titleOptions.forEach((row) => {
          res.data.avgList.forEach((element) => {
            if (element.clickType == row.id)
              this.$set(row, "timeLength", element.timeLength.toFixed(2));
          });
        });
        // 假设 res.data.qxIndustryList 和 res.data.sumList 是从 API 获取的数据
        // 假设 res.data.qxIndustryList 和 res.data.sumList 是从 API 获取的数据
        // 假设 res.data.qxIndustryList 和 res.data.sumList 是从 API 获取的数据
        const qxIndustryList = res.data.qxIndustryList;
        const sumList = res.data.sumList;

        // 遍历 qxIndustryList
        qxIndustryList.forEach((industry) => {
          // 初始化 9 个字段
          this.titleOptions.forEach((option) => {
            industry[`timeLength${option.id}`] = 0;
            industry[`name${option.id}`] = option.name;
          });

          // 找到与 industry.industryCode 相同的 sumList 项
          sumList.forEach((sum) => {
            if (sum.clickValue == industry.industryCode) {
              // 找到与 sum.clickType 相同的 titleOptions 项
              const matchingTitleOption = this.titleOptions.find(
                (option) => option.id === parseInt(sum.clickType)
              );

              if (matchingTitleOption) {
                // 将 timeLength 的值添加到 qxIndustryList 中对应的字段
                industry[`timeLength${sum.clickType}`] =
                  sum.timeLength.toFixed(2);
              }
            }
          });
        });
        this.qxIndustryList = qxIndustryList;
        console.log(qxIndustryList);
        //this.dataList = res.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  padding: 0;
  /* width: 1860px; */

  .classftion-box {
    // display: flex;
    display: flex;

    .classftion-left-content {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      /* 3列 */
      grid-gap: 10px;
      /* 列间距 */
      justify-items: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      height: 326px;
    }

    .classftion-left-box {
      width: 640px;
      height: 400px;
      border-radius: 10px;
      background: #fff;
      // margin-top: 39px;
      margin-right: 36px;

      &-tlite {
        padding: 20px;
        font-size: 20px;
        font-weight: 700;
        color: #1c1c1c;
      }

      .classftion-left {
        background: #ffffff;
        box-shadow: 0 2px 4px 0 #0a515940;
        width: 168px;
        height: 80px;
        border-radius: 4px;
        opacity: 1;
        background: #ffffff;
        box-shadow: 0 2px 4px 0 #0a515940;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        align-items: center;

        .classftion-left-name {
          font-size: 14px;
          font-weight: 700;
        }

        .classftion-left-num {
          font-size: 24px;
          font-weight: 900;
          font-family: "思源黑体";
          text-align: right;
          color: #0066e4;
        }
      }
    }

    .classftion-right-box {
      width: 1000px;
      height: 400px;
      border-radius: 10px;
      background: #fff;

      .classftion-right-tltle {
        padding: 20px;
        font-size: 20px;
        font-weight: 700;
        color: #1c1c1c;
      }
    }

    .classftion-right-big-tltle {
      width: 1840px;
      height: 110px;
      background: url(../../assets/images/click/big-title.png) no-repeat center center;
      background-size: 100%;
      text-align: center;
      margin: 56px 0 36px 0;
    }

    .classftion-list {
      display: flex;
      width: 95%;
      margin: 20px;
      justify-content: space-around;
      height: 40px;
      font-size: 14px;
      color: #2269de;
      background: #eaeff8;
      align-items: center;
      box-sizing: border-box;

      >div {
        flex: 1;
        text-align: center;
      }

      // > div {
      //     width: 415px;
      //     height: 690px;
      //     background: url(../../assets/images/click/classlistbg.png) no-repeat center center;
      //     background-size: 100%;
      //     text-align: center;
      //     margin-right: 30px;

      //     .classftion-list-tltle {
      //         height: 77px;
      //         padding-left: 23px;
      //         line-height: 77px;
      //         color: #14be98;
      //         font-size: 28px;
      //         font-weight: 800;
      //         text-align: left;
      //     }

      //     .classftion-list-item {
      //         height: 87.5px;
      //         line-height: 87.5px;
      //         display: flex;
      //         align-items: center;
      //         justify-content: space-between;
      //         padding: 0 45px 0 37px;

      //         > div {
      //         }
      //     }
      // }

      // .classftion-list2 {
      //     width: 336px;
      //     height: 690px;
      //     background: url(../../assets/images/click/classlist2bg.png) no-repeat center center;
      //     background-size: 100%;
      //     text-align: center;
      //     margin-right: 35px;
      // }
    }
  }
}

.classftion-list2:last-child,
.classftion-list>div:last-child {
  margin-right: 0 !important;
}

.classftion-listss {
  max-height: 244px;
  overflow-y: auto;
}

.classftion-listss>div {
  display: flex;
  align-items: center;
  width: 95%;
  margin: 0 20px;
  height: 40px;
  font-size: 14px;
  border-bottom: 1px dashed #cccccc;
  /* 添加下边框虚线 */
  justify-content: space-around;

  >div {
    flex: 1;
    text-align: center;
  }
}

.classftion-listss::-webkit-scrollbar {
  display: none;
}
</style>
