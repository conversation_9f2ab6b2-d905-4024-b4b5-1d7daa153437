<template>
  <div>
    <el-upload style="float:right;" :headers="headers" :action="uploadImgUrl" accept=".xls, .xlsx" :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload" :on-error="handleUploadError" name="file" :show-file-list="false">
      <div class="table-btn">文件上传</div>
    </el-upload>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      numVal: this.modelValue,
      dialogVisible: false,
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/system/excel/import", // 上传的图片服务器地址
      //uploadImgUrl: "http://***********:9098/excel/import",
    };
  },
  model: {
    prop: 'modelValue',
    event: 'change' // 事件名称
  },
  watch: {
    modelValue: {
      handler(newName, oldName) {
        this.numVal = newName;
      }, deep: true,
      immediate: true,
    }
  },
  props: {
    modelValue: {
      type: Array
    },
  },
  created() {
  },
  methods: {
    removeImage() {
      this.numVal = [];
      this.$emit('change', this.numVal)
    },
    handleUploadSuccess(res) {
      this.numVal = res.data;
      if (res.code == 500) {
        this.$message({
          type: "error",
          message: res.msg,
        });
      }
      this.$emit('change', res.data)
      this.loading.close();
    },
    handleBeforeUpload(file) {
      this.loading = this.$loading({
        lock: true,
        text: "上传中",
        background: "rgba(0, 0, 0, 0.7)",
      });
    },
    handleUploadError() {
      this.$message({
        type: "error",
        message: "上传失败",
      });
      this.loading.close();
    },
  }
};
</script>

<style>
.table-btn {
  box-sizing: border-box;
  width: 8.3vw;
  height: 2.1vw;
  border-radius: 0.2vw;
  border: 1px solid #0052d9;
  font-size: 0.8vw;
  color: #0052d9;
  line-height: 2.1vw;
  float: right;
  margin-left: 1.3vw;
  cursor: pointer;
}
</style>