<template>
    <div style="width: 100%;height: 100%;">
        <div :class="className" :style="{ height: height, width: width }" />
        <!--  -->
    </div>
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
    mixins: [resize],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        types: {
            type: Number,
            default: 1
        },
        width: {
            type: String,
            default: '90%'
        },
        height: {
            type: String,
            default: '300px'
        },
        loginCountList: {
            type: Array,
            default: []
        },
        useCountList: {
            type: Array,
            default: []
        }
    },
    data() {
        return {
            chart: null,
            innerData: [],
            outerData:[]
        }
    },
    watch: {
        loginCountList: {
            handler(val) {
                this.initChart()
            }
        },
        useCountList: {
            handler(val) {
                this.initChart()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            console.log('PolarChart mounted', this.loginCountList, this.useCountList)
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        //添加千分位
        formatCurrency(value) {
            if (value == 0) return 0
            if (value == '.') return ''
            if (value == 'N/A') return 'N/A'
            if (!value) return ''
            let val = Number(value) // 提前保留两位小数
            let intPart = parseInt(val) // 获取整数部分
            let intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,') // 将整数部分逢三一断
            let floatPart = '0' // 预定义小数部分
            val = val.toString() // 将number类型转为字符串类型，方便操作
            let value2Array = val.split('.')

            if (value2Array.length === 2) {
                // =2表示数据有小数位
                floatPart = value2Array[1].toString() // 拿到小数部分
                return intPartFormat + '.' + floatPart
            } else {
                return intPartFormat
            }
        },

        initChart() {
            let bgColor = '#fff'
            this.chart = echarts.init(this.$el, 'macarons')

            // 动态生成 series 数组
            let series = []
            let channelNames = [...new Set([...this.loginCountList, ...this.useCountList].map(item => item.channelName))]
            console.log('channelNames', channelNames)
            let colors = ['#3785F5', '#8FB6FF', '#FFA24A', '#34D399', '#F472B6', '#FB923C', '#A78BFA', '#22D3EE']

            // 外圆环数据
            this.outerData = channelNames.map(channelName => {
                let count = this.loginCountList.find(item => item.channelName === channelName)?.count || 0
                return { value: count, name: channelName }
            })

            // 内饼图数据
            this.innerData = channelNames.map(channelName => {
                let count = this.useCountList.find(item => item.channelName === channelName)?.count || 0
                return { value: count, name: channelName }
            })
            console.log('outerData',  this.outerData)
            series.push({
                name: '某一时间段登录',
                type: 'pie',
                selectedMode: 'pie',
                radius: ['0%', '60%'], // 调整内饼图的半径，使其更小
                center: ['50%', '45%'],
                label: {
                    position: 'outer',
                    fontSize: 14,
                    show: false // 隐藏外圆环的标签
                },
                labelLine: {
                    show: false // 隐藏外圆环的指示线
                },
                emphasis: {
                    scale: false, // 防止悬停时放大
                    label: {
                        show: false
                    }
                },
                data: this.innerData
            })

            // series.push({
            //     name: '内饼图:某一时段登录',
            //     type: 'pie',
            //     radius: ['0%', '35%'], // 调整内饼图的半径，使其更小
            //     center: ['50%', '35%'],
            //     label: {
            //         position: 'inner',
            //         fontSize: 14,
            //         show: false // 隐藏外圆环的标签
            //     },
            //     labelLine: {
            //         show: false
            //     },
            //     emphasis: {
            //         scale: false, // 防止悬停时放大
            //         label: {
            //             show: false
            //         }
            //     },
            //     data: innerData
            // })

            this.chart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: [
                    {
                        // 第一个 legend 配置
                        show: true,
                        // data: ['外圆环:首次注册', '内饼图:某一时段登录'],
                        left: '0%',
                        bottom: '18%',
                        fontSize: 12,
                        icon: 'rect', // 修改为有效图标
                        iconStyle: {
                            color: 'blue' // 设置图标颜色
                        },
                        itemGap: 10,
                        itemWidth: 10,
                        itemHeight: 10,
                        formatter: function(name) {
                            return name // 直接显示传入的名称
                        }
                    },
                    // {
                    //     // 第二个 legend 配置
                    //     show: true,
                    //     data: channelNames,
                    //     left: '0%',
                    //     bottom: '8%',
                    //     fontSize: 12,
                    //     icon: 'circle',
                    //     itemGap: 10,
                    //     itemWidth: 10,
                    //     itemHeight: 10
                    // }
                ],
                series: series
            })
        }
    }
}
</script>
