<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="解决方案名称" prop="solutionName">
        <el-input v-model="queryParams.solutionName" placeholder="请输入解决方案名称" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item>
      <!-- <el-form-item label="解决方案ID" prop="solutionId">
        <el-input v-model="queryParams.solutionId" placeholder="请输入解决方案ID" clearable @keyup.enter.native="handleQuery"/>
      </el-form-item> -->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:sanhua:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:sanhua:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:sanhua:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:sanhua:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sanhuaList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="解决方案ID" align="center" prop="solutionId" />
      <el-table-column label="解决方案名称" align="center" prop="solutionName" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:sanhua:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:sanhua:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改ishow平台行业场景与三化解决方案ID基础对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <!-- <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="序号" prop="id">
          <el-input v-model="form.id" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="行业id" prop="industryId">
          <el-input v-model="form.industryId" placeholder="请输入行业id" />
        </el-form-item>
        <el-form-item label="场景id" prop="itemId">
          <el-input v-model="form.itemId" placeholder="请输入场景id" />
        </el-form-item>
        <el-form-item label="解决方案ID" prop="solutionId">
          <el-input v-model="form.solutionId" placeholder="请输入解决方案ID" />
        </el-form-item>
        <el-form-item label="解决方案名称" prop="solutionName">
          <el-input v-model="form.solutionName" placeholder="请输入解决方案名称" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form> -->

      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="行业案例" prop="itemIds">
          <el-cascader v-model="form.itemIds" :options="selectList" placeholder="请选择行业案例" style="width:100%"></el-cascader>
        </el-form-item>

        <el-form-item label="解决方案名称" prop="solutionName">
          <el-input v-model="form.solutionName" placeholder="请输入解决方案名称" style="width:100%"/>
        </el-form-item>

        <el-form-item label="解决方案ID" prop="solutionId">
          <el-input v-model="form.solutionId" placeholder="请输入解决方案ID" style="width:100%"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSanhua, getSanhua, delSanhua, addSanhua, updateSanhua,listTypeData } from "@/api/system/sanhua";

export default {
  name: "Sanhua",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ishow平台行业场景与三化解决方案ID基础表格数据
      sanhuaList: [],
      aiSelectTypeList: [],
      activeName: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        solutionName: null,
      },
      selectList: [],
      dataList: [null, null, null, null, null],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        itemIds: [
          { required: true, message: "行业案例不能为空", trigger: "blur" }
        ],
        solutionName: [
          { required: true, message: "解决方案名称不能为空", trigger: "blur" }
        ],
        solutionId: [
          { required: true, message: "解决方案ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("ai_select_type").then((response) => {
      this.aiSelectTypeList = response.data;
      this.activeName = response.data[0].dictValue;
    });
    this.aiTypeChange();
  },
  methods: {
    /** 查询ishow平台行业场景与三化解决方案ID基础列表 */
    getList() {
      this.loading = true;
      listSanhua(this.queryParams).then(response => {
        this.sanhuaList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        industryId: null,
        itemId: null,
        solutionId: null,
        solutionName: null,
        sceneName: null,
        industryName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加ishow平台行业场景与三化解决方案ID基础";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSanhua(id).then(response => {
        this.selectList = response.datalist;
        this.form = response.data;
        this.open = true;
        this.title = "修改ishow平台行业场景与三化解决方案ID基础";
      });
    },
    /** 提交按钮 */
    submitForm() {

      this.$refs["form"].validate(valid => {
        if (valid) {

          if (this.form.itemIds != null && this.form.itemIds.length > 0) {
            this.form.industryId = this.form.itemIds[0];
            this.form.itemId = this.form.itemIds[1];
          }
          if (this.form.id != null) {
            updateSanhua(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSanhua(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除ishow平台行业场景与三化解决方案ID基础编号为"' + ids + '"的数据项？').then(function() {
        return delSanhua(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/sanhua/export', {
        ...this.queryParams
      }, `sanhua_${new Date().getTime()}.xlsx`)
    },
    aiTypeChange() {
      let aiType = 1;
      if (this.dataList[aiType] == null) {
        listTypeData({ aiType: aiType }).then((response) => {
          this.dataList[aiType] = response.data;
          this.selectList = response.data;
        });
      } else {
        this.selectList = this.dataList[aiType];
      }
    },
  }
};
</script>
