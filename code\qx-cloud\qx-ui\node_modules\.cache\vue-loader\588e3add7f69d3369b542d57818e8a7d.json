{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\scene\\scene.vue?vue&type=template&id=c92276fa", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\src\\views\\system\\scene\\scene.vue", "mtime": 1755684666418}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1747036193477}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747036187897}, {"path": "C:\\Users\\<USER>\\Desktop\\代码\\temp_ishow\\iShow_Market_Platform_QX\\code\\qx-cloud\\qx-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747036191888}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}