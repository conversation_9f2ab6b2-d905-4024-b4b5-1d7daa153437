<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业id" prop="industryId">
        <el-input v-model="queryParams.industryId" placeholder="请输入行业id" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="场景id" prop="itemId">
        <el-input v-model="queryParams.itemId" placeholder="请输入场景id" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:aicase:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:aicase:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:aicase:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:aicase:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <!-- <el-select v-model="form.aiType" filterable placeholder="请选择类型" @change="aiTypeChange" style="width:100%">
            <el-option v-for="dict in aiSelectTypeList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue">
            </el-option>
          </el-select> -->

    <el-tabs v-model="activeName" type="card" @tab-click="getList">
      <el-tab-pane v-for="dict in aiSelectTypeList" :key="dict.dictValue" :label="dict.dictLabel" :name="dict.dictValue"></el-tab-pane>
    </el-tabs>
    <el-table v-loading="loading" :data="caseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="主键" align="center" prop="id" />
      <el-table-column label="文档分类" align="center" prop="fileType">
        <template slot-scope="scope">
          <span>{{ scope.row.fileType == 0 ? '智能问答' : '案例素材' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="标题" align="center" prop="title" />
      <el-table-column label="前端url" align="center" prop="linkUrl" />
      <el-table-column label="ai文件路径" align="center" prop="fileUrl" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:aicase:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:aicase:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改ai基础数据对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="文档分类" prop="fileType">
          <el-select v-model.number="form.fileType" collapse-tags placeholder="请选择文档分类" style="width:100%" @change="fileTypeChange">
            <el-option :key="0" label="智能问答" :value="0"></el-option>
            <el-option :key="1" label="案例素材" :value="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="类型" prop="aiType">
          <el-select v-model="form.aiType" filterable placeholder="请选择类型" @change="aiTypeChange" style="width:100%">
            <el-option v-for="dict in aiSelectTypeList" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="子项" prop="itemId">
          <el-cascader v-model="form.itemIds" :options="selectList" placeholder="请输入子项" style="width:100%"></el-cascader>
        </el-form-item>

        <el-form-item label="状态" prop="syncType">
          <el-select v-model="form.syncType" collapse-tags placeholder="请选择状态" style="width:100%">
            <el-option key="1" label="上架" value="1"></el-option>
            <el-option key="0" label="下架" value="0"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="前端路径" prop="linkUrl">
          <el-input v-model="form.linkUrl" maxlength="200" show-word-limit placeholder="请输入内容" />
        </el-form-item>

        <!-- <el-form-item label="ai文件路径" prop="fileUrl">
          <el-input v-model="form.fileUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->

        <el-form-item label="ai文件路径" prop="assemblyUrl">
          <FileUpload v-model="form.fileUrl" :accept="accept" :close-on-click-modal="false" :fileSize="fileSize" :fileType="fileType" v-if="form.fileType!=null" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCase,
  getCase,
  delCase,
  addCase,
  updateCase,
  listTypeData,
} from "@/api/system/aicase";
import FileUpload from "../../../components/FileUploadTo";
export default {
  name: "Case",
  components: {
    FileUpload,
  },
  data() {
    return {
      fileSize: 200,
      accept: ".doc,.docx",
      fileType: ["doc", "docx"],
      aiSelectTypeList: [],
      activeName: 0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ai基础数据表格数据
      caseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industryId: null,
        itemId: null,
        aiType: null,
        linkUrl: null,
        fileUrl: null,
      },
      selectList: [],
      dataList: [null, null, null, null, null],
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();

    this.getDicts("ai_select_type").then((response) => {
      this.aiSelectTypeList = response.data;
      this.activeName = response.data[0].dictValue;
    });
  },
  methods: {
    fileTypeChange() {
      if (this.form.aiType == 0) {
        this.fileType = [
          "txt",
          "pdf",
          "doc",
          "docx",
          "ppt",
          "pptx",
          "xls",
          "xlsx",
          "jpg",
          "png",
        ];
        this.accept = ".txt,.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.jpg,.png";
      } else {
        this.fileType = ["doc", "docx"];
        this.accept = ".doc,.docx";
      }
    },
    aiTypeChange() {
      let aiType = this.form.aiType;
      if (this.dataList[aiType] == null) {
        listTypeData({ aiType: aiType }).then((response) => {
          this.dataList[aiType] = response.data;
          this.selectList = response.data;
          console.log(this.selectList);
        });
      } else {
        this.selectList = this.dataList[aiType];
      }
    },

    /** 查询ai基础数据列表 */
    getList() {
      this.loading = true;
      this.queryParams.aiType = this.activeName;
      listCase(this.queryParams).then((response) => {
        this.caseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        industryId: null,
        itemId: null,
        aiType: null,
        linkUrl: null,
        fileUrl: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        fileType: null,
        syncType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.form.syncType = "1";
      this.title = "添加ai基础数据";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getCase(id).then((response) => {
        this.selectList = response.datalist;
        this.form = response.data;
        this.form.fileType= parseInt(this.form.fileType)
        this.open = true;
        this.title = "修改ai基础数据";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.itemIds != null && this.form.itemIds.length > 0) {
        this.form.industryId = this.form.itemIds[0];
        this.form.itemId = this.form.itemIds[1];
      }
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateCase(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCase(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除ai基础数据编号为"' + ids + '"的数据项？')
        .then(function () {
          return delCase(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/case/export",
        {
          ...this.queryParams,
        },
        `case_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
