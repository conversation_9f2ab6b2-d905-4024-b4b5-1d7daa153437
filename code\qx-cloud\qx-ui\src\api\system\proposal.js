import request from '@/utils/request'

// 查询优化建议列表
export function listProposal(query) {
  return request({
    url: '/system/proposal/list',
    method: 'get',
    params: query
  })
}

// 查询优化建议详细
export function getProposal(proposalId) {
  return request({
    url: '/system/proposal/' + proposalId,
    method: 'get'
  })
}

// 新增优化建议
export function addProposal(data) {
  return request({
    url: '/system/proposal',
    method: 'post',
    data: data
  })
}

// 修改优化建议
export function updateProposal(data) {
  return request({
    url: '/system/proposal',
    method: 'put',
    data: data
  })
}

// 删除优化建议
export function delProposal(proposalId) {
  return request({
    url: '/system/proposal/' + proposalId,
    method: 'delete'
  })
}

// 导出优化建议
export function exportProposal(query) {
  return request({
    url: '/system/proposal/export',
    method: 'get',
    params: query
  })
}