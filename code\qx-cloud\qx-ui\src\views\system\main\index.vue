<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业" prop="industryCode">
        <el-select
          v-model="queryParams.industryCode"
          placeholder="请选择行业"
          clearable
          @clear="handleIndustryClear">
          <el-option
            v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="案例标题" prop="mainCaseTitle">
        <el-input v-model="queryParams.mainCaseTitle" placeholder="请输入案例标题" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="地域" prop="region">
        <el-input v-model="queryParams.region" placeholder="请输入地域" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:main:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="mainList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--       <el-table-column label="id" align="center" prop="mainCaseId" />-->
      <el-table-column label="案例标题" align="center" prop="mainCaseTitle" />
      <el-table-column label="案例封面" align="center" prop="mainCaseCover">
        <template slot-scope="scope">
          <img style="width:100px;height:50px;" :src="scope.row.mainCaseCover" />
        </template>
      </el-table-column>
      <el-table-column label="是否对外" align="center" prop="isAbout">
        <template slot-scope="scope">
          <span>{{scope.row.isAbout==0?'否':'是'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="行业名称" align="center" prop="industryCode">
        <template slot-scope="scope">
          <p>{{setIndustryCode(scope.row.industryCode)}}</p>
        </template>
      </el-table-column>
      <el-table-column label="地域" align="center" prop="region" />
      <el-table-column label="显示隐藏" align="center" prop="showHide">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.showHide" :active-value="0" :inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="隐藏说明" align="center" prop="hideExplain">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.hideExplain"
            raw-content
            placement="top-start"
            v-if="scope.row.hideExplain"
          >
            <span v-if="scope.row.hideExplain && scope.row.hideExplain.length <= 30">
               {{ scope.row.hideExplain }}
          </span>
            <span v-if="scope.row.hideExplain && scope.row.hideExplain.length > 30">
               {{ scope.row.hideExplain.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.hideExplain== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-zoom-in" @click="handleUpdate(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改用户案例对话框 -->
    <el-dialog :title="title" :visible.sync="open" :inline="true" width="900px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="行业:" prop="industryCode">
              <div>{{setIndustryCode(form.industryCode)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否对外:" prop="isAbout">
              <div>{{form.isAbout==0?'否':'是'}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例标题:" prop="mainCaseTitle">
          <!-- <div>{{form.mainCaseTitle}}</div> -->
            <el-input v-model="form.mainCaseTitle" placeholder="请输入案例标题" maxlength="16" show-word-limit />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="案例封面:" prop="mainCaseCover">
              <img :src="form.mainCaseCover" style="width:150px;height:150px; margin: 0 auto;">
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="案例内容" prop="mainCaseContent">
              <video v-if="fileName=='.mp4'" width="300px" height="150px" controls>
                <source :src="form.mainCaseContent" type="video/mp4">
              </video>
              <img v-else :src="form.mainCaseContent" style="width:150px;height:150px; margin: 0 auto;">
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="案例详情编辑" prop="explainTitle">
          <img :src="form.mainScene" v-if="form.mainScene != null && form.mainScene != '' && form.mainScene.indexOf('http') > -1" alt="" style="width: 160px;height: 90px;margin-left:30px;">
        </el-form-item>

        <!-- <el-form-item label="案例详情标题" prop="explainTitle">
          <div>{{form.explainTitle}}</div>
        </el-form-item>
        <el-form-item label="项目背景" prop="projectExplain">
          <div>{{form.projectExplain}}</div>
        </el-form-item>
        <el-form-item label="应用场景及亮点" prop="mainScene">
          <div>{{form.mainScene}}</div>
        </el-form-item>
        <el-form-item label="项目整体成本" prop="projectCost">
          <div>{{form.projectCost}}</div>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="隐藏信息" :visible.sync="openHide" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="hideform" :model="hideform" :rules="hiderules" label-width="160px">
        <el-form-item label="隐藏说明" prop="hideExplain">
          <el-input v-model="hideform.hideExplain" type="textarea" maxlength="200" show-word-limit placeholder="请输入隐藏缘由" />
        </el-form-item>
        <el-form-item label="是否邮件提醒" prop="isEmailTip" label-width="160px">
          <el-switch v-model="hideform.isEmailTip" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHide">确 定</el-button>
        <el-button @click="cancelHide">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMain, getMain, delMain, addMain, updateMain, exportMain } from "@/api/system/main";
import { listIndustry } from "@/api/system/industry";
export default {
  name: "Main",
  components: {
  },
  data() {
    return {
      openHide: false,
      hideform: { hideExplain: '', isEmailTip: 0, },
      hiderules: {
        hideExplain: [
          { required: true, message: "隐藏说明不能为空", trigger: "blur" }
        ],
      },
      fileName: '',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户案例表格数据
      mainList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mainCaseTitle: null,
        mainCaseCover: null,
        mainCaseContent: null,
        userId: null,
        region: null,
        isAbout: null,
        industryCode: null,
        explainTitle: null,
        projectExplain: null,
        mainScene: null,
        projectCost: null,
        orderByColumn: 'create_time',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      //行业列表
      industryList: []
    };
  },
  created() {
    this.getList();
    listIndustry().then(response => {
      this.industryList = response.rows;
    });
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    cancelHide() {
      this.openHide = false;
      this.hideform.row.showHide = 0;
    },
    submitHide() {
      let that = this;
      this.$refs["hideform"].validate(valid => {
        if (valid) {
          updateMain({ "mainCaseId": this.hideform.row.mainCaseId, "showHide": 1, hideExplain: this.hideform.hideExplain, isEmailTip: this.hideform.isEmailTip }).then(response => {
            that.msgSuccess("隐藏成功");
            that.getList();
            that.openHide = false;
          });
        }
      })
    },
    //修改状态
    handleStatusChange(row) {
      let that = this;
      if (row.showHide === 1) {
        that.openHide = true;
        that.hideform.row = row;
        // this.$prompt('请输入隐藏缘由', '隐藏信息', {
        //   confirmButtonText: '确定',
        //   cancelButtonText: '取消',
        //   inputPattern: /\S/,
        //   inputErrorMessage: '隐藏缘由不能为空'
        // }).then(({ value }) => {
        //   console.log(value);
        //   return updateMain({ "mainCaseId": row.mainCaseId, "showHide": 1, hideExplain: value }).then(response => {
        //     that.msgSuccess("隐藏成功");
        //     that.getList();
        //   });
        // }).catch((e) => {
        //   console.log(e)
        //   row.showHide = 0;
        // });
      } else {
        updateMain({ "mainCaseId": row.mainCaseId, "showHide": 0, hideExplain: "" }).then(response => {
          that.msgSuccess("显示成功");
          that.getList();
        });
      }
    },


    setIndustryCode(code) {
      let name = "";
      this.industryList.forEach(item => {
        if (item.industryCode == code) {
          name = item.industryName
        }
      })
      return name;
    },
    /** 查询用户案例列表 */
    getList() {

      this.loading = true;
      listMain(this.queryParams).then(response => {
        this.mainList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        mainCaseId: null,
        mainCaseTitle: null,
        mainCaseCover: null,
        mainCaseContent: null,
        createTime: null,
        userId: null,
        region: null,
        isAbout: null,
        industryCode: null,
        explainTitle: null,
        projectExplain: null,
        mainScene: null,
        projectCost: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.mainCaseId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户案例";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const mainCaseId = row.mainCaseId || this.ids;

      getMain(mainCaseId).then(response => {
        this.form = response.data;
        this.fileName = this.form.mainCaseContent.substring(this.form.mainCaseContent.lastIndexOf('.'));
        this.open = true;
        this.title = "查看落地案例";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.mainCaseId != null) {
            updateMain(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMain(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const mainCaseIds = row.mainCaseId || this.ids;
      this.$confirm('是否确认删除用户案例编号为"' + mainCaseIds + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delMain(mainCaseIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的用户案例数据项';
      }else {
        msg = '是否确认导出所有的用户案例数据项';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/main/export', exportParams, `main_${new Date().getTime()}.xlsx`)
      }).catch({});
    }
  }
};
</script>
