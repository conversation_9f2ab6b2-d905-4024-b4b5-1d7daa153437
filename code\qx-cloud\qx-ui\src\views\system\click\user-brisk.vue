<template>
  <div class="app-container">
    <div class="app-conter-box">
      <div class="classftion-box">
        <div class="classftion-right-box">
          <div class="classftion-right-big-tltle">
            <div class="title-text">用户活跃数</div>
            <div class="title-sub-text">
              <div class="classftion-right-small-tltle-sub-left2">人次</div>
              <div class="classftion-right-small-tltle-sub-left3">人数</div>
            </div>
            <!-- <div class="title-change" @click="titleChange()"></div> -->
            <div class="title-select">

              <el-select v-model="queryParams.isMobile" @change="MobileChange()" collapse-tags placeholder="请选择终端" clearable>
                <el-option :key="-1" label="全部" :value="-1"></el-option>
                <el-option v-for="dict in formOptions" :key="dict.dictValue" :label="dict.dictLabel" :value="dict.dictValue" />
              </el-select>

              <!-- <el-select v-model="queryParams.isMobile" @change="MobileChange()" placeholder="请选择终端">
                <el-option :key="undefined" label="全部" :value="undefined"></el-option>
                <el-option :key="0" label="PC端" :value="0"></el-option>
                <el-option :key="1" label="移动端" :value="1"></el-option>
              </el-select> -->
            </div>
            <img src="@/assets/images/index/qiehuan.png" @click="titleChange()" alt="" style="width: 58px;" />
          </div>

          <div class="brisk-box">
            <div class="brisk-form">
              <div class="chart-title">
                <div class="title-img"></div>
                <div class="title-text">日活跃数</div>

                <div class="title-date">
                  <el-date-picker v-model="dayDate" @change="getDaySum()" style="width:280px" format="yyyy/MM/dd" prefix-icon="" :clearable="false" ref="elDatePickControl" type="daterange" range-separator="-"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                                            disabledDate: time => {
                                                return time.getTime() > Date.now()
                                            }
                                        }"></el-date-picker>
                </div>
                <!--                                <div class="title-down" @click="downClick('elDatePickControl')"></div>-->
              </div>
              <div class="brisk-chart">
                <LineBriskChart ref="LineBriskChartDays" />
              </div>
            </div>

            <div class="brisk-form" style="margin-top:30px">
              <div class="chart-title">
                <div class="title-img"></div>
                <div class="title-text">月活跃数</div>

                <div class="title-date">
                  <el-date-picker v-model="dayDateMonth" @change="getMonthSum()" style="width:280px" format="yyyy/MM" prefix-icon="" :clearable="false" ref="elDatePickMonthControl" type="monthrange" range-separator="-"
                    start-placeholder="开始日期" end-placeholder="结束日期" :picker-options="{
                                            disabledDate: time => {
                                                return time.getTime() > Date.now()
                                            }
                                        }"></el-date-picker>
                </div>
                <!--                                <div class="title-down" @click="downClick('elDatePickControl')"></div>-->
              </div>
              <div class="brisk-chart">
                <LineBriskChart ref="LineBriskChartMonth" />
              </div>
            </div>
          </div>
          <div class="classftion-content">
            <div class="classftion-left">
              <div class="title-main">
                <div class="title-img"></div>
                <div class="title-text">日活跃数</div>

                <!-- <div class="title-date">
                                    <el-date-picker
                                        v-model="dayDate"
                                        @change="getDaySum()"
                                        style="width:280px"
                                        format="yyyy/MM/dd"
                                        prefix-icon=""
                                        :clearable="false"
                                        ref="elDatePickControl"
                                        type="daterange"
                                        range-separator="-"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :picker-options="{
                                            disabledDate: time => {
                                                return time.getTime() > Date.now()
                                            }
                                        }"
                                    ></el-date-picker>
                                </div> -->
                <!-- <div class="title-down" @click="downClick('elDatePickControl')"></div> -->
              </div>

              <div class="table-content">
                <div v-for="(item, index) in dayDateList" :key="index">
                  <div>{{ item.dateStr }}</div>
                  <div class="custom-progress ">
                    <div class="progress-bar first-bar" :style="{ width: calculateWidth(item.personOrder, item.personNumber + item.personOrder) + '%', backgroundColor: '#bedbff' }">{{ item.personOrder }}</div>
                    <div class="progress-bar second-bar" :style="{ width: calculateWidth(item.personNumber, item.personNumber + item.personOrder) + '%', backgroundColor: '#3470ff' }">{{ item.personNumber }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="classftion-left">
              <div class="title-main">
                <div class="title-img"></div>
                <div class="title-text">月活跃数</div>

                <!-- <div class="title-date">
                                    <el-date-picker
                                        v-model="dayDateMonth"
                                        @change="getMonthSum()"
                                        style="width:280px"
                                        format="yyyy/MM"
                                        prefix-icon=""
                                        :clearable="false"
                                        ref="elDatePickMonthControl"
                                        type="monthrange"
                                        range-separator="-"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :picker-options="{
                                            disabledDate: time => {
                                                return time.getTime() > Date.now()
                                            }
                                        }"
                                    ></el-date-picker>
                                </div> -->
                <!-- <div class="title-down" @click="downClick('elDatePickMonthControl')"></div> -->
              </div>
              <div class="table-content">
                <div v-for="(item, index) in dayDateMonthList" :key="index">
                  <div>{{ item.dateStr }}</div>
                  <div class="custom-progress ">
                    <div class="progress-bar first-bar" :style="{ width: calculateWidth(item.personOrder, item.personNumber + item.personOrder) + '%', backgroundColor: '#bedbff' }">{{ item.personOrder }}</div>
                    <div class="progress-bar second-bar" :style="{ width: calculateWidth(item.personNumber, item.personNumber + item.personOrder) + '%', backgroundColor: '#3470ff' }">{{ item.personNumber }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { setScale } from "@/utils/setScale";
import LineBriskChart from "../../dashboard/LineBriskChart";
import { datesum, monthsum } from "@/api/system/sum.js";

export default {
  name: "userBrisk",
  components: {
    LineBriskChart,
  },
  data() {
    return {
      formOptions: [],
      isShow: true,
      echartMapYData: [33, 34, 32, 36, 31, 38, 39, 40],
      echartNumYData: [8, 9, 7, 6.5, 11, 8, 10, 11],
      echartMapXData: [
        "2020/05/05",
        "2020/09/05",
        "2020/05/05",
        "2020/04/05",
        "2020/03/05",
        "2020/02/05",
        "2020/01/05",
        "2020/06/05",
      ],

      queryParams: { isMobile: null },

      dayDate: [],
      dayDateList: [],

      dayDateMonth: [],
      dayDateMonthList: [],

      list1: [],
    };
  },
  created() {
    this.getDicts("channel_type").then((response) => {
      this.formOptions = response.data;
    });
    const end = new Date();
    const start = new Date();
    end.setTime(end.getTime() - 3600 * 1000 * 24);
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
    this.dayDate = [start, end];

    const m_end = new Date();
    const m_start = new Date();
    m_start.setMonth(m_start.getMonth() - 6);
    this.dayDateMonth = [m_start, m_end];

    setScale();
    window.onresize = () => {
      setScale();
    };
    this.getDaySum();
    this.getMonthSum();
  },

  methods: {
    calculateWidth(value, total) {
      if (total === 0) return 0;
      return ((value / total) * 100).toFixed(2);
    },
    //切换图表列表
    titleChange() {
      this.isShow = !this.isShow;
    },
    MobileChange() {
      this.getDaySum();
      this.getMonthSum();
    },
    downClick(name) {
      let that = this;
      that.$refs[name].focus();
    },
    getDaySum() {
      let that = this;
      this.queryParams.params = {};
      if (this.dayDate.length > 0) {
        this.queryParams.startDate = this.parseTime(
          this.dayDate[0],
          "{y}-{m}-{d}"
        );
        this.queryParams.endDate = this.parseTime(
          this.dayDate[1],
          "{y}-{m}-{d}"
        );
      }
      datesum(this.queryParams).then((res) => {
        if (res.code == 200) {
          that.dayDateList = res.data.reverse();
          that.echartMapYData = [];
          that.echartNumYData = [];
          that.echartMapXData = [];
          that.dayDateList.forEach((item) => {
            that.echartMapYData.push(item.personOrder);
            that.echartNumYData.push(item.personNumber);
            that.echartMapXData.push(item.dateStr);
          });
          that.$refs["LineBriskChartDays"].initChart(
            that.echartMapXData.slice().reverse(),
            that.echartMapYData.slice().reverse(),
            that.echartNumYData.slice().reverse()
          );
        }
      });
    },
    getMonthSum() {
      let that = this;
      this.queryParams.params = {};
      if (this.dayDateMonth.length > 0) {
        this.queryParams.startDate = this.parseTime(
          this.dayDateMonth[0],
          "{y}-{m}"
        );
        this.queryParams.endDate = this.parseTime(
          this.dayDateMonth[1],
          "{y}-{m}"
        );
      }
      monthsum(this.queryParams).then((res) => {
        if (res.code == 200) {
          that.dayDateMonthList = res.data.reverse();
          let echartMapYData = [];
          let echartNumYData = [];
          let echartMapXData = [];
          that.dayDateMonthList.forEach((item) => {
            echartMapYData.push(item.personOrder);
            echartNumYData.push(item.personNumber);
            echartMapXData.push(item.dateStr);
          });
          that.$refs["LineBriskChartMonth"].initChart(
            echartMapXData.slice().reverse(),
            echartMapYData.slice().reverse(),
            echartNumYData.slice().reverse()
          );
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 0;
  padding: 0;
  width: 100%;
  font-family: "思源黑体";
  background: #f0f2f5;
  min-height: calc(100vh - 84px);
  .app-conter-box {
    width: 1200px;
    margin: auto;
    overflow: hidden;
    .classftion-box {
      display: flex;
      width: 100%;
      .classftion-left,
      .classftion-right {
        width: 520px;
        height: 500px;
        border-radius: 10px;
        opacity: 1;
        background: #ffffff;
        box-shadow: 0 6px 10px 0 #0000001a;
      }

      .classftion-right-tltle {
        width: 870px;
        height: 88px;
        background: url(../../../assets/images/click/各行业使用详情.png)
          no-repeat center center;
        background-size: 100%;
        text-align: center;
        margin: 56px 0 36px 0;
      }

      .classftion-right-big-tltle {
        width: 100%;
        height: 60px;
        border-radius: 10px;
        opacity: 1;
        padding: 0 30px;
        box-sizing: border-box;
        background: #ffffff;
        display: flex;
        align-items: center;
        margin: 30px 0;
        font-size: 20px;
        .title-sub-text {
          flex: 1;
          display: flex;
          flex-direction: row-reverse;
        }
        // .title-change {
        //     float: right;
        //     margin-top: 30px;
        //     margin-right: 50px;
        //     height: 51px;
        //     width: 45px;
        //     background: url(../../../assets/images/click/brisk_change.png) no-repeat center center;
        //     background-size: 100%;
        //     cursor: pointer;
        // }
        // .title-select {
        //     float: right;
        //     margin-top: 35px;
        //     margin-right: 50px;
        //     right: 170px;
        //     width: 180px;
        .title-select {
          margin: 0 30px;
        }
        ::v-deep .el-input__inner {
          width: 180px;
          border: 1px solid #3384e9;
          // margin: 0 30px;
        }
      }
    }
  }
}

.classftion-right-small-tltle-sub-left2 {
  padding-left: 20px;
  position: relative;
  margin-left: 20px;
}
.classftion-right-small-tltle-sub-left2::after {
  content: "";
  position: absolute;
  top: 30%;
  left: 0;
  width: 10px;
  height: 10px;
  background: #bedbff;
  border-radius: 50%;
}
.classftion-right-small-tltle-sub-left3 {
  padding-left: 20px;
  position: relative;
  margin-left: 20px;
}
.classftion-right-small-tltle-sub-left3::after {
  content: "";
  position: absolute;
  top: 30%;
  left: 0;
  width: 10px;
  height: 10px;
  background: #3470ff;
  border-radius: 50%;
}
/* justify-content: space-between; */

.f-40 {
  font-size: 40px;
  font-family: MiSans-Heavy, MiSans;
  font-weight: 800;
  color: #1366bf;
}

.f-72 {
  width: 147px;

  font-size: 72px;
  font-family: Futura-粗体, Futura;
  font-weight: 800;
  color: #14be98;
  margin-left: 161px;
}

.item-num {
  margin-top: 71px;
  font-size: 72px;
  font-family: Futura-粗体, Futura;
  font-weight: 800;
  color: #14be98;
  width: 100%;
  text-align: center;
}

.item-name {
  width: 100%;
  text-align: center;
  font-size: 40px;
  font-family: MiSans-Heavy, MiSans;
  font-weight: 800;
  color: #1366bf;
}

.classftion-list2:nth-child(5n) {
  margin-right: 0 !important;
}
.brisk-box {
  width: 1200px;
  height: 700px;
  border-radius: 10px;
  opacity: 1;
  background: #ffffff;
  margin-top: 30px;
  padding: 30px;

  box-sizing: border-box;
}
.brisk-form {
  width: 100%;
  height: 300px;
}
.brisk-chart {
  float: left;
  width: 1150px;
  // height: 320px;
}

.chart-title {
  height: 38px;
  width: 100%;
  box-sizing: border-box;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  font-size: 20px;
  .title-text {
    float: left;
    margin-left: 10px;

    font-size: 20px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
  }
  .title-center {
    float: left;
    margin-left: 600px;

    width: 200px;
    .title-blue {
      float: left;
      width: 32px;
      height: 9px;
      margin-top: 17px;
      background: linear-gradient(
        180deg,
        rgba(19, 102, 191, 0.65) 0%,
        rgba(19, 102, 191, 0.12) 62%,
        rgba(19, 102, 191, 0) 100%
      );
      opacity: 1;
    }
    .title-grren {
      float: left;
      width: 32px;
      height: 9px;
      margin-left: 50px;
      margin-top: 17px;
      background: linear-gradient(
        180deg,
        rgba(49, 214, 169, 0.75) 0%,
        rgba(49, 214, 169, 0.24) 54%,
        rgba(49, 214, 169, 0) 100%
      );
      opacity: 1;
    }
    .center-text {
      float: left;
      margin-left: 5px;
      font-size: 16px;
      height: 38px;
      line-height: 38px;
      font-weight: 800;
      color: #1366bf;
      font-family: MiSans-Heavy, MiSans;
    }
  }

  .title-date {
    flex: 1;
    text-align: right;
    font-size: 18px;
  }
  .title-down {
    // float: right;
    // margin-top: 15px;
    height: 9px;
    width: 17px;
    background: url(../../../assets/images/click/brisk_down.png) no-repeat
      center center;
    background-size: 100%;
  }
}
.title-img {
  width: 20px;
  height: 26px;
  background: url(../../../assets/images/index/biaoti.png) no-repeat center
    center;
  background-size: 100%;
}
.title-main {
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #cad4e6;
  .title-img {
    width: 20px;
    height: 26px;
    background: url(../../../assets/images/index/biaoti.png) no-repeat center
      center;
    background-size: 100%;
  }
  .title-text {
    margin-left: 10px;
    font-size: 30px;
    height: 38px;
    line-height: 38px;
    font-size: 20px;
    font-weight: 800;
    color: #1366bf;
  }
  .title-date {
    margin-right: 5px;
    font-size: 18px;
    height: 38px;
    line-height: 38px;
    font-weight: 800;
    color: #1366bf;
    font-family: MiSans-Heavy, MiSans;
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
  .title-down {
    height: 9px;
    width: 17px;
    background: url(../../../assets/images/click/brisk_down.png) no-repeat
      center center;
    background-size: 100%;
  }
}

.head-line {
  float: left;
  height: 2px;
  width: 817px;
  background: url(../../../assets/images/click/brisk_line.png) no-repeat center
    center;
  background-size: 100%;
  margin-left: 30px;
  margin-top: 20px;
}
.table-content {
  width: 100%;
  height: 390px;
  overflow-y: auto;
  > div {
    display: flex;
    height: 50px;
    align-items: center;
    justify-content: space-between;
    font-size: 15px !important;
    margin: 0 30px;
  }
}
.custom-progress {
  position: relative;
  width: 80%;
  height: 22px;
  background-color: #f0f0f0;
  border-radius: 22px;
  overflow: hidden;
  display: flex;
}

.progress-bar {
  height: 100%;
  text-align: center;
  line-height: 22px;
  color: white;
  font-weight: bold;
  position: absolute;
  top: 0;
  left: 0;
}

::v-deep .el-input__inner {
  border: 0;
}

::v-deep .el-range-editor--medium .el-range__icon,
.el-range-editor--medium .el-range__close-icon {
  display: none;
}

::v-deep .el-date-editor .el-range-separator {
  padding: 0;
}

::v-deep .el-date-editor .el-range__close-icon {
  display: none;
}
::v-deep .el-date-editor .el-range-input {
  width: 48%;
  font-size: 18px;
  height: 38px;
  line-height: 38px;
  font-weight: 800;
  color: #1366bf;
  font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-range-separator {
  font-size: 18px;
  height: 38px;
  line-height: 38px;
  font-weight: 800;
  color: #1366bf;
  font-family: MiSans-Heavy, MiSans;
}

::v-deep .el-date-editor > input:-moz-placeholder {
  color: #1366bf;
}

::v-deep .el-date-editor > input:-ms-input-placeholder {
  color: #1366bf;
}

::v-deep .el-date-editor > input::-webkit-input-placeholder {
  color: #1366bf;
}

input:-moz-placeholder {
  color: red;
}

input:-ms-input-placeholder {
  color: red;
}

input::-webkit-input-placeholder {
  color: red;
}
/*修改滚动条样式*/
div::-webkit-scrollbar {
  width: 6px;
  height: 10px;
  /**/
}
div::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}
div::-webkit-scrollbar-thumb {
  background: #2269de;
  border-radius: 10px;
}
div::-webkit-scrollbar-thumb:hover {
  background: #2269de;
}
div::-webkit-scrollbar-corner {
  background: #2269de;
}
.classftion-content {
  width: 1200px;
  display: flex;
  // height: 890px;
  border-radius: 10px;
  opacity: 1;
  padding: 30px 50px;
  box-sizing: border-box;
  background: #ffffff;
  justify-content: space-between;
}

.first-bar {
  border-top-left-radius: 22px;
  border-bottom-left-radius: 22px;
  text-align: right;
  color: #2269de;
}

.second-bar {
  border-top-right-radius: 22px;
  border-bottom-right-radius: 22px;
  // text-align: left;
}
</style>
