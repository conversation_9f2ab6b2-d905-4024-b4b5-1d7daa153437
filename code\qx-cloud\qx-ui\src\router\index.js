import Vue from 'vue'
import Router from 'vue-router'
import store from "@/store";
Vue.use(Router)
import { getToken, removeToken } from '@/utils/auth'
/* Layout */
import Layout from '@/layout'
import ParentView from '@/components/ParentView';

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: (resolve) => require(['@/views/redirect'], resolve)
      }
    ]
  },
  // {
  //   path: '/',
  //   component: (resolve) => require(['@/views/index'], resolve),
  // },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/loginto',
    component: (resolve) => require(['@/views/loginto'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
  // {
  //   path: '',
  //   component: Layout,
  //   redirect: 'redirect',
  //   children: [
  //     {
  //       path: 'index',
  //       component: (resolve) => require(['@/views/index'], resolve),
  //       name: '首页',
  //       meta: { title: '首页', icon: 'dashboard', noCache: true, affix: true }
  //     }
  //   ]
  // },
  {
    path: '/develop/detail',
    component: Layout,
    hidden: true,
    permissions: ['base:develop:edit'],
    children: [
      {
        path: 'index',
        component: () => import('@/views/system/hzhb/develop/detail'),
        name: 'developDetail',
        meta: { title: '行业生态链编辑', activeMenu: '/develop' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'type/data/:dictId(\\d+)',
        component: (resolve) => require(['@/views/system/dict/data'], resolve),
        name: 'Data',
        meta: { title: '字典数据', icon: '' }
      }
    ]
  },
  {
    path: '/developmentDetail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'development/data/:developmentId(\\d+)',
        component: (resolve) => require(['@/views/system/development/detail'], resolve),
        name: 'developmentDetail',
        meta: { title: '定制开发详情', icon: '' }
      }
    ]
  },
  {
    path: '/indexDetail',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'usersupport',
        component: (resolve) => require(['@/views/system/click/user-support'], resolve),
        name: 'userBrisk',
        meta: { title: '用户点赞', icon: '' }
      },
      {
        path: 'twocasemain',
        component: (resolve) => require(['@/views/system/click/two-casemain'], resolve),
        name: 'twoCasemain',
        meta: { title: '落地案例', icon: '' }
      },
      {
        path: 'user-brisk',
        component: (resolve) => require(['@/views/system/click/user-brisk'], resolve),
        name: 'userBrisk',
        meta: { title: '活跃人数', icon: '' }
      },
      {
        path: 'user-data',
        component: (resolve) => require(['@/views/system/click/user-data'], resolve),
        name: 'userData',
        meta: { title: '用户数据', icon: '' }
      },
      {
        path: 'user-region',
        component: (resolve) => require(['@/views/system/click/user-region'], resolve),
        name: 'userRegion',
        meta: { title: '用户地区', icon: '' }
      },
      {
        path: 'functional-classification',
        component: (resolve) => require(['@/views/system/click/functional-classification'], resolve),
        name: 'functionalClassification',
        meta: { title: '功能分类', icon: '' }
      },
      {
        path: 'partner-classification',
        component: (resolve) => require(['@/views/system/click/partner-classification'], resolve),
        name: 'partnerClassification',
        meta: { title: '合作伙伴分类', icon: '' }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'log',
        component: (resolve) => require(['@/views/monitor/job/log'], resolve),
        name: 'JobLog',
        meta: { title: '调度日志' }
      }
    ]
  },
  {
    path: '/gen',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'edit/:tableId(\\d+)',
        component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
        name: 'GenEdit',
        meta: { title: '修改生成配置' }
      }
    ]
  }
]

const router = new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  //base: '/ishow/',
  base: '/ishowa/',
  routes: constantRoutes
})


router.beforeEach((to, from, next) => {
  let defaultpath = localStorage.getItem('defaultpath');
  console.log(`Navigating from ${from.path} to ${to.path}, defaultpath: ${defaultpath}`);
  //用户删除token
  if (to.path === '/login') {
    removeToken()
  }

  if (defaultpath && defaultpath != "null" && defaultpath != "index") {
    if (to.path === '/index' || to.path === '/') {
      // 如果要跳转到 /index 或 / 页面，检查用户权限后重定向
      next(defaultpath); // 重定向到默认路径
    } else {
      next(); // 继续导航
    }
  } else {
    next(); // 没有默认路径时，继续导航
  }

});

export default router;