<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业场景" prop="sceneId">
        <el-cascader
          v-model="queryParams.sceneIds" size="small" placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" clearable @change="queryParamsHandleChange" @clear="handleClear">
        </el-cascader>
      </el-form-item>
      <el-form-item label="服务类别" prop="serviceType">
        <el-select v-model="queryParams.serviceType" placeholder="请选择服务类别" clearable size="small">
          <el-option label="硬件类别" value="0" />
          <el-option label="软件类别" value="1" />
          <el-option label="集成服务" value="2" />
          <el-option label="其它服务" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:default:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:default:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:default:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:default:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="defaultList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行业" align="center" prop="industryName" />
      <el-table-column label="场景" align="center" prop="sceneName" />
      <el-table-column label="服务类别" align="center" prop="serviceType">
        <template slot-scope="scope">
          <span>{{scope.row.serviceType==0?'硬件类别':scope.row.serviceType==1?'软件类别':scope.row.serviceType==2?'集成服务':'3其它服务'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务名称" align="center" prop="serviceName">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.serviceName"
            raw-content
            placement="top-start"
            v-if="scope.row.serviceName"
          >
            <span v-if="scope.row.serviceName && scope.row.serviceName.length <= 30">
               {{ scope.row.serviceName }}
          </span>
            <span v-if="scope.row.serviceName && scope.row.serviceName.length > 30">
               {{ scope.row.serviceName.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.serviceName== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="smallType">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.smallType"
            raw-content
            placement="top-start"
            v-if="scope.row.smallType"
          >
            <span v-if="scope.row.smallType && scope.row.smallType.length <= 30">
               {{ scope.row.smallType }}
          </span>
            <span v-if="scope.row.smallType && scope.row.smallType.length > 30">
               {{ scope.row.smallType.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.smallType== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="服务说明" align="center" prop="serviceExplain">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.serviceExplain"
            raw-content
            placement="top-start"
            v-if="scope.row.serviceExplain"
          >
            <span v-if="scope.row.serviceExplain && scope.row.serviceExplain.length <= 30">
               {{ scope.row.serviceExplain }}
          </span>
            <span v-if="scope.row.serviceExplain && scope.row.serviceExplain.length > 30">
               {{ scope.row.serviceExplain.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.serviceExplain== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="服务价格" align="center" prop="servicePrice" />
      <el-table-column label="服务单位" align="center" prop="serviceUnit" />
      <el-table-column label="数量" align="center" prop="serviceNumber" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:default:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:default:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改场景默认项对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="场景" prop="sceneId">
          <el-cascader v-model="form.sceneIds" :options="options" clearable @change="handleChange"></el-cascader>
        </el-form-item>
        <el-form-item label="服务类别" prop="serviceType">
          <el-select v-model="form.serviceType" placeholder="请选择服务类别" clearable size="small">
            <el-option label="硬件类别" :value="0" />
            <el-option label="软件类别" :value="1" />
            <el-option label="集成服务" :value="2" />
            <el-option label="其它服务" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务名称" prop="serviceName">
          <el-input v-model="form.serviceName" maxlength="100" show-word-limit placeholder="请输入服务名称" />
        </el-form-item>
        <el-form-item label="小分类" prop="smallType">
          <el-input v-model="form.smallType" maxlength="50" show-word-limit placeholder="请输入小分类名称" />
        </el-form-item>
        <el-form-item label="服务说明" prop="serviceExplain">
          <el-input v-model="form.serviceExplain" maxlength="500" show-word-limit type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="服务价格" prop="servicePrice">
          <el-input v-model="form.servicePrice" maxlength="6" show-word-limit placeholder="请输入服务价格"  @input="onInput2" />
        </el-form-item>
        <el-form-item label="服务单位" prop="serviceUnit">
          <el-input v-model="form.serviceUnit" maxlength="5" show-word-limit placeholder="请输入服务单位" />
        </el-form-item>
        <el-form-item label="数量" prop="serviceNumber">
          <el-input v-model="form.serviceNumber" maxlength="4" show-word-limit placeholder="请输入数量" @input="onInput" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDefault, getDefault, delDefault, addDefault, updateDefault, exportDefault } from "@/api/system/default";
import { listIndustry } from "@/api/system/industry";
import { listScene } from "@/api/system/scene";
import {listTypeV2} from "@/api/system/dict/type";
export default {
  name: "Default",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 场景默认项表格数据
      defaultList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceType: null,
        serviceName: null,
        serviceExplain: null,
        servicePrice: null,
        serviceUnit: null,
        serviceNumber: null,
        sceneId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sceneId: [
          { required: true, message: "所属行业场景不能为空", trigger: "blur" }
        ],
        serviceType: [
          { required: true, message: "服务类别不能为空", trigger: "blur" }
        ],
        serviceName: [
          { required: true, message: "服务名称不能为空", trigger: "blur" }
        ],
      },
      //行业列表
      industryList: [],
      //场景列表
      sceneList: [],
      options: [],
      selectItem: {},//选中的item
      number: false,
    };
  },
  created() {
    this.getList();
    listIndustry().then(response => {
      this.industryList = response.rows;
      listScene().then(response => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });

    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  },
  beforeDestroy() {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
  },
  methods: {
    handleClear() {
      // 清除场景时执行的操作
      this.queryParams.sceneIds = []; // 清空选中的场景
    },
    handleVisibilityChange(){
      if (document.visibilityState === 'visible') {
        console.log("123445")
      }
    },
    // fetchTypeOptions() {
    //   if ( this.number ){
    //     this.options = []
    //     listIndustry().then(response => {
    //       this.industryList = response.rows;
    //       listScene().then(response => {
    //         this.sceneList = response.rows;
    //         this.setOption();
    //       });
    //     });
    //   }
    // },
    onInput(value) {
      this.form.serviceNumber = value.replace(/[^0-9]/g, '');
      if (parseInt(value) > 9999) {
        this.form.serviceNumber = '9999';
      } else {
        this.form.serviceNumber = value;
      }
    },
    onInput2(value) {
      this.form.servicePrice = value.replace(/[^0-9]/g, '');
      if (parseFloat(value) > 999999) {
        this.form.servicePrice = '999999';
      } else {
        this.form.servicePrice = value;
      }
    },
    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.queryParams.industryId = value[0];
      };

      if (value != null || value.length > 1) {
        this.queryParams.sceneId = value[1];
      };
    },
    //选中
    handleChange(value) {
      if (!value || value.length === 0) {
        this.form.sceneId = null; // 清空场景值
      }
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach(row => {
        if (row.id === value[1]) {
          this.selectItem = row;
        }
      });
    },
    //设置行业场景下拉
    setOption() {
      console.log(this.number)

      this.industryList.forEach(element => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: []
        }
        this.sceneList.forEach(row => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName
            });
          }
        });
        this.options.push(item);

      });
      console.log(this.options.length)
    },
    /** 查询场景默认项列表 */
    getList() {
      this.loading = true;
      listDefault(this.queryParams).then(response => {
        this.defaultList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        serviceType: null,
        serviceName: '',
        serviceExplain: '',
        servicePrice: null,
        serviceUnit: null,
        smallType: null,
        serviceNumber: null,
        sceneId: null,
        sceneIds: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.industryId = null;
      this.queryParams.sceneIds = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      // this.form.serviceType = 0;
      // this.form.sceneIds = [5795766694805504, 5795791426027520];
      // this.form.serviceUnit = '项';
      // this.form.sceneId = 5795791426027520;
      this.title = "添加场景默认项";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let that = this;
      this.reset();
      const id = row.id || this.ids
      getDefault(id).then(response => {
        this.form = response.data;
        that.sceneList.forEach(row => {
          if (row.id === that.form.sceneId) {
            that.selectItem = row;
            that.form.sceneIds = [row.industryId, row.id];
            console.log(that.form.sceneIds);
          }
        });
        this.open = true;
        this.title = "修改场景默认项";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDefault(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDefault(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.defaultList
        .filter(item => ids.includes(item.id))
        .map(item => item.serviceName)
        .join(', ');

      this.$confirm('是否确认删除场景默认项服务名称为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delDefault(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的场景默认项数据';
      }else {
        msg = '是否确认导出所有的场景默认项数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/default/export', exportParams, `default_${new Date().getTime()}.xlsx`)
      }).catch({});
    }



  }
};
</script>
