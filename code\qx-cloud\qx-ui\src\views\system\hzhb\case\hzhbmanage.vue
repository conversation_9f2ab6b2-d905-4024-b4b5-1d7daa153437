<template>
  <div class="app-container" style="padding:20px;">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px" style="text-align:left;">
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号码" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="账号" prop="account">
        <el-input v-model="queryParams.account" placeholder="请输入账号" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="queryParams.nickname" placeholder="请输入昵称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
     <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="手机号码" align="center" prop="phone" />
      <el-table-column label="账号" align="center" prop="account" />
      <el-table-column label="昵称" align="center" prop="nickname" />
      <el-table-column label="电子邮箱" align="center" prop="email" />
      <el-table-column label="企业图片" align="center" prop="icon">
        <template slot-scope="scope">
          <img style="width:50px;height:50px;border-radius: 50%;" :src="scope.row.icon" />
        </template>
      </el-table-column>
      <el-table-column label="供应商类型" align="center" prop="supplierGrade">
        <template slot-scope="scope">
          <span>{{scope.row.supplierGrade==0?'普通供应商':'一级供应商'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" key="accountState">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.accountState" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleUpdatePwd(scope.row)">重置密码</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="form.id==null?rules:rules2" label-width="100px">
        <el-form-item label="账号" prop="account">
          <el-input v-model="form.account" v-bind:disabled="form.id!=null" placeholder="请输入账号邮箱地址" maxlength="50" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-show="form.id==null">
          <el-input v-model="form.password" placeholder="请输入密码" type="password" maxlength="50" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="form.nickname" placeholder="请输入昵称" maxlength="50" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码" maxlength="11" />
        </el-form-item>
        <!-- <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱" maxlength="100" />
        </el-form-item> -->
        <el-form-item label="企业图片" prop="icon">
          <ImageUpload @input="setInput" :value="form.icon" />
        </el-form-item>
        <!-- <el-form-item label="供应商类型" prop="icon">
          <el-radio-group v-model="form.supplierGrade">
            <el-radio :label="0">普通供应商</el-radio>
            <el-radio :label="1">一级供应商</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="openSetPwd" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="pwdform" :model="pwdform" :rules="pwdrules" label-width="80px">
        <el-form-item label="密码" prop="password">
          <el-input v-model="pwdform.password" type="password" placeholder="请输入密码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPwdForm">确 定</el-button>
        <el-button @click="cancelPwd">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import ImageUpload from "../../../../components/ImageUpload";
import { add, update, deleteTo, detail, list } from "@/api/hzhb/caseuser"
import md5 from 'md5';
export default {
  name: "hzhbmanage",
  components: {
    ImageUpload
  },
  data() {
    return {
      openSetPwd: false,
      pwdform: {},
      // 表单校验
      pwdrules: {
        password: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,50}/,
            message: "密码为数字字母，且长度为8至50位",
            trigger: "blur"
          }
        ]
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        phone: null,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        email: null,
        icon: null,
        isExamine: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        account: [
          { required: true, message: "用户账号不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,50}/,
            message: "用户账号为数字字母，且长度为8至50位",
            trigger: "blur"
          }, {
            type: "email",
            message: "'用户账号请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        password: [
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,50}/,
            message: "密码为数字字母，且长度为8至50位",
            trigger: "blur"
          }
        ],
        nickname: [
          { required: true, message: "用户名称不能为空", trigger: "blur" }
        ],
        // email: [
        //   { required: true, message: "邮箱地址不能为空", trigger: "blur" },
        //   {
        //     type: "email",
        //     message: "请输入正确的邮箱地址",
        //     trigger: ["blur", "change"]
        //   }
        // ],
        phonenumber: [
          { required: true, message: "用户手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      },
      rules2: {
        account: [
          { required: true, message: "用户账号不能为空", trigger: "blur" },
          {
            pattern: /(?=.*\d)(?=.*[a-zA-Z]).{8,50}/,
            message: "用户账号为数字字母，且长度为8至50位",
            trigger: "blur"
          }
        ],
        nickname: [
          { required: true, message: "用户名称不能为空", trigger: "blur" }
        ],
        email: [
          { required: true, message: "邮箱地址不能为空", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        phonenumber: [
          { required: true, message: "用户手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getList();

  },
  methods: {
    /** 修改按钮操作 */
    handleUpdatePwd(row) {
      const id = row.id || this.ids;
      this.openSetPwd = true;
      this.pwdform = {
        id: id,
        password: null
      }
      this.resetForm("pwdform");
    },
    cancelPwd() {
      this.openSetPwd = false;
      this.pwdform = {
        id: null,
        password: null
      }
      this.resetForm("pwdform");
    },
    /** 提交按钮 */
    submitPwdForm() {
      this.$refs["pwdform"].validate(valid => {
        if (valid) {
          if (this.pwdform.id != null) {
            this.pwdform.password=md5(this.pwdform.password)
            update(this.pwdform).then(response => {
              this.msgSuccess("修改密码成功");
              this.openSetPwd = false;
            });
          }
        }
      });
    },
    setInput(value) {
      this.$set(this.form, "icon", value);
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      list(this.queryParams).then(response => {
        this.userList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        phone: null,
        account: null,
        password: null,
        nickname: null,
        accountState: null,
        email: null,
        icon: null,
        createTime: null
      };
      this.resetForm("form");
    },// 用户状态修改
    handleStatusChange(row) {
      let that = this;
      let text = row.accountState === "0" ? "启用" : "停用";
      this.$confirm('确认要"' + text + '""' + row.account + '"用户吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.$caseapi.userapi.updateUser({
          id: row.id,
          accountState: row.accountState
        }).then(response => {
          that.msgSuccess(text + "成功");
        }).catch(function () {
          row.accountState = row.accountState === "0" ? "1" : "0";
        });
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      detail(id).then(response => {
        this.form = response.data.data;
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            this.form.password = null;

            update(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.password = md5(this.form.password);
            add(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let that = this;
      const ids = row.id == null ? this.ids : [row.id];
      const names = this.userList
        .filter(item => ids.includes(item.id))
        .map(item => item.nickname)
        .join(', ');
      this.$confirm('是否确认删除用户为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return deleteTo(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
  }
};
</script>
<style lang="scss" scoped>
.mb8 {
  margin-bottom: 8px;
}

::v-deep .el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 40px;
      font-size: 13px;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*='el-icon-'] + span {
      margin-left: 1px;
    }
  }
}
</style>
