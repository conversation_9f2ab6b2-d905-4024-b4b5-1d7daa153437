import request from '@/utils/request'

// 查询行业列表
export function listIndustry(query) {
  return request({
    url: '/system/industry/list',
    method: 'get',
    params: query
  })
}

export function listIndustryV2(query) {
  return request({
    url: '/system/industry/list/v2',
    method: 'get',
    params: query
  })
}

// 查询行业详细
export function getIndustry(id) {
  return request({
    url: '/system/industry/' + id,
    method: 'get'
  })
}

// 新增行业
export function addIndustry(data) {
  return request({
    url: '/system/industry',
    method: 'post',
    data: data
  })
}

// 修改行业
export function updateIndustry(data) {
  return request({
    url: '/system/industry',
    method: 'put',
    data: data
  })
}

// 修改行业user
export function updateUserIndustry(data) {
  return request({
    url: '/system/industry',
    method: 'put',
    data: data
  })
}

// 删除行业
export function delIndustry(id) {
  return request({
    url: '/system/industry/' + id,
    method: 'delete'
  })
}

// 导出行业
export function exportIndustry(query) {
  return request({
    url: '/system/industry/export',
    method: 'get',
    params: query
  })
}
