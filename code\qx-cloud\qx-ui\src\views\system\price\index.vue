<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
<!--       <el-form-item label="行业场景" prop="sceneId">-->
<!--        <el-cascader v-model="queryParams.sceneIds" size="small"  placeholder="请选择行业场景" :props="{ checkStrictly: true }" :options="options" @change="queryParamsHandleChange"></el-cascader>-->
<!--         </el-form-item> &ndash;&gt;-->

      <el-form-item label="行业" prop="industryCode">
        <el-select
          v-model="queryParams.industryCode"
          placeholder="请选择行业"
          clearable
          @clear="handleIndustryClear">
          <el-option
            v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="类型key" prop="typeKey">
        <el-input v-model="queryParams.typeKey" placeholder="请输入类型key" clearable size="small" @keyup.enter.native="handleQuery" @input="handleKeyPress"/>
      </el-form-item>
      <el-form-item label="类型名称" prop="typeName">
        <el-input v-model="queryParams.typeName" placeholder="请输入类型名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="价格key" prop="priceKey">
        <el-input v-model="queryParams.priceKey" placeholder="请输入价格key" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:price:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:price:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:price:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:price:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="priceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行业名称" align="center" prop="industryName" />
<!--      <el-table-column label="场景名称" align="center" prop="sceneName" />-->
      <el-table-column label="类型key" align="center" prop="typeKey" />
      <el-table-column label="类型名称" align="center" prop="typeName">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.typeName"
            raw-content
            placement="top-start"
            v-if="scope.row.typeName"
          >
            <span v-if="scope.row.typeName && scope.row.typeName.length <= 30">
               {{ scope.row.typeName }}
          </span>
            <span v-if="scope.row.typeName && scope.row.typeName.length > 30">
               {{ scope.row.typeName.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.typeName== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="价格key" align="center" prop="priceKey" />
      <el-table-column label="价格" align="center" prop="priceValue">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.priceValue"
            raw-content
            placement="top-start"
            v-if="scope.row.priceValue"
          >
            <span v-if="scope.row.priceValue && scope.row.priceValue.length <= 30">
               {{ scope.row.priceValue }}
          </span>
            <span v-if="scope.row.priceValue && scope.row.priceValue.length > 30">
               {{ scope.row.priceValue.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.priceValue== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="name">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.name"
            raw-content
            placement="top-start"
            v-if="scope.row.name"
          >
            <span v-if="scope.row.name && scope.row.name.length <= 30">
               {{ scope.row.name }}
          </span>
            <span v-if="scope.row.name && scope.row.name.length > 30">
               {{ scope.row.name.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.name== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark">
        <template #default="scope">
          <el-tooltip
            :content="scope.row.remark"
            raw-content
            placement="top-start"
            v-if="scope.row.remark"
          >
            <span v-if="scope.row.remark && scope.row.remark.length <= 30">
               {{ scope.row.remark }}
          </span>
            <span v-if="scope.row.remark && scope.row.remark.length > 30">
               {{ scope.row.remark.substr(0, 30) + "..." }}
          </span>
          </el-tooltip>
          <span v-else-if="scope.row.remark== null"> </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:price:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:price:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改网络报价项对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="行业名称" prop="industryCode">
          <el-select v-model="form.industryCode" placeholder="请选择行业">
            <el-option v-for="item in industryList" :key="item.industryCode" :label="item.industryName" :value="item.industryCode"></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="场景" prop="sceneId">-->
<!--          <el-cascader v-model="form.sceneIds" :options="options" @change="handleChange"></el-cascader>-->
<!--        </el-form-item>-->
        <el-form-item label="类型key" prop="typeKey">
          <el-input v-model="form.typeKey" placeholder="请输入类型key"  maxlength="50" show-word-limit @input="handleTypeKeyPress"/>
        </el-form-item>
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="form.typeName" placeholder="请输入类型名称"  maxlength="100" show-word-limit/>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" maxlength="200" show-word-limit/>
        </el-form-item>
        <el-form-item label="价格key" prop="priceKey">
          <el-input v-model="form.priceKey" placeholder="请输入价格key" maxlength="50" show-word-limit @input="handlePriceKeyPress"/>
        </el-form-item>
        <el-form-item label="价格" prop="priceValue">
          <el-input v-model="form.priceValue" type="textarea" :rows="5" placeholder="请输入价格" maxlength="4000" show-word-limit/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注" maxlength="300" show-word-limit/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPrice, getPrice, delPrice, addPrice, updatePrice, exportPrice } from "@/api/system/price";
import { listIndustry } from "@/api/system/industry";
import {listScene} from "@/api/system/scene";
export default {
  name: "Price",
  components: {
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 网络报价项表格数据
      priceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sceneId: null,
        typeKey: null,
        typeName: null,
        priceKey: null,
        priceValue: null,
        name: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industryCode: [
          { required: true, message: "行业不能为空 ", trigger: "change" }
        ],
        typeKey: [
          { required: true, message: "类型key不能为空", trigger: "blur" }
        ],
        typeName: [
          { required: true, message: "类型名称不能为空", trigger: "blur" }
        ],
      },
      //行业列表
      industryList: [],
      //场景列表
      sceneList: [],
      options: [],
      selectItem: {},//选中的item
    };
  },
  created() {
    this.getList();
    listIndustry().then(response => {
      this.industryList = response.rows;
      listScene().then(response => {
        this.sceneList = response.rows;
        this.setOption();
      });
    });
  },
  methods: {
    handleIndustryClear() {
      // 清除行业选择时执行的操作
      this.queryParams.industryId = null; // 重置行业ID
    },
    //选中
    queryParamsHandleChange(value) {
      if (value != null || value.length > 0) {
        this.queryParams.industryId = value[0];
      };

      if (value != null || value.length > 1) {
        this.queryParams.sceneId = value[1];
      };
    },
    //选中
    handleChange(value) {
      if (value == null || value.length < 2) return;
      this.form.sceneId = value[1];
      this.sceneList.forEach(row => {
        if (row.id === value[1]) {
          this.selectItem = row;
        }
      });
    }, //设置行业场景下拉
    setOption() {
      this.industryList.forEach(element => {
        let item = {
          value: element.id,
          label: element.industryName,
          children: []
        }
        this.sceneList.forEach(row => {
          if (row.industryId === element.id) {
            item.children.push({
              value: row.id,
              label: row.sceneName
            });
          }
        });
        this.options.push(item);
      });
    },
    /** 查询网络报价项列表 */
    getList() {
      this.loading = true;
      listPrice(this.queryParams).then(response => {
        this.priceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        sceneId: null,
        typeKey: null,
        typeName: null,
        priceKey: null,
        priceValue: null,
        name: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        sceneIds: null,
        industryCode: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.sceneIds = [];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加网络报价项";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      let that = this;
      this.reset();
      const id = row.id || this.ids
      getPrice(id).then(response => {
        this.form = response.data;
        this.open = true;
        that.sceneList.forEach(row => {
          if (row.id === that.form.sceneId) {
            that.selectItem = row;
            that.form.sceneIds = [row.industryId, row.id];
            console.log(that.form.sceneIds);
          }
        });
        this.title = "修改网络报价项";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updatePrice(this.form).then(response => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPrice(this.form).then(response => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id == null ? this.ids : [row.id];
      console.log(ids);
      console.log(this.priceList);
      const names = this.priceList
        .filter(item => ids.includes(item.id))
        .map(item => item.priceKey)
        .join(', ');

      this.$confirm('是否确认删除网络报价项编号为"' + names + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return delPrice(ids);
      }).then(() => {
        this.getList();
        this.msgSuccess("删除成功");
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      let that = this;
      const exportParams = {
        ...that.queryParams,
        exportIdList: that.ids.length ? that.ids : null // Add ids if any are selected
      };
      let msg = '';
      if ( that.ids.length > 0 ){
        msg = '是否确认导出所筛选或选中的网络报价项数据';
      }else {
        msg = '是否确认导出所有的网络报价项数据';
      }
      this.$confirm(msg, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        that.download('system/price/export', exportParams, `price_${new Date().getTime()}.xlsx`)
      }).catch({});
    },
    handleKeyPress(event) {
      this.queryParams.typeKey = this.queryParams.typeKey.replace(/[^a-zA-Z0-9]/g, '');
    },
    handleTypeKeyPress(event) {
      this.form.typeKey = this.form.typeKey.replace(/[^a-zA-Z0-9]/g, '');
    },
    handlePriceKeyPress(event) {
      this.form.priceKey = this.form.priceKey.replace(/[^a-zA-Z0-9]/g, '');
    },
    // handlePriceValueKeyPress(event) {
    //   this.form.priceValue = this.form.priceValue.replace(/[^a-zA-Z0-9]/g, '');
    // }
  }
};
</script>
